import { z } from "zod";
import { Octokit, RestEndpointMethodTypes } from "@octokit/rest";

const RepositorySchema = z.object({
  owner: z.string().min(1),
  repo: z.string().min(1),
});

const PullRequestStateSchema = z.enum(["open", "closed", "all"]);

const PullRequestSortSchema = z.enum([
  "created",
  "updated",
  "popularity",
  "long-running",
]);

const PullRequestDirectionSchema = z.enum(["asc", "desc"]);

export interface ListPullRequestsOptions {
  state?: z.infer<typeof PullRequestStateSchema>;
  head?: string;
  base?: string;
  sort?: z.infer<typeof PullRequestSortSchema>;
  direction?: z.infer<typeof PullRequestDirectionSchema>;
  perPage?: number;
  page?: number;
}

// Use the actual GitHub API types
export type PullRequest =
  RestEndpointMethodTypes["pulls"]["list"]["response"]["data"][0];
export type PullRequestDetail =
  RestEndpointMethodTypes["pulls"]["get"]["response"]["data"];

/**
 * GitHubApiClient class for managing GitHub API operations
 */
export class GitHubApiClient {
  private _octokit: Octokit | null = null;
  private _owner: string | null = null;
  private _repo: string | null = null;

  /**
   * Initializes the GitHub API client
   * @param token - GitHub personal access token
   * @param owner - Repository owner (optional, can be set later)
   * @param repo - Repository name (optional, can be set later)
   */
  constructor(token?: string, owner?: string, repo?: string) {
    if (token) {
      this._octokit = new Octokit({
        auth: token,
      });
    } else {
      // Try to get token from environment
      const envToken = process.env.GITHUB_TOKEN;
      if (envToken) {
        this._octokit = new Octokit({
          auth: envToken,
        });
      } else {
        throw new Error(
          "GitHub token not provided. Set GITHUB_TOKEN environment variable or pass token to constructor."
        );
      }
    }

    this._owner = owner || null;
    this._repo = repo || null;
  }

  /**
   * Sets the repository context
   * @param owner - Repository owner
   * @param repo - Repository name
   */
  public setRepository(owner: string, repo: string): void {
    this._owner = owner;
    this._repo = repo;
  }

  /**
   * Gets the Octokit instance
   */
  private get octokit(): Octokit {
    if (!this._octokit) {
      throw new Error(
        "GitHubApiClient not initialized. Provide a token in constructor or set GITHUB_TOKEN environment variable."
      );
    }
    return this._octokit;
  }

  /**
   * Gets the current repository owner
   */
  get owner(): string {
    if (!this._owner) {
      throw new Error(
        "Repository owner not set. Call setRepository() or provide owner in constructor."
      );
    }
    return this._owner;
  }

  /**
   * Gets the current repository name
   */
  get repo(): string {
    if (!this._repo) {
      throw new Error(
        "Repository name not set. Call setRepository() or provide repo in constructor."
      );
    }
    return this._repo;
  }

  /**
   * Retrieves all pull requests with pagination
   * @param options - Options for filtering and pagination
   * @param owner - Repository owner (optional, uses instance default if not provided)
   * @param repo - Repository name (optional, uses instance default if not provided)
   */
  async listAllPullRequests(
    options: ListPullRequestsOptions = {},
    owner?: string,
    repo?: string
  ): Promise<PullRequest[]> {
    const currentOwner = owner || this.owner;
    const currentRepo = repo || this.repo;

    // Validate repository parameters
    RepositorySchema.parse({ owner: currentOwner, repo: currentRepo });

    // Validate options
    const validatedOptions = {
      state: options.state
        ? PullRequestStateSchema.parse(options.state)
        : "all",
      head: options.head,
      base: options.base,
      sort: options.sort
        ? PullRequestSortSchema.parse(options.sort)
        : "created",
      direction: options.direction
        ? PullRequestDirectionSchema.parse(options.direction)
        : ("desc" as const),
      per_page: options.perPage || 100, // Maximum page size
    };

    // Fetch all pull requests with pagination
    const allPullRequests: PullRequest[] = [];
    let page = options.page || 1;

    do {
      const res = await this.octokit.rest.pulls.list({
        owner: currentOwner,
        repo: currentRepo,
        state: validatedOptions.state,
        head: validatedOptions.head,
        base: validatedOptions.base,
        sort: validatedOptions.sort,
        direction: validatedOptions.direction,
        per_page: validatedOptions.per_page,
        page: page,
      });

      const pullRequests = res.data;
      allPullRequests.push(...pullRequests);

      // If we got fewer results than requested, we've reached the end
      if (pullRequests.length < validatedOptions.per_page) {
        break;
      }

      page++;

      // Safety check to prevent infinite loops
      if (allPullRequests.length > 10000) {
        console.warn(
          "Warning: Found more than 10000 pull requests, stopping pagination"
        );
        break;
      }
    } while (allPullRequests.length > 0);

    return allPullRequests;
  }

  /**
   * Retrieves a specific pull request by number
   * @param pullNumber - Pull request number
   * @param owner - Repository owner (optional, uses instance default if not provided)
   * @param repo - Repository name (optional, uses instance default if not provided)
   */
  async getPullRequest(
    pullNumber: number,
    owner?: string,
    repo?: string
  ): Promise<PullRequestDetail> {
    const currentOwner = owner || this.owner;
    const currentRepo = repo || this.repo;

    // Validate repository parameters
    RepositorySchema.parse({ owner: currentOwner, repo: currentRepo });

    const res = await this.octokit.rest.pulls.get({
      owner: currentOwner,
      repo: currentRepo,
      pull_number: pullNumber,
    });

    return res.data;
  }

  /**
   * Retrieves pull requests with a specific state
   * @param state - Pull request state
   * @param options - Additional options
   * @param owner - Repository owner (optional, uses instance default if not provided)
   * @param repo - Repository name (optional, uses instance default if not provided)
   */
  async listPullRequestsByState(
    state: z.infer<typeof PullRequestStateSchema>,
    options: Omit<ListPullRequestsOptions, "state"> = {},
    owner?: string,
    repo?: string
  ): Promise<PullRequest[]> {
    return this.listAllPullRequests({ ...options, state }, owner, repo);
  }

  /**
   * Retrieves open pull requests
   * @param options - Additional options
   * @param owner - Repository owner (optional, uses instance default if not provided)
   * @param repo - Repository name (optional, uses instance default if not provided)
   */
  async listOpenPullRequests(
    options: Omit<ListPullRequestsOptions, "state"> = {},
    owner?: string,
    repo?: string
  ): Promise<PullRequest[]> {
    return this.listPullRequestsByState("open", options, owner, repo);
  }

  /**
   * Retrieves closed pull requests
   * @param options - Additional options
   * @param owner - Repository owner (optional, uses instance default if not provided)
   * @param repo - Repository name (optional, uses instance default if not provided)
   */
  async listClosedPullRequests(
    options: Omit<ListPullRequestsOptions, "state"> = {},
    owner?: string,
    repo?: string
  ): Promise<PullRequest[]> {
    return this.listPullRequestsByState("closed", options, owner, repo);
  }
}
