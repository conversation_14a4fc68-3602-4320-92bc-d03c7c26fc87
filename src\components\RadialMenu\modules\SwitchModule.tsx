import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { MathUtils, Vector2 } from "three";
import { useRadialItem } from "../useRadialItem";
import { BaseRadialItem, RadialItemType } from "./types";
import { cn } from "@/lib/utils";

export interface RadialSwitchItem extends BaseRadialItem {
  /** Type of the item */
  type: RadialItemType.Switch;

  /** Offset from the outer radius of the main menu */
  offset: number;

  /** Angle of the switch, e.g. arc angle */
  angle: number;

  /** Size of the switch */
  size: number;

  /** Current value of the switch */
  value: boolean;

  /** Callback function called when the switch value changes */
  onChange: (value: boolean) => void;
}

interface SwitchOptions {
  /** Offset from the outer radius of the main menu */
  startRadius: number;

  /** Start angle of the Switch in radians */
  startAngle: number;

  /** End angle of the Switch in radians */
  endAngle: number;
}

function generateSwitchPanel(opts: SwitchOptions) {
  const { startRadius, startAngle, endAngle } = opts;

  const angleDt = endAngle - startAngle;
  const handleRadius = startRadius;

  const p1 = new Vector2();
  const p2 = new Vector2();

  p1.set(
    handleRadius * Math.cos(startAngle),
    handleRadius * Math.sin(startAngle)
  );

  p2.set(handleRadius * Math.cos(endAngle), handleRadius * Math.sin(endAngle));

  const track = [
    `M ${p1.x} ${p1.y}`,
    `A ${startRadius} ${startRadius} ${angleDt} 0 1 ${p2.x} ${p2.y}`,
  ];

  const handle = [
    `M ${handleRadius} 0`,
    `A ${handleRadius} 0 360 0 1 ${handleRadius} 0`,
  ];

  return {
    track: track.join(" "),
    handle: handle.join(" "),
    handleOffset: handleRadius,
    handleStartAngle: startAngle * MathUtils.RAD2DEG,
    handleEndAngle: endAngle * MathUtils.RAD2DEG,
  };
}

export const RadialSwitchModule = ({ item }: { item: RadialSwitchItem }) => {
  const { angle, radius, events } = useRadialItem();
  const [isActive, setIsActive] = useState(item.value);

  const rads = item.angle * MathUtils.DEG2RAD;
  const startAngle = angle - rads / 2;
  const endAngle = angle + rads / 2;

  const size = (radius + item.offset + item.size / 2) * 2; // Size of the switch panel

  const { track, handle, handleStartAngle, handleEndAngle } =
    generateSwitchPanel({
      startRadius: radius + item.offset,
      startAngle: startAngle,
      endAngle: endAngle,
    });

  useEffect(() => {
    const onClick = () => {
      setIsActive((prev) => {
        const newValue = !prev;
        item.onChange(newValue);
        return newValue;
      });
    };

    events.on("click", onClick);

    return () => {
      events.off("click", onClick);
    };
  }, [events, item]);

  return (
    <motion.div className="absolute w-0 h-0 flex justify-center items-center">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
      >
        <svg
          width={size}
          height={size}
          viewBox={`${-size / 2} ${-size / 2} ${size} ${size}`}
          style={{ pointerEvents: "none", zIndex: 1, position: "relative" }}
        >
          <g
            onClick={() => {
              setIsActive(!isActive);
              item.onChange(!isActive);
            }}
            style={{ pointerEvents: "auto" }}
          >
            <path
              d={track}
              fill="none"
              strokeWidth={40}
              strokeLinecap="round"
              className={cn(
                "transition-colors duration-200 ease-in-out",
                isActive
                  ? "stroke-nilo-fill-primary hover:stroke-nilo-fill-primary-hover active:stroke-nilo-fill-primary-pressed"
                  : "stroke-nilo-fill-secondary hover:stroke-nilo-fill-secondary-hover active:stroke-nilo-fill-secondary-pressed"
              )}
            />
            <path
              d={handle}
              fill="#none"
              // stroke="#212121"
              strokeWidth={32}
              strokeLinecap="round"
              className="stroke-nilo-fill-tertiary transition-transform duration-200 ease-in-out pointer-events-none"
              style={{
                rotate: isActive
                  ? `${handleEndAngle}deg`
                  : `${handleStartAngle}deg`,
              }}
            />
          </g>
        </svg>
      </motion.div>
    </motion.div>
  );
};

export const buildSwitchModule = (
  payload: Omit<RadialSwitchItem, "type">
): RadialSwitchItem => {
  return {
    type: RadialItemType.Switch,
    ...payload,
  };
};
