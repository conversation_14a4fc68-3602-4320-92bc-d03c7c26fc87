import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import {
  RunFixtureRequest,
  RunFixtureResponse,
  Range,
} from "@nilo/firebase-schema";
import { bindFunction } from "@/config/firebase";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Typography } from "@/components/ui-nilo/Typography";

const runFixture = bindFunction<RunFixtureRequest, RunFixtureResponse>(
  "runFixture"
);

const FIXTURE_OPTIONS: {
  value: RunFixtureRequest["fixture"];
  label: string;
  description: string;
}[] = [
  {
    value: "noop",
    label: "Noop",
    description: "Does nothing",
  },
  {
    value: "createRandomInternalDataJobs",
    label: "Create Random Internal Data Jobs",
    description:
      "Creates random internal data jobs with optional logs and sub-jobs",
  },
] as const;

type FixtureType = (typeof FIXTURE_OPTIONS)[number]["value"];

interface RangeInputProps {
  label: string;
  value: Range | undefined;
  onChange: (value: Range | undefined) => void;
}

const RangeInput: React.FC<RangeInputProps> = ({ label, value, onChange }) => {
  const [enabled, setEnabled] = useState(!!value);
  const [min, setMin] = useState(value?.min ?? 0);
  const [max, setMax] = useState(value?.max ?? 10);

  const handleToggle = (checked: boolean) => {
    setEnabled(checked);
    if (checked) {
      onChange({ min, max });
    } else {
      onChange(undefined);
    }
  };

  const handleMinChange = (newMin: number) => {
    setMin(newMin);
    if (enabled) {
      onChange({ min: newMin, max });
    }
  };

  const handleMaxChange = (newMax: number) => {
    setMax(newMax);
    if (enabled) {
      onChange({ min, max: newMax });
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id={`${label}-enabled`}
          checked={enabled}
          onChange={(e) => handleToggle(e.target.checked)}
          className="rounded"
        />
        <Label htmlFor={`${label}-enabled`}>{label}</Label>
      </div>
      {enabled && (
        <div className="flex space-x-2">
          <div className="flex-1">
            <Label htmlFor={`${label}-min`}>Min</Label>
            <Input
              id={`${label}-min`}
              type="number"
              value={min}
              onChange={(e) => handleMinChange(Number(e.target.value))}
              placeholder="Min value"
            />
          </div>
          <div className="flex-1">
            <Label htmlFor={`${label}-max`}>Max</Label>
            <Input
              id={`${label}-max`}
              type="number"
              value={max}
              onChange={(e) => handleMaxChange(Number(e.target.value))}
              placeholder="Max value"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default function Fixtures() {
  const [selectedFixture, setSelectedFixture] = useState<FixtureType>("noop");
  const [isRunning, setIsRunning] = useState(false);
  const [result, setResult] = useState<RunFixtureResponse | null>(null);

  // Form state for createRandomInternalDataJobs
  const [count, setCount] = useState<Range | undefined>({ min: 10, max: 10 });
  const [logCount, setLogCount] = useState<Range | undefined>({
    min: 0,
    max: 5,
  });
  const [subJobCount, setSubJobCount] = useState<Range | undefined>({
    min: 0,
    max: 3,
  });
  const [subJobLogCount, setSubJobLogCount] = useState<Range | undefined>({
    min: 0,
    max: 2,
  });

  const handleRunFixture = async () => {
    if (!selectedFixture) return;

    setIsRunning(true);
    setResult(null);

    try {
      const request: RunFixtureRequest = {
        fixture: selectedFixture,
        ...(selectedFixture === "createRandomInternalDataJobs" && {
          count,
          logCount,
          subJobCount,
          subJobLogCount,
        }),
      };

      const response = await runFixture(request);
      setResult(response.data);
    } catch (error) {
      console.error("Error running fixture:", error);
      setResult({
        success: false,
        message: `Error: ${error instanceof Error ? error.message : "Unknown error"}`,
      });
    } finally {
      setIsRunning(false);
    }
  };

  const renderFixtureForm = () => {
    if (selectedFixture === "createRandomInternalDataJobs") {
      return (
        <div className="space-y-4">
          <Typography.Heading level={3}>Parameters</Typography.Heading>
          <RangeInput label="Count" value={count} onChange={setCount} />
          <RangeInput
            label="Log Count"
            value={logCount}
            onChange={setLogCount}
          />
          <RangeInput
            label="Sub Job Count"
            value={subJobCount}
            onChange={setSubJobCount}
          />
          <RangeInput
            label="Sub Job Log Count"
            value={subJobLogCount}
            onChange={setSubJobLogCount}
          />
        </div>
      );
    }

    return null;
  };

  return (
    <div className="h-screen bg-black text-icon-primary overflow-y-auto scrollbar-thin-nilo">
      <div className="mx-auto space-y-12 pt-8 pb-24 max-w-6xl">
        <header className="text-center">
          <Typography.Title level={1} color="light" className="mb-4">
            Fixtures
          </Typography.Title>
          <Typography.Body level={1} color="light" weight="medium">
            Run fixture functions to generate test data
          </Typography.Body>
        </header>

        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle>Select and Run Fixture</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="fixture-select">Fixture Function</Label>
              <Select
                value={selectedFixture}
                onValueChange={(value) =>
                  setSelectedFixture(value as FixtureType)
                }
              >
                <SelectTrigger id="fixture-select">
                  <SelectValue placeholder="Select a fixture function" />
                </SelectTrigger>
                <SelectContent>
                  {FIXTURE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-sm text-muted-foreground">
                          {option.description}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedFixture && renderFixtureForm()}

            <Button
              onClick={handleRunFixture}
              disabled={!selectedFixture || isRunning}
              className="w-full"
            >
              {isRunning ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Running...
                </>
              ) : (
                "Run Fixture"
              )}
            </Button>
          </CardContent>
        </Card>

        {result && (
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {result.success ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500" />
                )}
                <span>Result</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Typography.Body level={1}>
                {result.success
                  ? "Fixture executed successfully!"
                  : "Fixture execution failed!"}
                {result.message && (
                  <div className="mt-2 font-mono text-sm">{result.message}</div>
                )}
              </Typography.Body>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
