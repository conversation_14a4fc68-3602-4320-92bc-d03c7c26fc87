import { PromptEntity } from "@/core/entity";
import { GameEntityComponent } from "@/core/components";
import { TransformBehavior } from "@/core/client/behaviors";
import {
  Behavior,
  BehaviorConstructor,
  EntityBuilder,
  EntityData,
  EntityId,
} from "@nilo/ecs";

/* Allows constructing a *PromptEntity* **/
export class PromptBehavior implements Behavior {
  prompt: string;
  userId: EntityId;
  color: number;
  isStatic: boolean;

  constructor(
    prompt: string = "",
    userId: EntityId,
    color: number = 0xffffff,
    isStatic: boolean = false
  ) {
    this.prompt = prompt;
    this.userId = userId;
    this.color = color;
    this.isStatic = isStatic;
  }

  addToEntity(entity: EntityBuilder): void {
    const promptEntity = new PromptEntity(null, {
      prompt: this.prompt,
      userId: this.userId,
      color: this.color,
      isStatic: this.isStatic,
    });

    entity.addComponent(GameEntityComponent, promptEntity);
  }

  getFromEntity(entity: EntityData): this {
    const promptEntity = entity.getComponent(
      GameEntityComponent
    )! as PromptEntity;

    const prompt = promptEntity.getPrompt() || this.prompt;
    const color = promptEntity.getColor() || this.color;
    const userId = promptEntity.userId;
    const isStatic = promptEntity.isStaticEntity;

    return new PromptBehavior(prompt, userId, color, isStatic) as this;
  }

  dependencies(): BehaviorConstructor[] {
    return [TransformBehavior];
  }
}
