import {
  FieldValue,
  QueryDocumentSnapshot,
  Timestamp,
  UpdateData,
} from "firebase-admin/firestore";
import {
  DbCollections,
  DbModelGenerationTask,
  ModelGenerationProviders,
  ModelGenerationProvider,
  ModelGenerationTaskType,
  ModelGenerationTaskStatus,
  ModelGenerationTaskId,
  DbAsset,
} from "@nilo/firebase-schema";
import { db } from "../..";
import { onDocumentCreated, onDocumentUpdated } from "../utils";
import { collection } from "../../typedFirebase";
import { Logger } from "../../logger";
import { runWithRetry } from "../../utils";
import { uploadModelToFirestoreFromURL } from "../../storage";
import { captureEvent } from "../../posthog";
import { processTripoRequest } from "./tripo";
import { processMeshyRequest } from "./meshy";
import { processRodinRequest } from "./fal-rodin";
import {
  expectedDurationSecondsPerProvider,
  ModelGenerationProviderTaskOptions,
  parseProviderVersion,
  providers,
  supportedProviderVersionPairs,
} from "./config";
import { moderateTaskContent } from "./moderation";

async function runModerationCheck(
  task: DbModelGenerationTask,
  taskDocumentSnapshot: QueryDocumentSnapshot<
    DbModelGenerationTask,
    DbModelGenerationTask
  >,
  logger: Logger
): Promise<void> {
  try {
    // Initialize moderation tracking
    await taskDocumentSnapshot.ref.update({
      moderation: {
        status: "pending",
        startedAt: FieldValue.serverTimestamp(),
      },
    });

    logger.debug("🍆 CALLING MODERATION", task);
    const moderationResult = await moderateTaskContent(task, logger);
    logger.debug("🍆 Moderation result", moderationResult);

    if (!moderationResult.allowed) {
      logger.warn("❌ Task blocked by server-side moderation", {
        reason: moderationResult.reason,
      });
      await taskDocumentSnapshot.ref.update({
        moderation: {
          status: "flagged",
          reasoning: moderationResult.reason,
          completedAt: FieldValue.serverTimestamp(),
        },
      });
    } else {
      logger.debug("✅ Content passed moderation");
      await taskDocumentSnapshot.ref.update({
        moderation: {
          status: "passed",
          completedAt: FieldValue.serverTimestamp(),
        },
      });
    }
  } catch (error) {
    logger.error("❌ Error during moderation", error);
    // On moderation error, allow generation to proceed but log the issue
    await taskDocumentSnapshot.ref.update({
      moderation: {
        status: "error", // Mark as error on moderation system failure
        reasoning: `Moderation system error: ${String(error)}`,
        completedAt: FieldValue.serverTimestamp(),
      },
    });
  }
}

async function runAssetSaving(
  task: DbModelGenerationTask,
  taskId: string,
  taskDocumentSnapshot: QueryDocumentSnapshot<DbModelGenerationTask>,
  logger: Logger
) {
  // add model being generated to assets (and the builder bag)
  const assetsCollection = collection<DbAsset>(DbCollections.assets);
  const assetRef = await assetsCollection.add({
    createdAt: FieldValue.serverTimestamp(),
    updatedAt: FieldValue.serverTimestamp(),
    userId: task.userId,
    type: "3d-model",
    status: "pending",
    isDeleted: false,
    rigged: task.taskBody.type === ModelGenerationTaskType.animate_rig,
    cacheKey: task.cacheKey,
    // FIXME: do we actually need a name?
    name:
      task.taskBody.type === ModelGenerationTaskType.text_to_model
        ? task.taskBody.prompt
        : task.cacheKey,
    provider: providers[0].provider, // just a placeholder - this will get updated later
    taskId,
  });
  await taskDocumentSnapshot.ref.update({
    assets: FieldValue.arrayUnion(assetRef.id),
  });
  logger.debug("💾 Asset saved", { assetRef });
}

export type ModelGenerationContext = {
  task: DbModelGenerationTask;
  providerOptions: ModelGenerationProviderTaskOptions;
  originalTask?: DbModelGenerationTask;
  functionStartedAtMs: number;
  logger: Logger;
};

export interface ModelGenerationListener {
  onRequested: (update?: { providerTaskId?: string }) => Promise<void>;
  onProgress: (progress: {
    status: ModelGenerationTaskStatus;
    progress: number;
  }) => Promise<void>;
  onFinished: (
    result:
      | { status: "success" }
      | {
          status: "failed" | "cancelled";
          message: string;
          suggestion: string;
          providerCode?: number;
        }
  ) => Promise<boolean>;
  onModelReady: (providerModelUrl: string) => Promise<boolean>;
}

type ModelGenerationProcessor = (
  context: ModelGenerationContext,
  listener: ModelGenerationListener
) => Promise<void>;

const modelGenerationProcessors: Record<
  ModelGenerationProviders,
  ModelGenerationProcessor
> = {
  [ModelGenerationProviders.Tripo]: processTripoRequest,
  [ModelGenerationProviders.Meshy]: processMeshyRequest,
  [ModelGenerationProviders["Fal-Rodin"]]: processRodinRequest,
};

export const onModelGenerationRequest = onDocumentCreated(
  `${DbCollections.modelGenerationTasks}/{id}`,
  { memory: "1GiB" },
  async (event) => {
    const logger = Logger.create({ taskId: event.params.id });

    const functionStartedAtMs = performance.now();
    const taskId = event.params.id;
    const taskDocumentSnapshot = event.data as
      | QueryDocumentSnapshot<DbModelGenerationTask, DbModelGenerationTask>
      | undefined;
    if (!taskDocumentSnapshot) {
      logger.warn("❌ No task found");
      return;
    }
    if (taskDocumentSnapshot.data()?.createdAt === undefined) {
      logger.debug("🚀 Task createdAt is undefined - setting now");
      await taskDocumentSnapshot.ref.update({
        createdAt: FieldValue.serverTimestamp(),
      });
    }
    const {
      success: taskParseSuccess,
      data: task,
      error: taskParseError,
    } = DbModelGenerationTask.safeParse(taskDocumentSnapshot.data());
    if (!taskParseSuccess) {
      logger.error("❌ Invalid task data", taskParseError);
      return;
    }
    logger.info("🚀 New Model Generation Request", task);

    // Start moderation in parallel with generation
    const moderationPromise = runModerationCheck(
      task,
      taskDocumentSnapshot,
      logger
    );

    // add model being generated to assets (and the builder bag)
    await runAssetSaving(task, taskId, taskDocumentSnapshot, logger);

    let finalUpdate: UpdateData<DbModelGenerationTask> = {
      status: "failed",
      message: "Internal error processing request",
      suggestion: "Try again later",
    };
    switch (task.taskBody.type) {
      case ModelGenerationTaskType.image_to_model:
      case ModelGenerationTaskType.text_to_model: {
        let providersToTry = providers.map(({ provider, providerOptions }) => ({
          provider,
          providerOptions: providerOptions ?? {},
        }));
        if (task.requestedProviderVersion) {
          const parsedProvider = parseProviderVersion(
            task.requestedProviderVersion
          );
          if (parsedProvider === undefined) {
            logger.warn(
              "❌ Requested provider version not found",
              task.requestedProviderVersion
            );
            await taskDocumentSnapshot.ref.update({
              status: "failed",
              message: `Requested provider version not found: ${task.requestedProviderVersion}. Known versions: ${Object.keys(supportedProviderVersionPairs).join(", ")}`,
              finishedAt: FieldValue.serverTimestamp(),
            });
            return;
          }
          providersToTry = [parsedProvider];
        }

        for (const { provider, providerOptions } of providersToTry) {
          logger.debug("🚀 Processing request with provider", {
            providerName: provider.name,
            providerVersion: provider.version,
          });
          const processRequest = modelGenerationProcessors[provider.name];
          if (!processRequest) {
            logger.error("❌ No processor found for provider", {
              providerName: provider.name,
            });
            throw new Error("No processor found for provider");
          }
          const context: ModelGenerationContext = {
            task: { ...task, provider },
            providerOptions: providerOptions?.[task.taskBody.type] ?? {},
            functionStartedAtMs,
            logger,
          };
          const listener = new Listener(context, taskId, taskDocumentSnapshot);
          try {
            await processRequest(context, listener);
          } catch (err) {
            logger.warn("❌ Error processing request", {
              error: err,
              provider: provider.name,
            });
            await listener.onFinished({
              status: "failed",
              message: "Error processing request",
              suggestion: "Try again later",
            });
          }
          if (listener.finalUpdate) {
            finalUpdate = listener.finalUpdate;
            if (finalUpdate.status === "success") {
              break;
            }
          }
        }
        break;
      }
      case ModelGenerationTaskType.animate_rig: {
        // find provider which generated the original model
        const originalTaskId = task.taskBody.taskId;
        if (!originalTaskId) {
          logger.error("❌ No original task ID found");
          await taskDocumentSnapshot.ref.update({
            status: "failed",
            message: "No original task ID found",
          });
          return;
        }
        const originalTask = (
          await collection<DbModelGenerationTask>(
            DbCollections.modelGenerationTasks
          )
            .doc(originalTaskId)
            .get()
        )?.data();
        if (!originalTask) {
          logger.error("❌ Original task not found", { originalTaskId });
          await taskDocumentSnapshot.ref.update({
            status: "failed",
            message: "Original task not found",
          });
          return;
        }
        const provider = originalTask.provider;
        if (!provider) {
          logger.error("❌ Original task provider not found", {
            originalTaskId,
          });
          await taskDocumentSnapshot.ref.update({
            status: "failed",
            message: "Original task's provider not found",
          });
          return;
        }
        await taskDocumentSnapshot.ref.update({ provider });
        const processRequest = modelGenerationProcessors[provider.name];
        if (!processRequest) {
          logger.error("❌ No processor found for provider", {
            providerName: provider.name,
          });
          throw new Error("No processor found for provider");
        }
        const providerOptions = providers.find(
          ({ provider: p }) =>
            p.name === provider.name && p.version === provider.version
        )?.providerOptions;
        const context: ModelGenerationContext = {
          task: { ...task, provider },
          providerOptions: providerOptions?.[task.taskBody.type] ?? {},
          originalTask,
          functionStartedAtMs,
          logger,
        };
        const listener = new Listener(context, taskId, taskDocumentSnapshot);
        try {
          await processRequest(context, listener);
        } catch (err) {
          logger.warn("❌ Error processing request", {
            error: err,
            provider: provider.name,
          });
          await listener.onFinished({
            status: "failed",
            message: "Error processing request",
            suggestion: "Try again later",
          });
        }
        if (listener.finalUpdate) {
          finalUpdate = listener.finalUpdate;
        }
        break;
      }
    }

    // Wait for moderation to complete before finalizing
    await moderationPromise;

    // Apply the final update regardless of moderation status
    // Generation success/failure is independent of moderation
    await taskDocumentSnapshot.ref.update(finalUpdate);
    logger.debug("🚀 Task finished", { finalUpdate });
  }
);

class Listener implements ModelGenerationListener {
  private context: ModelGenerationContext;
  private taskId: string;
  private taskDocumentSnapshot: QueryDocumentSnapshot<DbModelGenerationTask>;
  public status: ModelGenerationTaskStatus = "unknown";
  public finalUpdate?: UpdateData<DbModelGenerationTask>;

  constructor(
    context: ModelGenerationContext,
    taskId: ModelGenerationTaskId,
    taskDocumentSnapshot: QueryDocumentSnapshot<DbModelGenerationTask>
  ) {
    this.context = context;
    this.taskId = taskId;
    this.taskDocumentSnapshot = taskDocumentSnapshot;
  }

  get task(): DbModelGenerationTask {
    return this.context.task;
  }

  get provider(): ModelGenerationProvider | undefined {
    return this.task.provider;
  }

  get rigged(): boolean {
    return this.task.taskBody.type === ModelGenerationTaskType.animate_rig;
  }

  get logger(): Logger {
    return this.context.logger;
  }

  update(updates: UpdateData<DbModelGenerationTask>) {
    return this.taskDocumentSnapshot.ref.update(updates);
  }

  async onRequested(update?: { providerTaskId?: string }): Promise<void> {
    this.logger.debug("🚀 onRequested", {
      providerTaskId: update?.providerTaskId,
    });
    this.status = "queued";
    const updateData: UpdateData<DbModelGenerationTask> = {
      requestedAt: FieldValue.serverTimestamp(),
      status: this.status,
      provider: this.task.provider,
    };
    const expectedDurationSeconds = this.provider
      ? expectedDurationSecondsPerProvider[
          `${this.provider.name}-${this.provider.version}`
        ][this.task.taskBody.type]
      : undefined;
    this.logger.debug("🚀 Expected duration", {
      expectedDurationSeconds,
      provider: this.provider,
    });
    if (expectedDurationSeconds && !isNaN(expectedDurationSeconds)) {
      updateData.eta = Timestamp.fromDate(
        new Date(Date.now() + expectedDurationSeconds * 1000)
      );
    }
    const providerTaskId = update?.providerTaskId;
    if (providerTaskId) {
      updateData.providerTaskId = providerTaskId;
    }
    await this.update(updateData);
  }

  async onProgress(progress: {
    status: ModelGenerationTaskStatus;
    progress: number;
  }): Promise<void> {
    this.status = progress.status;
    await this.update({
      status: this.status,
      progress: progress.progress,
    });
  }

  async onFinished(
    result:
      | { status: "success" }
      | {
          status: "failed" | "cancelled";
          message: string;
          suggestion: string;
          providerCode?: number;
        }
  ): Promise<boolean> {
    this.status = result.status;
    this.finalUpdate =
      result.status === "success"
        ? {
            status: this.status,
            progress: 100,
            finishedAt: FieldValue.serverTimestamp(),
          }
        : result.providerCode
          ? {
              status: this.status,
              message: result.message,
              suggestion: result.suggestion,
              finishedAt: FieldValue.serverTimestamp(),
              providerCode: result.providerCode,
            }
          : {
              status: this.status,
              message: result.message,
              suggestion: result.suggestion,
              finishedAt: FieldValue.serverTimestamp(),
            };
    return this.status === "success";
  }

  async onModelReady(providerModelUrl: string): Promise<boolean> {
    try {
      const modelUrl = await runWithRetry(() =>
        uploadModelToFirestoreFromURL({
          modelUrl: providerModelUrl,
          modelDir: "generatedModels",
          modelName: this.taskId,
          ext: providerModelUrl.includes(".fbx") ? "fbx" : undefined,
        })
      );
      this.logger.debug(
        `🚀 Uploaded model (end-to-end in ${performance.now() - this.context.functionStartedAtMs}ms)`,
        modelUrl
      );
      await this.update({
        modelUrl,
        rigged: this.rigged,
        uploadedAt: FieldValue.serverTimestamp(),
      });
      return true;
    } catch (err) {
      this.logger.error("❌ Uploading model failed", { error: err });
      return false;
    }
  }
}

function convertTaskStatusToAssetStatus(
  taskStatus: ModelGenerationTaskStatus | undefined
): DbAsset["status"] {
  switch (taskStatus) {
    case "success":
      return "completed";
    case "failed":
      return "failed";
    case "cancelled":
      return "failed";
    case "queued":
      return "pending";
    case "running":
      return "processing";
    case undefined:
      return "pending";
    default:
      throw new Error(`Unknown task status: ${taskStatus}`);
  }
}

export const onModelGenerationTaskUpdated = onDocumentUpdated(
  `${DbCollections.modelGenerationTasks}/{id}`,
  async (event) => {
    const taskId = event.params.id;
    const logger = Logger.create({ taskId });
    const task = event.data?.after?.data() as DbModelGenerationTask | undefined;
    const taskBefore = event.data?.before?.data() as
      | DbModelGenerationTask
      | undefined;
    if (!task || !taskBefore) {
      logger.info("❌ No task found");
      return;
    }

    // check if the task status changed
    const newStatus = convertTaskStatusToAssetStatus(task.status);
    const oldStatus = convertTaskStatusToAssetStatus(taskBefore.status);
    if (newStatus === oldStatus && task.modelUrl === taskBefore.modelUrl) {
      // no meaningful change
      logger.debug("🚀 No meaningful change");
      return;
    }

    // update the assets
    const batch = db.batch();
    const assetsCollection = collection<DbAsset>(DbCollections.assets);
    const update: UpdateData<Extract<DbAsset, { type: "3d-model" }>> = {
      status: newStatus,
      provider: task.provider,
      updatedAt: FieldValue.serverTimestamp(),
    };
    if (newStatus === "completed" && task.modelUrl) {
      update.url = task.modelUrl;
    }
    logger.debug("🚀 Updating assets", {
      newStatus,
      oldStatus,
      update,
      assetIds: task.assets,
    });
    for (const assetId of task.assets ?? []) {
      const assetRef = assetsCollection.doc(assetId);
      batch.update(assetRef.ref, update);
    }
    await batch.commit();

    // emit metrics event
    if (
      newStatus !== oldStatus &&
      (newStatus === "completed" || newStatus === "failed")
    ) {
      const generationSettings = getGenerationSettings(task);
      if (!generationSettings) {
        logger.warn("❌ Unknown generation task");
        return;
      }
      if (!task.finishedAt || !task.requestedAt) {
        logger.warn("❌ No finishedAt or requestedAt found");
        return;
      }
      if (!task.provider) {
        logger.warn("❌ No provider found");
        return;
      }
      const cost = getCost(task);
      if (!cost) {
        logger.warn("❌ No cost found");
        return;
      }
      const assetId = task.assets?.[0];
      if (!assetId) {
        logger.warn("❌ No asset ID found");
        return;
      } else if (task.assets?.length && task.assets.length > 1) {
        logger.warn("❌ Multiple assets found");
        return;
      }
      const generationDurationMs =
        task.finishedAt?.toDate().getTime() -
        task.requestedAt?.toDate().getTime();
      captureEvent(task.userId, "ai_generation", {
        ...generationSettings,
        generation_duration_ms: generationDurationMs,
        asset_id: assetId,
        world_id: task.worldId,
        success: newStatus === "completed",
        error_type: newStatus === "failed" ? task.message : undefined,
        cost_usd: cost,
        model_provider: task.provider.name,
        model_name: task.provider.version,
        model_generation_task_id: taskId,
      });
    }
  }
);

function getGenerationSettings(task: DbModelGenerationTask):
  | {
      generation_type: "text_to_3d";
      prompt_text: string;
    }
  | {
      generation_type: "image_to_3d";
      image_url: string;
    }
  | {
      generation_type: "animate_rig";
    }
  | undefined {
  switch (task.taskBody.type) {
    case ModelGenerationTaskType.text_to_model:
      return {
        generation_type: "text_to_3d",
        prompt_text: task.taskBody.prompt,
      };
    case ModelGenerationTaskType.image_to_model:
      return {
        generation_type: "image_to_3d",
        image_url: task.taskBody.imageUrl,
      };
    case ModelGenerationTaskType.animate_rig:
      return {
        generation_type: "animate_rig",
      };
    default:
      return undefined;
  }
}

function getCost(task: DbModelGenerationTask): number | undefined {
  const provider = task.provider;
  if (!provider) {
    return undefined;
  }
  const costCents = providers.find(
    ({ provider: p }) =>
      p.name === provider.name && p.version === provider.version
  )?.costCents[task.taskBody.type];
  return costCents ? costCents / 100 : undefined;
}
