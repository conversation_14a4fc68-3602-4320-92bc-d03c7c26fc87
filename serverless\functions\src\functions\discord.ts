import { HttpsError } from "firebase-functions/v2/https";
import { getAuth } from "firebase-admin/auth";
import { defineSecret } from "firebase-functions/params";
import { DbCollections } from "@nilo/firebase-schema";
import { Logger } from "../logger";
import { isEmulator } from "../utils";
import { onRequest, isInternalEmail } from "./utils";
import { checkUserInWhitelisted } from "./whitelist";

const DISCORD_CLIENT_ID = defineSecret("DISCORD_CLIENT_ID");
const DISCORD_CLIENT_SECRET = defineSecret("DISCORD_CLIENT_SECRET");

interface DiscordUser {
  id: string;
  global_name: string;
  username: string;
  email: string;
  avatar: string;
  verified: boolean;
}

/**
 * Find existing user by email
 */
async function findUserByEmail(
  email: string,
  logger: Logger
): Promise<string | null> {
  try {
    const auth = getAuth();
    const userRecord = await auth.getUserByEmail(email);
    logger.info("Found existing user by email", {
      uid: userRecord.uid,
      email,
    });
    return userRecord.uid;
  } catch (error) {
    // User not found by email
    if (error instanceof Error && error.message.includes("no user record")) {
      logger.info("No existing user found with email", { email });
      return null;
    }

    logger.error("Error finding user by email", {
      email,
      error: error instanceof Error ? error.message : error,
    });
    return null;
  }
}

/**
 * Validate redirect URI for security
 */
function validateRedirectUri(
  redirectUri: string,
  logger: Logger
): { isValid: boolean; error?: string } {
  if (!redirectUri) {
    logger.error("Missing redirect URI");
    return { isValid: false, error: "Redirect URI is required" };
  }

  try {
    const url = new URL(redirectUri);

    // Only allow HTTPS in production, HTTP allowed in development/emulator
    if (!isEmulator && url.protocol !== "https:") {
      logger.error("Invalid redirect URI protocol", { redirectUri });
      return { isValid: false, error: "Redirect URI must use HTTPS" };
    }

    // Additional security: ensure the redirect URI ends with the expected callback path
    if (!url.pathname.endsWith("/auth/discord/callback")) {
      logger.error("Invalid redirect URI path", { redirectUri });
      return { isValid: false, error: "Invalid redirect URI path" };
    }

    // Domain whitelist validation for production
    if (!isEmulator) {
      const allowedDomains = [
        "nilo.io",
        "localhost", // for local development
      ];

      const isAllowedDomain =
        allowedDomains.some(
          (domain) =>
            url.hostname === domain || url.hostname.endsWith(`.${domain}`)
        ) || url.hostname.endsWith("-nilo1.vercel.app");

      if (!isAllowedDomain) {
        logger.error("Redirect URI domain not whitelisted", {
          redirectUri,
          hostname: url.hostname,
        });
        return { isValid: false, error: "Redirect URI domain not allowed" };
      }
    }

    return { isValid: true };
  } catch (error) {
    logger.error("Invalid redirect URI format", { redirectUri, error });
    return { isValid: false, error: "Invalid redirect URI format" };
  }
}

/**
 * Create or update user in Firebase Auth
 */
async function upsertFirebaseUser(
  user: DiscordUser,
  logger: Logger
): Promise<string> {
  const userInfo = {
    email: user.email,
    displayName: user.global_name || user.username,
    photoURL: user.avatar
      ? `https://cdn.discordapp.com/avatars/${user.id}/${user.avatar}.png`
      : undefined,
  };

  const auth = getAuth();

  // First, try to find existing user by email
  const existingUid = await findUserByEmail(user.email, logger);

  if (existingUid) {
    // User exists, just log them in and add Discord provider if not present
    try {
      // Get current user to check existing custom claims
      const existingUser = await auth.getUser(existingUid);
      const existingClaims = existingUser.customClaims || {};

      // Only update custom claims if Discord provider is not already present
      if (!existingClaims.discord_id) {
        const updatedClaims = {
          ...existingClaims,
          discord_id: user.id,
          provider: "discord",
        };

        await auth.setCustomUserClaims(existingUid, updatedClaims);
        logger.info("Added Discord provider to existing user", {
          uid: existingUid,
          discordId: user.id,
        });
      } else {
        logger.info("User already has Discord provider, logging in", {
          uid: existingUid,
          discordId: user.id,
        });
      }

      return existingUid;
    } catch (error) {
      logger.error("Failed to update existing Firebase user", {
        uid: existingUid,
        error: error instanceof Error ? error.message : error,
      });
      throw new HttpsError("internal", "Failed to update user account");
    }
  } else {
    // Check whitelist before creating new user
    logger.info("Checking whitelist for new Discord user", {
      email: user.email,
    });

    // Skip whitelist check in emulator
    if (!isEmulator) {
      // Skip whitelist check for internal emails
      if (!isInternalEmail(user.email)) {
        if (!user.email) {
          logger.warn("No email provided for whitelist check", {
            discordId: user.id,
          });
          throw new HttpsError(
            "invalid-argument",
            "Email is required for account creation"
          );
        }

        let isWhitelisted = false;

        isWhitelisted = await checkUserInWhitelisted(user.email, logger, {
          discordId: user.id,
        });

        if (!isWhitelisted) {
          logger.warn(
            `Discord user not whitelisted in AirTable or ${DbCollections.whitelistedEmails} collection, blocking account creation`,
            {
              discordId: user.id,
              email: user.email,
            }
          );
          throw new HttpsError(
            "permission-denied",
            "Your email is not whitelisted for account creation.",
            { isWhitelisted }
          );
        }
      } else {
        logger.info("Discord user is internal, skipping whitelist check", {
          discordId: user.id,
          email: user.email,
        });
      }
    } else {
      logger.info(
        "Emulator detected, skipping whitelist check for Discord user",
        {
          discordId: user.id,
          email: user.email,
        }
      );
    }

    // Create new user and let Firebase generate the UID
    try {
      const userRecord = await auth.createUser({
        ...userInfo,
        // Don't specify uid - let Firebase generate it
      });

      // Set custom claims to store Discord ID
      await auth.setCustomUserClaims(userRecord.uid, {
        discord_id: user.id,
        provider: "discord",
      });

      logger.info("Created new Firebase user", {
        uid: userRecord.uid,
        discordId: user.id,
      });
      return userRecord.uid;
    } catch (error) {
      logger.error("Failed to create Firebase user", {
        error: error instanceof Error ? error.message : error,
      });
      throw new HttpsError("internal", "Failed to create user account");
    }
  }
}

/**
 * Discord OAuth token exchange API endpoint
 * Receives authorization code, exchanges for custom token
 */
export const authenticateDiscord = onRequest(
  async (request, response) => {
    const logger = Logger.create({ function: "discordAuth" });

    if (request.method !== "POST") {
      response.status(405).json({ message: "Method not allowed" });
      return;
    }

    const clientId = DISCORD_CLIENT_ID.value() ?? process.env.DISCORD_CLIENT_ID;
    const clientSecret =
      DISCORD_CLIENT_SECRET.value() ?? process.env.DISCORD_CLIENT_SECRET;

    try {
      if (!clientId || !clientSecret) {
        logger.error("Missing Discord OAuth configuration", {
          hasClientId: !!clientId,
          hasClientSecret: !!clientSecret,
        });
        throw new HttpsError(
          "failed-precondition",
          "Discord OAuth not properly configured"
        );
      }

      const { code, redirectUri } = request.body;

      if (!code) {
        logger.error("Missing authorization code");
        response
          .status(400)
          .json({ message: "Authorization code is required" });
        return;
      }

      // Validate redirectUri format for security
      const validation = validateRedirectUri(redirectUri, logger);
      if (!validation.isValid) {
        response.status(400).json({ message: validation.error });
        return;
      }

      logger.info("Processing Discord OAuth token exchange", {
        hasCode: !!code,
        redirectUri,
      });

      // Exchange code for access token
      const tokenResponse = await fetch(
        "https://discord.com/api/oauth2/token",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: new URLSearchParams({
            client_id: clientId,
            client_secret: clientSecret,
            grant_type: "authorization_code",
            code: code,
            redirect_uri: redirectUri,
          }),
        }
      );

      if (!tokenResponse.ok) {
        const error = await tokenResponse.json();
        logger.error("Failed to exchange code for token", {
          status: tokenResponse.status,
          error,
        });
        throw new Error("Token exchange failed");
      }

      const tokenData = (await tokenResponse.json()) as {
        access_token: string;
        token_type: string;
        scope: string;
      };

      logger.info("Successfully exchanged code for token", {
        tokenType: tokenData.token_type,
        scope: tokenData.scope,
      });

      // Get Discord user info
      const userResponse = await fetch("https://discord.com/api/users/@me", {
        headers: {
          Authorization: `Bearer ${tokenData.access_token}`,
        },
      });

      const user = (await userResponse.json()) as DiscordUser;

      if (!userResponse.ok) {
        logger.error("Failed to fetch user info", {
          status: userResponse.status,
          error: user,
        });
        throw new Error("Failed to fetch user info");
      }

      logger.info("Successfully fetched Discord user", {
        discordId: user.id,
        username: user.username,
        email: user.email,
        verified: user.verified,
      });

      const uid = await upsertFirebaseUser(user, logger);

      const auth = getAuth();
      let customToken;

      try {
        customToken = await auth.createCustomToken(uid);
        logger.info("Successfully created custom token", { customToken });
      } catch (error) {
        logger.error("Failed to create custom token", {
          error,
        });
        throw new HttpsError("internal", "Failed to create custom token");
      }

      logger.info("Successfully created custom token", { uid });

      response.status(200).json({
        customToken,
        user: {
          id: user.id,
          username: user.username,
          avatar: user.avatar,
          email: user.email,
        },
      });
    } catch (error) {
      logger.error("Discord auth error:", error);
      response.status(500).json({ message: "Authentication failed" });
    }
  },
  {
    secrets: [
      "DISCORD_CLIENT_ID",
      "DISCORD_CLIENT_SECRET",
      "AIRTABLE_API_KEY",
      "AIRTABLE_BASE_ID",
      "AIRTABLE_TABLE_ID",
    ],
  }
);
