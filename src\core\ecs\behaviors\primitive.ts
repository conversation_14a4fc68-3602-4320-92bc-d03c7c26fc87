import { PhysicsDataBehavior } from "./physics";
import { PrimitiveType, TransformationData } from "@/liveblocks.config";
import { EntityOriginTypes } from "@/core/entity";
import { PrimitiveEntity } from "@/core/entity/PrimitiveEntity";
import { GameEntityComponent } from "@/core/components";
import {
  Behavior,
  BehaviorConstructor,
  EntityBuilder,
  EntityData,
  EntityId,
} from "@nilo/ecs";
import { TransformBehavior } from "@/core/client/behaviors";

/* Allows constructing a *PrimitiveEntity* **/
export class PrimitiveBehavior implements Behavior {
  primitiveType: PrimitiveType;
  color: number;
  userId: EntityId | null;
  originType: EntityOriginTypes;
  transformations: TransformationData[];
  tags: string[] = [];

  constructor(
    primitiveType: PrimitiveType = "Cube",
    color: number = 0xffffff,
    userId: EntityId | null = null,
    originType: EntityOriginTypes = EntityOriginTypes.NativeOrigin,
    transformations: TransformationData[] = [],
    tags: string[] = []
  ) {
    this.primitiveType = primitiveType;
    this.color = color;
    this.userId = userId;
    this.originType = originType;
    this.transformations = transformations;
    this.tags = tags;
  }

  addToEntity(entity: EntityBuilder): void {
    const primitiveEntity = new PrimitiveEntity(null, this.userId);

    // Set primitive properties
    primitiveEntity.setColor(this.color, false);
    if (this.originType) {
      primitiveEntity.setOriginType(this.originType, false);
    }
    primitiveEntity.setPrimitiveType(this.primitiveType, false);

    // Apply transformations if any
    for (const transformation of this.transformations) {
      primitiveEntity.addTransformation(transformation, false);
    }

    // TODO: NILO-945
    primitiveEntity.addTag(...this.tags);

    entity.addComponent(GameEntityComponent, primitiveEntity);
  }

  getFromEntity(entity: EntityData): this {
    const primitiveEntity = entity.getComponent(
      GameEntityComponent
    )! as PrimitiveEntity;

    const primitiveType =
      primitiveEntity.getPrimitiveType() || this.primitiveType;

    const color = primitiveEntity.getColor() || this.color;
    const userId = primitiveEntity._userId || this.userId;
    const originType = primitiveEntity.getOriginType() || this.originType;

    const transformations =
      primitiveEntity.getTransformations() || this.transformations;

    return new PrimitiveBehavior(
      primitiveType,
      color,
      userId,
      originType,
      transformations
    ) as this;
  }

  dependencies(): BehaviorConstructor[] {
    return [TransformBehavior, PhysicsDataBehavior];
  }
}
