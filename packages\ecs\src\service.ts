import { World } from "./world";

export type ServiceConstructor<T> = new (...args: any[]) => T;

/** Base class for all services. */
export class Service {
  /**
   * Binds this lifetime of a service to a world.
   * @note Does nothing yet, only exists to ensure systems are
   * cleanly initialized in tandem with world lifetimes.
   */
  public constructor(private _world: World) {}

  /** Retrieves the world this service is in. */
  protected get world() {
    return this._world;
  }
}

/** Stores and manages singleton services in the World. */
export class ServiceStore {
  private _services: Map<ServiceConstructor<unknown>, unknown> = new Map();

  public add<T extends Service>(instance: T): void {
    // Register under all parent types
    let prototype = Object.getPrototypeOf(instance);
    while (prototype && prototype !== Service.prototype) {
      const constructor = prototype.constructor;
      if (constructor) {
        if (this._services.has(constructor as ServiceConstructor<T>)) {
          throw new Error(`Service ${constructor.name} already registered`);
        }

        this._services.set(constructor as ServiceConstructor<T>, instance);
      }
      prototype = Object.getPrototypeOf(prototype);
    }
  }

  public get<T>(type: ServiceConstructor<T>): T | undefined {
    const service = this._services.get(type);
    if (service !== undefined) {
      return service as T;
    } else {
      return undefined;
    }
  }

  public has<T>(type: ServiceConstructor<T>): boolean {
    return this._services.has(type);
  }
}
