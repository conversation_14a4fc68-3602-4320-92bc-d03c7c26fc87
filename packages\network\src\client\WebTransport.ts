import Emittery from "emittery";
import { WebTransportOptions } from "@fails-components/webtransport";
import { logger } from "@sentry/react";
import {
  Channel,
  Channels,
  ChannelType,
  CleanupCallback,
  Client,
  ClientEvents,
  ClientOptions,
  ConnectionCloseInfo,
  MessageCallback,
  NetworkStream,
  ProviderType,
} from "../types";
import { StreamMessageReader, StreamMessageWriter } from "../utils";
import { NetworkStats, ChannelStats } from "../core/stats";

const WTQuickTypicalHeader = 12; // Mid-range Quick header size
const WTUDPSize = 28; // UDP header size (IPv4)
const WTMinDatagramPacketSize = WTQuickTypicalHeader + WTUDPSize;

const WTStreamFrameHeader = 8; // WebTransport stream frame header
const WTMinStreamPacketSize = WTQuickTypicalHeader + WTStreamFrameHeader;

/**
 * Calculate the size of the WebTransport datagram that will be sent over the network
 *
 * @param byteLength - The length of the byte array to send
 * @returns The size of the datagram packet that will be sent over WebTransport including QUIC/UDP headers
 */
const getWTDatagramSize = (byteLength: number): number => {
  return byteLength + WTMinDatagramPacketSize;
};

/**
 * Calculate the size of the WebTransport stream message that will be sent over the network
 *
 * @param byteLength - The length of the byte array to send
 * @returns The size of the stream packet that will be sent over WebTransport including QUIC stream headers
 */
const getWTStreamSize = (byteLength: number): number => {
  return byteLength + WTMinStreamPacketSize;
};

interface ChannelDescriptor {
  reader: StreamMessageReader;
  writer: StreamMessageWriter;
}

class UnreliableUnorderedChannel implements Channel {
  private _messageReader: StreamMessageReader;
  private _messageWriter: StreamMessageWriter;
  private _stats: ChannelStats;

  constructor(descriptor: ChannelDescriptor, stats: ChannelStats) {
    this._stats = stats;
    this._messageReader = descriptor.reader;
    this._messageWriter = descriptor.writer;

    this._messageReader.subscribe((data) => {
      this._stats._onReceive(
        this.getPacketSize(data.byteLength),
        data.byteLength
      );
    });
  }

  protected getPacketSize(byteLength: number): number {
    return getWTDatagramSize(byteLength);
  }

  public async send(data: Uint8Array): Promise<void> {
    try {
      await this._messageWriter.write(data);

      this._stats._onSend(this.getPacketSize(data.byteLength), data.byteLength);
    } catch (error) {
      // The session can be closed while we are writing
      // TODO: Handle this more gracefully
      logger.error("🔌 WebTransport send failed", {
        dataSize: data.length,
        error,
        isWriterClosed: this._messageWriter ? "unknown" : "writer-null",
      });
    }
  }

  public onReceive(cb: MessageCallback): CleanupCallback {
    return this._messageReader.subscribe(cb);
  }

  public get type(): ChannelType {
    return ChannelType.UNRELIABLE_UNORDERED;
  }

  public get stats(): ChannelStats {
    return this._stats;
  }
}

class ReliableOrderedChannel extends UnreliableUnorderedChannel {
  protected override getPacketSize(byteLength: number): number {
    return getWTStreamSize(byteLength);
  }

  public override get type(): ChannelType {
    return ChannelType.RELIABLE_ORDERED;
  }
}

export class WebTransportClient
  extends Emittery<ClientEvents>
  implements Client
{
  private _wt: WebTransport;
  private _channels = {} as Channels;

  private _stats: NetworkStats;

  declare private _reliableOrderedChannel: ReliableOrderedChannel;
  declare private _unreliableUnorderedChannel: UnreliableUnorderedChannel;

  declare private _ready: Promise<void>;
  declare private _closed: Promise<ConnectionCloseInfo>;

  static isSupported(): boolean {
    return typeof WebTransport !== "undefined";
  }

  constructor(options: ClientOptions) {
    super();

    const url = `https://${options.host + (options.port ? `:${options.port}` : "")}${options.path ?? ""}`;

    const wtOptions: WebTransportOptions = {
      serverCertificateHashes: options.hashes,
      datagramsReadableMode: "bytes",
    };

    this._wt = new WebTransport(url, wtOptions);

    this._stats = new NetworkStats();

    this.initReadyState();
    this.initCloseState();
    this.initStats();
  }

  private initStats() {
    let rafId: number = -1;
    const onFrame = () => {
      this.updateStats();
      rafId = requestAnimationFrame(onFrame);
    };

    this._ready.then(() => {
      rafId = requestAnimationFrame(onFrame);
    });

    this._closed.then(() => {
      cancelAnimationFrame(rafId);
    });
  }

  private initReadyState() {
    this._ready = this._wt.ready
      .then(() => {
        return Promise.all([
          this.initDatagramChannel(),
          this.initStreamChannel(),
        ]);
      })
      .then(() => {
        this._channels = {
          reliableOrdered: this._reliableOrderedChannel,
          unreliableUnordered: this._unreliableUnorderedChannel,
        };

        this.emit("ready");
      })
      .catch((err) => {
        logger.error("🔌 WebTransport connection error", err);
      });
  }

  private initCloseState() {
    this._closed = this._wt.closed.then((event) => {
      const closeInfo = {
        closeCode: event.closeCode || 0,
        reason: event.reason || "",
      };

      this.emit("close", closeInfo);

      return closeInfo;
    });
  }

  private async initDatagramChannel(): Promise<void> {
    const stats = new ChannelStats(
      ChannelType.UNRELIABLE_UNORDERED,
      this._stats
    );

    const datagramsReader = new StreamMessageReader(
      this._wt.datagrams.readable
    );
    const datagramsWriter = new StreamMessageWriter(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (this._wt.datagrams as any).createWritable?.() ||
        this._wt.datagrams.writable
    );

    this._unreliableUnorderedChannel = new UnreliableUnorderedChannel(
      {
        reader: datagramsReader,
        writer: datagramsWriter,
      },
      stats
    );

    this._wt.closed.then(() => {
      datagramsReader.dispose();
      datagramsWriter.dispose();
    });
  }

  private async initStreamChannel(): Promise<void> {
    const incomingStreamReader = (
      this._wt.incomingBidirectionalStreams as ReadableStream<NetworkStream>
    ).getReader();

    const { value, done } = await incomingStreamReader.read();

    if (done) {
      throw new Error("Reliable channel cannot be opened");
    }

    const stats = new ChannelStats(ChannelType.RELIABLE_ORDERED, this._stats);

    const streamReader = new StreamMessageReader(value.readable);
    const streamWriter = new StreamMessageWriter(value.writable);

    this._reliableOrderedChannel = new ReliableOrderedChannel(
      {
        reader: streamReader,
        writer: streamWriter,
      },
      stats
    );

    this._wt.closed.then(() => {
      streamReader.dispose();
      streamWriter.dispose();
    });
  }

  private _lastTimestamp = 0;
  private updateStats() {
    const now = performance.now();
    const delta = now - this._lastTimestamp;

    if (delta > NetworkStats.STATS_INTERVAL) {
      this._lastTimestamp = now;

      this.channels.reliableOrdered.stats._onCycle(delta);
      this.channels.unreliableUnordered.stats._onCycle(delta);
      this._stats._onCycle(delta);

      this._lastTimestamp = now;
    }
  }

  public get channels(): Readonly<Channels> {
    return this._channels;
  }

  public get MAX_DATAGRAM_SIZE(): number {
    return this._wt.datagrams.maxDatagramSize;
  }

  public get ready(): Promise<void> {
    return this._ready;
  }

  public get closed(): Promise<ConnectionCloseInfo> {
    return this._closed;
  }

  public get provider(): ProviderType {
    return ProviderType.WebTransport;
  }

  public get stats(): NetworkStats {
    return this._stats;
  }

  close(info?: ConnectionCloseInfo): void {
    this._wt.close(info);
  }

  public get socket(): WebTransport {
    return this._wt;
  }
}
