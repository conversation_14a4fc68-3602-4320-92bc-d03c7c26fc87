import { describe, test, expect, beforeEach } from "@jest/globals";
import {
  Struct,
  f32,
  u32,
  text,
  ByteStreamReader,
  List,
} from "@evenstar/byteform";
import { decode as decodeCBOR } from "cbor-x";
import { World, SystemContext } from "@nilo/ecs";
import {
  NetworkComponent,
  ComponentSerializeSystem,
  EntitySerializationComponent,
} from "../src/index";

describe("Serialization Integration Tests", () => {
  let world: World;
  let systemContext: SystemContext;
  let system: ComponentSerializeSystem;

  // Test data structures
  interface Position {
    x: number;
    y: number;
  }

  interface PlayerData {
    id: number;
    name: string;
    level: number;
  }

  // Test schemas
  const positionSchema = new Struct({
    x: f32,
    y: f32,
  });

  const playerDataSchema = new Struct({
    id: u32,
    name: text,
    level: u32,
  });

  // Test components
  const PositionComponent = NetworkComponent.SelfData<Position>(
    "position",
    positionSchema
  );
  const PlayerComponent = NetworkComponent.SelfData<PlayerData>(
    "player",
    playerDataSchema
  );

  beforeEach(() => {
    world = new World();
    systemContext = new SystemContext("serialize-test");
    system = new ComponentSerializeSystem();
    system.init(world, systemContext);
  });

  test("Serialize", () => {
    const entity = world.spawnEntity([
      [PositionComponent, { x: 10.5, y: 20.3 }],
    ]);

    // Run serialization
    system.run(world, systemContext);

    // Verify entity state was created and populated
    const entityState = entity.getComponent(EntitySerializationComponent);
    expect(entityState).toBeDefined();
    expect(entityState!.components.has(PositionComponent.KEY)).toBe(true);

    const componentState = entityState!.components.get(PositionComponent.KEY)!;
    expect(componentState.dirty).toBe(true);
    expect(componentState.data).toBeInstanceOf(Uint8Array);
    expect(componentState.data!.length).toBeGreaterThan(0);

    // Verify the serialized data can be read back
    const reader = new ByteStreamReader(componentState.data!);
    const deserializedData = reader.readSchema(positionSchema);
    expect(deserializedData.x).toBeCloseTo(10.5, 5);
    expect(deserializedData.y).toBeCloseTo(20.3, 5);
  });

  test("SerializeTwice", () => {
    const entity = world.spawnEntity([
      [PlayerComponent, { id: 123, name: "alice", level: 5 }],
    ]);

    // First serialization
    system.run(world, systemContext);
    let entityState = entity.getComponent(EntitySerializationComponent)!;
    let componentState = entityState.components.get(PlayerComponent.KEY)!;
    const firstData = new Uint8Array(componentState.data!);
    expect(componentState.dirty).toBe(true);

    // Reset dirty flag to simulate processing
    componentState.dirty = false;

    // Change the component data
    entity.setComponent(PlayerComponent, { id: 123, name: "alice", level: 6 });

    // Second serialization
    system.run(world, systemContext);
    entityState = entity.getComponent(EntitySerializationComponent)!;
    componentState = entityState.components.get(PlayerComponent.KEY)!;
    const secondData = componentState.data!;

    // Verify data changed
    expect(componentState.dirty).toBe(true);
    expect(secondData).not.toEqual(firstData);

    // Verify new data is correct
    const reader = new ByteStreamReader(secondData);
    const deserializedData = reader.readSchema(playerDataSchema);
    expect(deserializedData).toEqual({ id: 123, name: "alice", level: 6 });
  });

  test("SerializeNoChange", () => {
    const entity = world.spawnEntity([[PositionComponent, { x: 1.0, y: 2.0 }]]);

    // First serialization
    system.run(world, systemContext);
    let entityState = entity.getComponent(EntitySerializationComponent)!;
    let componentState = entityState.components.get(PositionComponent.KEY)!;
    const originalData = new Uint8Array(componentState.data!);
    expect(componentState.dirty).toBe(true);

    // Reset dirty flag to simulate processing
    componentState.dirty = false;

    // Run again without changing component data
    system.run(world, systemContext);
    entityState = entity.getComponent(EntitySerializationComponent)!;
    componentState = entityState.components.get(PositionComponent.KEY)!;

    // Data should be the same and not marked dirty again
    expect(componentState.data).toEqual(originalData);
    expect(componentState.dirty).toBe(false);
  });

  test("RemoveComponent", () => {
    const entity = world.spawnEntity([
      [PositionComponent, { x: 5.0, y: 10.0 }],
    ]);

    // First serialize the component
    system.run(world, systemContext);
    let entityState = entity.getComponent(EntitySerializationComponent)!;
    let componentState = entityState.components.get(PositionComponent.KEY)!;
    expect(componentState.data).toBeDefined();
    expect(componentState.dirty).toBe(true);

    // Reset dirty flag
    componentState.dirty = false;

    // Remove the component
    entity.removeComponent(PositionComponent);
    system.run(world, systemContext);

    // Verify component is marked for removal
    entityState = entity.getComponent(EntitySerializationComponent)!;
    componentState = entityState.components.get(PositionComponent.KEY)!;
    expect(componentState.dirty).toBe(true);
    expect(componentState.data).toBeUndefined();
  });

  test("Multiple entities serialization", () => {
    const entity1 = world.spawnEntity([
      [PositionComponent, { x: 1.0, y: 2.0 }],
    ]);
    const entity2 = world.spawnEntity([
      [PositionComponent, { x: 3.0, y: 4.0 }],
    ]);
    const entity3 = world.spawnEntity([]); // No position component

    system.run(world, systemContext);

    // Entities with position should have entity state
    const entityState1 = entity1.getComponent(EntitySerializationComponent);
    const entityState2 = entity2.getComponent(EntitySerializationComponent);
    const entityState3 = entity3.getComponent(EntitySerializationComponent);

    expect(entityState1).toBeDefined();
    expect(entityState2).toBeDefined();
    expect(entityState3).toBeNull(); // No component, no entity state

    // Verify each has correct serialized data
    const data1 = entityState1!.components.get(PositionComponent.KEY)!.data!;
    const data2 = entityState2!.components.get(PositionComponent.KEY)!.data!;

    const reader1 = new ByteStreamReader(data1);
    const reader2 = new ByteStreamReader(data2);
    const pos1 = reader1.readSchema(positionSchema);
    const pos2 = reader2.readSchema(positionSchema);

    expect(pos1.x).toBeCloseTo(1.0, 5);
    expect(pos1.y).toBeCloseTo(2.0, 5);
    expect(pos2.x).toBeCloseTo(3.0, 5);
    expect(pos2.y).toBeCloseTo(4.0, 5);
  });

  test("Complex data serialization roundtrip", () => {
    interface ComplexData {
      numbers: number[];
      text: string;
      nested: {
        value: number;
      };
    }

    // Create a custom component with transformation
    const ComplexComponent = new NetworkComponent<
      ComplexData,
      { n: number[]; t: string; v: number }
    >(
      "complex",
      (data) => ({ n: data.numbers, t: data.text, v: data.nested.value }),
      (data) => ({ numbers: data.n, text: data.t, nested: { value: data.v } }),
      new Struct({
        n: new List(f32),
        t: text,
        v: f32,
      })
    );

    const originalData: ComplexData = {
      numbers: [1.1, 2.2, 3.3],
      text: "test-data",
      nested: { value: 42.5 },
    };

    const entity = world.spawnEntity([[ComplexComponent, originalData]]);

    system.run(world, systemContext);

    const entityState = entity.getComponent(EntitySerializationComponent)!;
    const componentState = entityState.components.get(ComplexComponent.KEY)!;
    expect(componentState.data).toBeDefined();

    // Verify data can be deserialized correctly
    const reader = new ByteStreamReader(componentState.data!);
    const serializedData = reader.readSchema(
      ComplexComponent.schema.byteformSchema!
    );
    const reconstructedData = ComplexComponent.fromData(serializedData);

    expect(reconstructedData.text).toBe(originalData.text);
    expect(reconstructedData.nested.value).toBeCloseTo(
      originalData.nested.value,
      5
    );
    expect(reconstructedData.numbers).toHaveLength(originalData.numbers.length);
    for (let i = 0; i < originalData.numbers.length; i++) {
      expect(reconstructedData.numbers[i]).toBeCloseTo(
        originalData.numbers[i],
        5
      );
    }
  });

  describe("CBOR serialization", () => {
    test("SerializeCBOR - component without schema uses CBOR", () => {
      const CBORComponent = new NetworkComponent<Position, Position>(
        "cbor-position",
        (data) => data,
        (data) => data
        // No schema provided - should use CBOR
      );

      const originalPosition = { x: 10.5, y: 20.3 };
      const entity = world.spawnEntity([[CBORComponent, originalPosition]]);

      // Run serialization - should use CBOR instead of throwing error
      system.run(world, systemContext);

      // Verify entity state was created and populated
      const entityState = entity.getComponent(EntitySerializationComponent);
      expect(entityState).toBeDefined();
      expect(entityState!.components.has(CBORComponent.KEY)).toBe(true);

      const componentState = entityState!.components.get(CBORComponent.KEY)!;
      expect(componentState.dirty).toBe(true);
      expect(componentState.data).toBeInstanceOf(Uint8Array);
      expect(componentState.data!.length).toBeGreaterThan(0);

      // Verify the serialized data can be decoded with CBOR
      const decodedData = decodeCBOR(componentState.data!);
      expect(decodedData).toEqual(originalPosition);
      expect(decodedData.x).toBeCloseTo(10.5, 5);
      expect(decodedData.y).toBeCloseTo(20.3, 5);
    });

    test("Complex data CBOR serialization", () => {
      interface ComplexCBORData {
        id: number;
        name: string;
        items: string[];
        meta: { created: number; updated: number };
      }

      const CBORComplexComponent = new NetworkComponent<
        ComplexCBORData,
        ComplexCBORData
      >(
        "cbor-complex",
        (data) => data,
        (data) => data
        // No schema - should use CBOR
      );

      const originalData: ComplexCBORData = {
        id: 42,
        name: "test-entity",
        items: ["item1", "item2", "item3"],
        meta: { created: 1234567890, updated: 1234567900 },
      };

      const entity = world.spawnEntity([[CBORComplexComponent, originalData]]);

      system.run(world, systemContext);

      const entityState = entity.getComponent(EntitySerializationComponent)!;
      const componentState = entityState.components.get(
        CBORComplexComponent.KEY
      )!;
      expect(componentState.data).toBeDefined();

      // Verify CBOR deserialization
      const decodedData = decodeCBOR(componentState.data!);
      expect(decodedData).toEqual(originalData);
      expect(decodedData.id).toBe(42);
      expect(decodedData.name).toBe("test-entity");
      expect(decodedData.items).toEqual(["item1", "item2", "item3"]);
      expect(decodedData.meta.created).toBe(1234567890);
      expect(decodedData.meta.updated).toBe(1234567900);
    });
  });
});
