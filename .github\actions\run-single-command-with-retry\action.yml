name: "Run Single Command with retry"
description: "Run a single shell command with 5 attempts"
inputs:
  command:
    description: "The command to run"
    required: true
runs:
  using: "composite"
  steps:
    - shell: bash
      run: |
        for i in {1..5}; do
          if ${{ inputs.command }}; then
            echo "Command successful on attempt $i"
            break
          else
            echo "Command failed on attempt $i"
            if [ $i -lt 5 ]; then
              delay=$((RANDOM % 60 + 30))  # Random delay between 30-90 seconds
              echo "Waiting $delay seconds before retry..."
              sleep $delay
            else
              echo "All 5 attempts failed"
              exit 1
            fi
          fi
        done
