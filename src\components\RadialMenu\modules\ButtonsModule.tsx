import { HTMLMotionProps, motion } from "framer-motion";
import { DOMAttributes, FC, ReactNode, useEffect, useState } from "react";
import { MathUtils, Vector2, Vector2Like } from "three";
import { useRadialItem } from "../useRadialItem";
import { RadialUtils } from "../utils";
import { useRadialMenu } from "../context";
import { useIsMobile } from "../useIsMobile";
import { BaseRadialItem, RadialItemType } from "./types";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

type Side = "top" | "right" | "bottom" | "left";

const tmp2 = new Vector2();
const vecToSide = (vec: Vector2Like): Side => {
  tmp2.set(vec.x, vec.y);
  tmp2.normalize();
  const angle = Math.atan2(tmp2.y, tmp2.x) * (180 / Math.PI);

  if (angle >= -45 && angle < 45) return "right";
  if (angle >= 45 && angle < 135) return "bottom";
  if (angle >= 135 || angle < -135) return "left";
  if (angle >= -135 && angle < -45) return "top";

  console.warn("Unexpected angle:", angle);
  return "right"; // Default case, should not happen
};

interface RadialButtonProps {
  /** Index of the layer */
  layerIndex: number;

  /** Index of the item within the layer */
  layerItemIndex: number;

  /** Index of the item */
  itemIndex: number;
}

export interface RadialButtonsItem extends BaseRadialItem {
  /** Type of the item */
  type: RadialItemType.Buttons;

  /** Tooltip for the button */
  tooltip?: (props: RadialButtonProps) => string | null;

  /** Amount of items per layer. Count of items in this array represents the number of buttons in each layer */
  layers: number[];

  /** Component to render for each button */
  Component: FC<RadialButtonProps>;

  /** Callback function called when the component is unmounted */
  onUnmount?: () => void;

  /** Callback function called when the mobile interaction is finished */
  onMobileInteractionFinish?: (e: PointerEvent) => void; // If the pointer was up on window

  /** HTML Attributes to spread on the button element */
  attributes?: DOMAttributes<HTMLDivElement>;

  /** Whether to close the menu on click */
  closeOnClick?: boolean;

  /** Offset from the outer radius of the main menu */
  offset: number;

  /** Angle offset for the button */
  angleOffset?: number;

  /** Gap between layers */
  layersGap: number;

  /** Gap between buttons in the layer */
  buttonsGap: number;

  /** Size of the button */
  buttonSize: number;
}

interface RadialButtonElementProps {
  /** Radial button item */
  item: RadialButtonsItem;

  /** Index of the layer */
  layerIndex: number;

  /** Index of the item within the layer */
  layerItemIndex: number;

  /** Index of the item */
  itemIndex: number;

  /** Position of the button */
  point: Vector2Like;
}

const RadialButtonElement = ({
  item,

  layerIndex,
  layerItemIndex,
  itemIndex,

  point,
}: RadialButtonElementProps) => {
  const { close } = useRadialMenu();
  const [isHovered, setIsHovered] = useState(false);
  const isMobile = useIsMobile();

  const { Component, buttonSize } = item;

  const tooltip = item.tooltip
    ? item.tooltip({
        layerIndex,
        itemIndex,
        layerItemIndex,
      })
    : null;

  return (
    <motion.div
      style={{
        position: "absolute",
        zIndex: 2,

        left: point.x - buttonSize / 2,
        top: point.y - buttonSize / 2,

        width: buttonSize,
        height: buttonSize,

        overflow: "visible",

        display: "flex",
        justifyContent: "center",
        alignItems: "center",

        borderRadius: buttonSize / 2,
        touchAction: "none",
      }}
      animate={{ scale: isHovered ? 1.1 : 1 }}
      onClick={() => {
        if (item.closeOnClick) {
          close();
        }
      }}
    >
      <Tooltip delayDuration={0} open={isMobile ? isHovered : undefined}>
        <TooltipTrigger asChild>
          <div
            className="w-full h-full absolute"
            onPointerEnter={() => setIsHovered(true)}
            onPointerLeave={() => setIsHovered(false)}
          >
            <Component
              layerIndex={layerIndex}
              itemIndex={itemIndex}
              layerItemIndex={layerItemIndex}
            />
          </div>
        </TooltipTrigger>
        {tooltip && (
          <TooltipContent
            className="z-10000 relative pointer-events-none bg-nilo-fill-secondary text-nilo-text-secondary h-9 rounded-nilo-default flex items-center justify-center text-xs"
            style={{
              boxShadow: "0 4px 4px rgba(0, 0, 0, 25%)",
            }}
            side={vecToSide(point)}
            arrowProps={{
              className: "fill-nilo-fill-secondary",
              style: {
                width: 16,
                height: 20,
              },
            }}
          >
            <div className="p-2.5 z-10" style={{ lineHeight: "10px" }}>
              <p
                className="text-[11px] font-bold"
                style={{ lineHeight: "10px" }}
              >
                {tooltip}
              </p>
            </div>
          </TooltipContent>
        )}
      </Tooltip>
    </motion.div>
  );
};

export const RadialButtonsModule = ({ item }: { item: RadialButtonsItem }) => {
  const { angle, radius } = useRadialItem();
  const { buttonSize, onUnmount, onMobileInteractionFinish } = item;
  const isMobile = useIsMobile();

  useEffect(() => {
    return onUnmount;
  }, [onUnmount]);

  useEffect(() => {
    if (!isMobile) return;
    if (!onMobileInteractionFinish) return;

    window.addEventListener("pointerup", onMobileInteractionFinish);

    return () => {
      window.removeEventListener("pointerup", onMobileInteractionFinish);
    };
  }, [onMobileInteractionFinish, isMobile]);

  if (item.layers.length === 0) {
    return null; // No buttons to display
  }

  const layers: Vector2Like[][] = [];

  let layerIndex = 0;
  for (const itemsCount of item.layers) {
    const layerRadius =
      radius +
      item.offset +
      buttonSize / 2 +
      layerIndex * (item.layersGap + buttonSize);

    const angleOffset = item.angleOffset || 0;

    layers.push(
      RadialUtils.generateWedgedPoints({
        total: itemsCount,
        gap: item.buttonsGap,
        size: item.buttonSize,
        angle: angle + angleOffset * MathUtils.DEG2RAD, // Adjust angle to point upwards
        radius: layerRadius,
      })
    );
    layerIndex++;
  }

  let itemIndex = 0;

  return (
    <motion.div
      {...(item.attributes as HTMLMotionProps<"div">)}
      onPointerDown={(e) =>
        (e.target as HTMLElement).releasePointerCapture(e.pointerId)
      }
    >
      {layers.map((points, layerIndex) => (
        <motion.div
          key={layerIndex}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{
            delay: layerIndex * 0.075,
          }}
        >
          {points.map((point, index) => {
            const currentIndex = itemIndex++;

            return (
              <RadialButtonElement
                key={index}
                item={item}
                layerIndex={layerIndex}
                itemIndex={currentIndex}
                layerItemIndex={index}
                point={point}
              />
            );
          })}
        </motion.div>
      ))}
    </motion.div>
  );
};

export const buildButtonsModule = (
  payload: Omit<RadialButtonsItem, "type">
): RadialButtonsItem => {
  return {
    type: RadialItemType.Buttons,
    ...payload,
  };
};

export interface ButtonsItem extends DOMAttributes<HTMLDivElement> {
  icon: ReactNode;
  tooltip?: string;
}

interface ButtonsSetProps
  extends Omit<
    RadialButtonsItem,
    | "type"
    | "Component"
    | "layers"
    | "offset"
    | "layersGap"
    | "buttonsGap"
    | "buttonSize"
  > {
  layers: ButtonsItem[][];
}

export const buildButtonsSetModule = (
  payload: ButtonsSetProps
): RadialButtonsItem => {
  const items = payload.layers;
  return {
    type: RadialItemType.Buttons,
    ...payload,

    layers: items.map((layer) => layer.length),
    tooltip: ({ layerIndex, layerItemIndex }: RadialButtonProps) =>
      items[layerIndex][layerItemIndex].tooltip || null,
    Component: ({ layerIndex, layerItemIndex }: RadialButtonProps) => {
      const { icon, tooltip: _, ...props } = items[layerIndex][layerItemIndex];
      return (
        <div
          className="w-full h-full text-2xl overflow-hidden flex justify-center items-center rounded-full pointer-events-auto bg-nilo-fill-tertiary hover:bg-nilo-fill-tertiary-hover active:bg-nilo-fill-tertiary-pressed"
          {...props}
        >
          {icon}
        </div>
      );
    },

    offset: 12,

    layersGap: 12,
    buttonsGap: 12,

    buttonSize: 64,
  };
};
