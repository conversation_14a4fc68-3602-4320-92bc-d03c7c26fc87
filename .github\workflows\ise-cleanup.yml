name: Cleanup ISE

on:
  workflow_dispatch: {}
  pull_request:
    types: [closed]

jobs:
  # deletes Firebase Functions, Firestore Database and Storage Bucket
  cleanup-firebase:
    # never run this for main or prod
    if: (github.head_ref || github.ref_name) != 'main' && (github.head_ref || github.ref_name) != 'prod'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-gcp-auth
      - uses: ./.github/actions/setup-node
      - uses: ./.github/actions/get-isolated-environment
        id: isolated-env
        with:
          branch: ${{ github.head_ref || github.ref_name }}
      - run: pnpm exec firebase --non-interactive functions:delete --force ${{ steps.isolated-env.outputs.functions }}
        continue-on-error: true
      - name: Delete database
        run: |
          if pnpm exec firebase firestore:databases:get ${{ steps.isolated-env.outputs.database }} &> /dev/null; then
            echo "Database exists, deleting"
            pnpm exec firebase --non-interactive firestore:databases:delete --force ${{ steps.isolated-env.outputs.database }}
          else
            echo "Database does not exist, skipping"
          fi
      - name: Delete bucket
        run: |
          if gcloud storage buckets describe gs://${{ steps.isolated-env.outputs.bucket }} &> /dev/null; then
            echo "Bucket exists, deleting"
            gcloud storage rm -r --quiet gs://${{ steps.isolated-env.outputs.bucket }}
          else
            echo "Bucket does not exist, skipping"
          fi

  cleanup-server-fleet:
    # never run this for main or prod
    if: (github.head_ref || github.ref_name) != 'main' && (github.head_ref || github.ref_name) != 'prod'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-gcp-auth
        with:
          token_format: access_token
      - uses: google-github-actions/get-gke-credentials@v1
        with:
          cluster_name: nilo-eu-west4-cluster
          location: europe-west4
      - uses: ./.github/actions/setup-node
      - uses: ./.github/actions/get-isolated-environment
        id: isolated-env
        with:
          branch: ${{ github.head_ref || github.ref_name }}
      - run: helm delete ${{ steps.isolated-env.outputs.fleet }} --ignore-not-found
