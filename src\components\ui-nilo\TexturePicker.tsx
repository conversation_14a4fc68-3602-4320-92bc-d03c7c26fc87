import type React from "react";

import { XIcon } from "lucide-react";
import { useCallback, useState, useEffect } from "react";
import toast from "react-hot-toast";
import { RepeatWrapping } from "three";
import { Button } from "../ui/button";
import { SceneSettingsService } from "@/services/SceneSettingsService";
import {
  RoomDataService,
  type NamedTextureData,
  getTextureUrl,
  getTextureDisplayName,
} from "@/services/RoomDataService";
import { Client } from "@/core/client";
import { cn } from "@/lib/utils";

// Functional Components
interface TexturePickerProps {
  id: string;
  value?: NamedTextureData | string;
  onChange?: (value: NamedTextureData | string) => void;
  roomId: string;
  type: "environment" | "grid";
  subtype?: "map" | "roughnessMap" | "metalnessMap" | "normalMap" | "aoMap";
  defaultText?: string;
  className?: string;
}

export function TexturePicker({
  id,
  value,
  onChange,
  roomId,
  type,
  subtype,
  defaultText = "Current",
  className,
}: TexturePickerProps) {
  const [textureUrl, setTextureUrl] = useState(
    getTextureDisplayName(value, "")
  );
  const { previewImage, setPreviewImageFromFile, setPreviewImageFromUrl } =
    usePreviewImage();
  const [isLoading, setIsLoading] = useState(false);

  // Update display when value prop changes
  useEffect(() => {
    const displayName = getTextureDisplayName(value);
    const url = getTextureUrl(value);

    setTextureUrl(displayName);
    if (url) {
      setPreviewImageFromUrl(url);
    } else {
      setPreviewImageFromUrl(null);
    }
  }, [value, setPreviewImageFromUrl]);

  const handleFileChange = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validate file type
      const validTypes = [
        "image/jpeg",
        "image/png",
        "image/webp",
        "image/hdr",
        "image/ktx2",
      ];
      if (!validTypes.includes(file.type)) {
        console.error("🚫 Invalid file type. Please select an image file.");
        return;
      }

      // Validate subtype for grid type
      if (type === "grid" && !subtype) {
        console.error("🚫 Subtype is required when type is 'grid'");
        return;
      }

      setIsLoading(true);

      try {
        await toast.promise(
          (async () => {
            let url: string;

            if (type === "environment") {
              // Save environment texture to Firebase Storage
              url =
                await SceneSettingsService.textures.saveEnvironmentTexture(
                  file
                );
              await RoomDataService.updateTextures(roomId, {
                environment: { url, name: file.name },
              });

              // Load texture
              await Client.lighting?.loadCustomEnvironment(file);
            } else {
              // Save grid texture to Firebase Storage
              url = await SceneSettingsService.textures.saveGridTexture(
                file,
                subtype!
              );
              await RoomDataService.updateTextures(roomId, {
                grid: {
                  [subtype!]: { url, name: file.name },
                },
              });

              // Load texture
              await Client.grid?.loadCustomGridMap(file, (texture) => {
                const grid = Client.grid;
                const composer = Client.composer;
                texture.wrapS = texture.wrapT = RepeatWrapping;
                grid.gridScale = composer.options.gridRepeat;

                switch (subtype) {
                  case "map":
                    grid.gridMap = texture;
                    grid.mesh.material.map = texture;
                    break;
                  case "roughnessMap":
                    grid.gridRoughnessMap = texture;
                    grid.mesh.material.roughnessMap = texture;
                    grid.mesh.material.roughness = 1;
                    break;
                  case "metalnessMap":
                    grid.gridMetalnessMap = texture;
                    grid.mesh.material.metalnessMap = texture;
                    grid.mesh.material.metalness = 1;
                    break;
                  case "normalMap":
                    grid.gridNormalMap = texture;
                    grid.mesh.material.normalMap = texture;
                    break;
                  case "aoMap":
                    grid.gridAOMap = texture;
                    grid.mesh.material.aoMap = texture;
                    break;
                }

                texture.repeat.set(
                  grid.gridSize * composer.options.gridRepeat,
                  grid.gridSize * composer.options.gridRepeat
                );
                grid.mesh.material.needsUpdate = true;
              });
            }

            setTextureUrl(file.name);
            setPreviewImageFromFile(file);
            onChange?.({ url, name: file.name });
          })(),
          {
            loading: `Loading ${file.name}...`,
            success: `Loaded ${file.name}`,
            error: (err: Error) => err.message,
          }
        );
      } catch (err) {
        console.error("❌ Error loading texture:", err);
      } finally {
        setIsLoading(false);
      }
    },
    [onChange, setPreviewImageFromFile, roomId, type, subtype]
  );

  const clearTexture = useCallback(() => {
    setTextureUrl("");
    setPreviewImageFromUrl(null);
    onChange?.("");
  }, [onChange, setPreviewImageFromUrl]);

  return (
    <div
      className={cn(
        "relative flex cursor-pointer items-center gap-4 rounded-nilo-default p-1 text-sm font-normal font-nilo-primary leading-none outline-none select-none",
        "text-nilo-text-secondary",
        "hover:bg-nilo-fill-secondary",
        "active:bg-nilo-fill-tertiary",
        "focus-visible:bg-nilo-fill-secondary-hover",
        "ring-2 ring-inset ring-nilo-border-secondary",
        className
      )}
    >
      <div className="w-8 h-8 bg-nilo-fill-secondary-hover rounded flex items-center justify-center overflow-hidden">
        {previewImage ? (
          <img
            src={previewImage}
            alt="Texture preview"
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-4 h-4 text-nilo-icon-placeholder">
            {isLoading ? (
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-nilo-icon-placeholder" />
            ) : null}
          </div>
        )}
      </div>

      <div
        className="flex-1 text-nilo-text-secondary truncate"
        title={textureUrl || undefined}
      >
        {getTextureDisplayName(value, defaultText)}
      </div>

      <input
        type="file"
        id={`${id}-file`}
        accept="image/*,.hdr,.ktx2"
        onChange={handleFileChange}
        className="hidden"
      />

      <label
        htmlFor={`${id}-file`}
        className="absolute inset-0 cursor-pointer"
        title="Click to upload texture"
        aria-label="Upload texture"
      >
        <span className="sr-only">Upload texture</span>
      </label>

      {textureUrl && (
        <Button
          variant="ghost"
          size="sm"
          onClick={clearTexture}
          className="h-8 w-8 p-0 relative z-10"
          title="Clear texture"
        >
          <XIcon size={14} />
        </Button>
      )}
    </div>
  );
}

// Custom hook for managing preview image with automatic cleanup
function usePreviewImage() {
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [isObjectUrl, setIsObjectUrl] = useState<boolean>(false);

  const setPreviewImageFromFile = useCallback(
    (file: File | null) => {
      // Revoke previous URL if it's an object URL
      if (previewImage && isObjectUrl) {
        URL.revokeObjectURL(previewImage);
      }

      // Create new URL if file is provided
      if (file) {
        const newUrl = URL.createObjectURL(file);
        setPreviewImage(newUrl);
        setIsObjectUrl(true);
      } else {
        setPreviewImage(null);
        setIsObjectUrl(false);
      }
    },
    [previewImage, isObjectUrl]
  );

  const setPreviewImageFromUrl = useCallback(
    (url: string | null) => {
      // Revoke previous URL if it's an object URL
      if (previewImage && isObjectUrl) {
        URL.revokeObjectURL(previewImage);
      }

      // Set external URL directly (no need to create object URL)
      setPreviewImage(url);
      setIsObjectUrl(false);
    },
    [previewImage, isObjectUrl]
  );

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (previewImage && isObjectUrl) {
        URL.revokeObjectURL(previewImage);
      }
    };
  }, [previewImage, isObjectUrl]);

  return { previewImage, setPreviewImageFromFile, setPreviewImageFromUrl };
}
