import { useAuthState } from "react-firebase-hooks/auth";
import {
  addDoc,
  deleteDoc,
  doc,
  limit,
  serverTimestamp,
  setDoc,
  updateDoc,
  WithFieldValue,
  QueryDocumentSnapshot,
  getDocs,
  startAfter,
} from "firebase/firestore";
import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Link,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import {
  CopyPlusIcon,
  HeartIcon,
  HeartOffIcon,
  Loader2,
  PlayIcon,
  TrashIcon,
} from "lucide-react";
import { LiveMap } from "@liveblocks/client";
import { useStorage, useOthers } from "@liveblocks/react";
import { RoomProvider } from "@liveblocks/react/suspense";
import { auth, bindFunction } from "@/config/firebase";
import { useWorldPreviewScreenshotSD } from "@/hooks/useWorldPreviewScreenshotSD";
import { useDeleteWorld } from "@/hooks/useDeleteWorld";
import { ConfirmationModal } from "@/components/ui-nilo/ConfirmationModal";
import {
  queryT,
  useFirebaseQuery,
  whereT,
  orderByT,
  useStreamDocumentById,
  useStreamDocument,
  QueryConstraintT,
} from "@/hooks/firebaseHooks";
import {
  usersCollection,
  worldPlaySessionsCollection,
  worldsCollection,
  worldScoresCollection,
  worldUserRelationDoc,
} from "@/utils/firestoreCollections";
import {
  CreateWorldRequest,
  CreateWorldResponse,
  CloneWorldRequest,
  DbWorldPlaySession,
  DbWorld,
  DbCollections,
} from "@nilo/firebase-schema";
import { Button } from "@/components/ui/button";
import { Typography } from "@/components/ui-nilo/Typography";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { ShareIcon, UserIcon } from "@/icons";
import {
  FirestoreDocumentUrl,
  LiveblocksRoomUrl,
} from "@/pages/internal/devUrls";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const createWorld = bindFunction<CreateWorldRequest, CreateWorldResponse>(
  "createWorld"
);

const cloneWorld = bindFunction<CloneWorldRequest, CreateWorldResponse>(
  "cloneWorld"
);

// Component to fetch and display Liveblocks room data
function LiveblocksRoomData() {
  const storage = useStorage((root) => root);
  const others = useOthers();

  const [storageExpanded, setStorageExpanded] = useState(false);
  const [presenceExpanded, setPresenceExpanded] = useState(false);

  const storageData = storage
    ? {
        entities: storage.entities
          ? Object.fromEntries(storage.entities.entries())
          : {},
      }
    : null;

  const presenceData = others.map((other) => ({
    connectionId: other.connectionId,
    id: other.id,
    info: other.info,
    presence: other.presence,
  }));

  return (
    <div className="space-y-4">
      {/* Storage Data */}
      <div className="bg-nilo-fill-secondary rounded-lg p-4">
        <button
          onClick={() => setStorageExpanded(!storageExpanded)}
          className="flex items-center justify-between w-full text-left"
        >
          <Typography.Heading
            level={3}
            color="light"
            weight="medium"
            className="mb-0"
          >
            Storage Data
          </Typography.Heading>
          <span className="text-nilo-text-secondary">
            {storageExpanded ? "▼" : "▶"}
          </span>
        </button>
        {storageExpanded && (
          <pre className="text-xs text-nilo-text-secondary overflow-auto max-h-96 bg-black/20 p-4 rounded border mt-4">
            {JSON.stringify(storageData, null, 2)}
          </pre>
        )}
      </div>

      {/* Presence Data */}
      <div className="bg-nilo-fill-secondary rounded-lg p-4">
        <button
          onClick={() => setPresenceExpanded(!presenceExpanded)}
          className="flex items-center justify-between w-full text-left"
        >
          <Typography.Heading
            level={3}
            color="light"
            weight="medium"
            className="mb-0"
          >
            Presence Data
          </Typography.Heading>
          <span className="text-nilo-text-secondary">
            {presenceExpanded ? "▼" : "▶"}
          </span>
        </button>
        {presenceExpanded && (
          <pre className="text-xs text-nilo-text-secondary overflow-auto max-h-96 bg-black/20 p-4 rounded border mt-4">
            {JSON.stringify(presenceData, null, 2)}
          </pre>
        )}
      </div>
    </div>
  );
}

const formatDate = (timestamp: DbWorld["createdAt"] | undefined) => {
  if (!timestamp) {
    return "N/A";
  }
  return new Date(timestamp.toDate()).toLocaleString();
};

const formatPlayTime = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = (seconds % 60).toFixed(2);

  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
};

const formatDateTwoLines = (timestamp: DbWorld["createdAt"] | undefined) => {
  if (!timestamp) {
    return { date: "N/A", time: "" };
  }
  const date = new Date(timestamp.toDate());
  return {
    date: date.toLocaleDateString(),
    time: date.toLocaleTimeString(),
  };
};

const ActionButton = ({
  children,
  disabled,
  onClick,
  variant = "default",
}: {
  children: React.ReactNode;
  disabled?: boolean;
  onClick: () => Promise<unknown> | unknown;
  variant?: "default" | "secondary" | "tertiary" | "primary" | "ghost";
}) => {
  const [processing, setProcessing] = useState(false);
  return (
    <Button
      variant={variant}
      disabled={disabled || processing}
      onClick={async () => {
        setProcessing(true);
        try {
          await onClick();
        } finally {
          setProcessing(false);
        }
      }}
    >
      {processing ? "Processing..." : children}
    </Button>
  );
};

const WorldRow = ({
  worldId,
  userId,
}: {
  worldId: string;
  userId?: string;
}) => {
  const world = useStreamDocumentById(worldsCollection, worldId).value?.data();
  const score = useStreamDocumentById(
    worldScoresCollection,
    worldId
  ).value?.data();
  const navigate = useNavigate();

  const {
    isDeleting,
    showDeleteConfirm,
    handleDeleteWorld,
    openDeleteConfirm,
    closeDeleteConfirm,
  } = useDeleteWorld({
    onSuccess: () => {
      // Optionally refresh the list or show a success message
    },
  });

  const handleConfirmDelete = async () => {
    if (userId) {
      await handleDeleteWorld(worldId, userId);
    }
  };

  return (
    <>
      <TableRow
        className="hover:bg-gray-50/5 hover:cursor-pointer"
        onClick={() => {
          navigate(`/internal/worlds/${worldId}`);
        }}
      >
        <TableCell className="w-24">
          <div className="flex flex-col gap-1">
            <Badge className={world?.isPublic ? "bg-green-500" : "bg-red-500"}>
              {world?.isPublic ? "Public" : "Private"}
            </Badge>
            {world?.isFeatured && (
              <Badge className="bg-yellow-500">Featured</Badge>
            )}
          </div>
        </TableCell>

        <TableCell className="font-medium w-48">
          <div className="flex flex-col gap-1">
            <Typography.Body
              level={2}
              color="light"
              weight="medium"
              className="truncate"
            >
              {world?.name || "Unnamed World"}
            </Typography.Body>
            <Typography.Label
              level={3}
              color="light"
              className="font-mono text-xs"
            >
              {worldId}
            </Typography.Label>
          </div>
        </TableCell>

        <TableCell className="w-32 max-w-32">
          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-1 min-w-0">
              <UserIcon className="w-3 h-3 flex-shrink-0" />
              <span className="truncate min-w-0">{world?.ownerId}</span>
            </div>
          </div>
        </TableCell>

        <TableCell className="w-24">
          {score ? (
            <Typography.Label
              level={3}
              color="light"
              className="text-green-400"
            >
              {score.score.toFixed(2)}
            </Typography.Label>
          ) : (
            <Typography.Label level={3} color="light">
              -
            </Typography.Label>
          )}
        </TableCell>

        <TableCell className="w-24">
          {score ? (
            <Typography.Label level={3} color="light" className="text-blue-400">
              {score.likes.toFixed(2)}
            </Typography.Label>
          ) : (
            <Typography.Label level={3} color="light">
              -
            </Typography.Label>
          )}
        </TableCell>

        <TableCell className="w-24">
          {score ? (
            <Typography.Label
              level={3}
              color="light"
              className="text-yellow-400"
            >
              {formatPlayTime(score.totalPlayTimeSeconds)}
            </Typography.Label>
          ) : (
            <Typography.Label level={3} color="light">
              -
            </Typography.Label>
          )}
        </TableCell>

        <TableCell className="w-32">
          <div className="flex flex-col gap-1">
            <Typography.Label level={3} color="light" className="text-xs">
              {formatDateTwoLines(world?.createdAt).date}
            </Typography.Label>
            <Typography.Label level={3} color="light" className="text-xs">
              {formatDateTwoLines(world?.createdAt).time}
            </Typography.Label>
          </div>
        </TableCell>

        <TableCell className="w-40">
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              title="Play world"
              onClick={(e) => {
                e.stopPropagation();
                navigate(`/play/${worldId}`);
              }}
            >
              <PlayIcon className="w-4 h-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              title="Delete world"
              onClick={(e) => {
                e.stopPropagation();
                openDeleteConfirm();
              }}
              disabled={isDeleting}
            >
              <TrashIcon className="w-4 h-4 text-red-500 stroke-red-500" />
            </Button>
          </div>
        </TableCell>
      </TableRow>
      <ConfirmationModal
        open={showDeleteConfirm}
        onOpenChange={closeDeleteConfirm}
        title="Delete World"
        description={`Are you sure you want to delete "${world?.displayName || world?.name || "Unnamed World"}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleConfirmDelete}
        loading={isDeleting}
        variant="destructive"
      />
    </>
  );
};

function WorldOwner({ worldId }: { worldId: string }) {
  const world = useStreamDocumentById(worldsCollection, worldId).value?.data();
  const user = useStreamDocumentById(
    usersCollection,
    world?.ownerId
  ).value?.data();
  return (
    <Typography.Label level={2} color="light" className="font-mono">
      {user?.displayName} ({world?.ownerId} - {user?.username || "?"})
    </Typography.Label>
  );
}

export function Worlds() {
  const [user] = useAuthState(auth);
  const userId = user?.uid;
  const [searchParams, setSearchParams] = useSearchParams();

  const userProfiles = useFirebaseQuery(
    queryT(usersCollection, orderByT("displayName", "asc"))
  );

  // Filter state from URL params
  const ownerFilter = searchParams.get("owner") || "all";
  const showOnlyPublic = searchParams.get("public") === "true";
  const showOnlyFeatured = searchParams.get("featured") === "true";
  const showMyWorlds = searchParams.get("myWorlds") === "true";

  // Pagination state
  const [worlds, setWorlds] = useState<QueryDocumentSnapshot<DbWorld>[]>([]);
  const [lastDoc, setLastDoc] = useState<QueryDocumentSnapshot<DbWorld> | null>(
    null
  );
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);

  // Load more worlds function
  const loadMoreWorlds = useCallback(async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    try {
      const constraints: QueryConstraintT<DbWorld, DbWorld>[] = [];

      // Extract filter values from URL params
      const currentOwnerFilter = searchParams.get("owner") || "all";
      const currentShowOnlyPublic = searchParams.get("public") === "true";
      const currentShowOnlyFeatured = searchParams.get("featured") === "true";
      const currentShowMyWorlds = searchParams.get("myWorlds") === "true";

      // Add filter constraints first
      if (currentShowOnlyPublic) {
        constraints.push(whereT("isPublic", "==", true));
      }
      if (currentShowOnlyFeatured) {
        constraints.push(whereT("isFeatured", "==", true));
      }
      if (currentShowMyWorlds && userId) {
        constraints.push(whereT("ownerId", "==", userId));
      } else if (
        currentOwnerFilter.trim() &&
        currentOwnerFilter !== "all" &&
        !currentShowMyWorlds
      ) {
        constraints.push(whereT("ownerId", "==", currentOwnerFilter.trim()));
      }

      // Add ordering and limit
      constraints.push(orderByT("createdAt", "desc"));
      constraints.push(limit(20));

      if (lastDoc) {
        constraints.push(startAfter(lastDoc));
      }

      const firestoreQuery = queryT(
        worldsCollection,
        undefined,
        ...constraints
      );
      const snapshot = await getDocs(firestoreQuery);

      const newWorlds = snapshot.docs as QueryDocumentSnapshot<DbWorld>[];
      setWorlds((prev) => [...prev, ...newWorlds]);
      setLastDoc(newWorlds[newWorlds.length - 1] || null);
      setHasMore(newWorlds.length === 20);
    } catch (error) {
      console.error("Error loading more worlds:", error);
    } finally {
      setLoading(false);
    }
  }, [searchParams, userId, lastDoc, loading, hasMore]);

  // Load initial worlds
  useEffect(() => {
    const loadInitialWorlds = async () => {
      setInitialLoading(true);
      setWorlds([]);
      setLastDoc(null);
      setHasMore(true);

      try {
        const constraints: QueryConstraintT<DbWorld, DbWorld>[] = [];

        // Extract filter values from URL params
        const currentOwnerFilter = searchParams.get("owner") || "all";
        const currentShowOnlyPublic = searchParams.get("public") === "true";
        const currentShowOnlyFeatured = searchParams.get("featured") === "true";
        const currentShowMyWorlds = searchParams.get("myWorlds") === "true";

        // Add filter constraints first
        if (currentShowOnlyPublic) {
          constraints.push(whereT("isPublic", "==", true));
        }
        if (currentShowOnlyFeatured) {
          constraints.push(whereT("isFeatured", "==", true));
        }
        if (currentShowMyWorlds && userId) {
          constraints.push(whereT("ownerId", "==", userId));
        } else if (
          currentOwnerFilter.trim() &&
          currentOwnerFilter !== "all" &&
          !currentShowMyWorlds
        ) {
          constraints.push(whereT("ownerId", "==", currentOwnerFilter.trim()));
        }

        // Add ordering and limit
        constraints.push(orderByT("createdAt", "desc"));
        constraints.push(limit(20));

        const firestoreQuery = queryT(
          worldsCollection,
          undefined,
          ...constraints
        );
        const snapshot = await getDocs(firestoreQuery);

        const newWorlds = snapshot.docs as QueryDocumentSnapshot<DbWorld>[];
        setWorlds(newWorlds);
        setLastDoc(newWorlds[newWorlds.length - 1] || null);
        setHasMore(newWorlds.length === 20);
      } catch (error) {
        console.error("Error loading initial worlds:", error);
      } finally {
        setInitialLoading(false);
      }
    };

    loadInitialWorlds();
  }, [searchParams, userId]);

  // Scroll detection
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current || loading || !hasMore) return;

      const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
      const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;

      if (isNearBottom) {
        loadMoreWorlds();
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener("scroll", handleScroll);
      return () => container.removeEventListener("scroll", handleScroll);
    }
  }, [loadMoreWorlds, loading, hasMore]);

  // Reset filters when switching to "My Worlds"
  const handleShowMyWorldsChange = (checked: boolean) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("myWorlds", checked.toString());
    if (checked) {
      newParams.set("owner", "all");
    }
    setSearchParams(newParams);
  };

  // Helper function to update URL params
  const updateFilter = (key: string, value: string | boolean) => {
    const newParams = new URLSearchParams(searchParams);
    if (value === "" || value === false) {
      newParams.delete(key);
    } else {
      newParams.set(key, value.toString());
    }
    setSearchParams(newParams);
  };

  return (
    <div
      className="h-screen bg-black text-icon-primary overflow-y-auto"
      ref={containerRef}
    >
      <div className="mx-auto space-y-8 pt-8 pb-24 px-6 max-w-6xl">
        {/* Header */}
        <header className="text-center border-b border-accessories-divider pb-8">
          <div className="flex items-center justify-center">
            <Typography.Title level={1} color="light" className="mb-4">
              Worlds ({worlds.length})
            </Typography.Title>
          </div>
          <Typography.Body level={1} color="light" weight="medium">
            {showMyWorlds
              ? "Your personal world collection"
              : "Browse and discover worlds"}
          </Typography.Body>
        </header>

        {/* Filters */}
        <div className="bg-nilo-fill-secondary rounded-lg p-4">
          <div className="flex flex-wrap items-center gap-4">
            {/* My Worlds Toggle */}
            <div className="flex items-center space-x-2">
              <Switch
                id="my-worlds"
                checked={showMyWorlds}
                onCheckedChange={handleShowMyWorldsChange}
              />
              <Label
                htmlFor="my-worlds"
                className="text-nilo-text-secondary text-sm"
              >
                My worlds
              </Label>
            </div>

            {/* Owner Filter */}
            {!showMyWorlds && (
              <div className="flex items-center space-x-2">
                <Label className="text-nilo-text-secondary text-sm whitespace-nowrap">
                  Owner
                </Label>
                <Select
                  value={ownerFilter}
                  onValueChange={(value) => updateFilter("owner", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select user" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All users</SelectItem>
                    {userProfiles.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.data().displayName} ({user.id} -{" "}
                        {user.data().username || "?"})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Public Worlds Toggle */}
            <div className="flex items-center space-x-2">
              <Switch
                id="public-only"
                checked={showOnlyPublic}
                onCheckedChange={(checked) => updateFilter("public", checked)}
              />
              <Label
                htmlFor="public-only"
                className="text-nilo-text-secondary text-sm"
              >
                Public only
              </Label>
            </div>

            {/* Featured Worlds Toggle */}
            <div className="flex items-center space-x-2">
              <Switch
                id="featured-only"
                checked={showOnlyFeatured}
                onCheckedChange={(checked) => updateFilter("featured", checked)}
              />
              <Label
                htmlFor="featured-only"
                className="text-nilo-text-secondary text-sm"
              >
                Featured only
              </Label>
            </div>

            {/* Clear Filters Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSearchParams(new URLSearchParams());
              }}
              className="ml-auto"
            >
              Clear
            </Button>
          </div>
        </div>

        {/* Create World Button */}
        <div className="flex justify-center">
          <ActionButton variant="primary" onClick={() => createWorld({})}>
            Create New World
          </ActionButton>
        </div>

        {/* Worlds Table */}
        <div className="bg-surface-primary rounded-lg border border-accessories-divider overflow-x-auto">
          {initialLoading ? (
            <div className="text-center py-12">
              <Typography.Body level={2} color="light">
                Loading worlds...
              </Typography.Body>
            </div>
          ) : worlds.length === 0 ? (
            <div className="text-center py-12">
              <Typography.Body level={2} color="light">
                {showMyWorlds
                  ? "No worlds created yet"
                  : "No worlds found matching your filters"}
              </Typography.Body>
            </div>
          ) : (
            <Table className="w-full table-fixed">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-24">Status</TableHead>
                  <TableHead className="w-48">Name & ID</TableHead>
                  <TableHead className="w-32">Owner</TableHead>
                  <TableHead className="w-24">Score</TableHead>
                  <TableHead className="w-24">Likes</TableHead>
                  <TableHead className="w-24">Time</TableHead>
                  <TableHead className="w-32">Created</TableHead>
                  <TableHead className="w-40">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {worlds.map((world) => (
                  <WorldRow key={world.id} worldId={world.id} userId={userId} />
                ))}
              </TableBody>
            </Table>
          )}

          {/* Loading indicator for pagination */}
          {loading && (
            <div className="text-center py-4">
              <Typography.Body level={2} color="light">
                Loading more worlds...
              </Typography.Body>
            </div>
          )}

          {/* End of results indicator */}
          {!hasMore && worlds.length > 0 && (
            <div className="text-center py-4">
              <Typography.Body level={2} color="light">
                No more worlds to load
              </Typography.Body>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

const WorldPlaySessionsSection = ({ worldId }: { worldId: string }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [user] = useAuthState(auth);
  const userId = user?.uid;

  // Query for play sessions for this world
  const playSessionsQuery = queryT(
    worldPlaySessionsCollection,
    whereT("worldId", "==", worldId),
    orderByT("startedAt", "desc"),
    limit(50)
  );
  const playSessions = useFirebaseQuery(playSessionsQuery);

  const formatPlaySessionDate = (
    timestamp: DbWorldPlaySession["startedAt"]
  ) => {
    if (!timestamp) return "N/A";
    return new Date(timestamp.toDate()).toLocaleString();
  };

  const getSessionStatus = (session: DbWorldPlaySession) => {
    return session.endedAt ? "Completed" : "Active";
  };

  const getSessionStatusColor = (session: DbWorldPlaySession) => {
    return session.endedAt ? "bg-green-500" : "bg-yellow-500";
  };

  return (
    <div className="bg-nilo-fill-secondary rounded-lg p-4">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full text-left"
      >
        <Typography.Heading
          level={3}
          color="light"
          weight="medium"
          className="mb-0"
        >
          World Play Sessions ({playSessions.length})
        </Typography.Heading>
        <span className="text-nilo-text-secondary">
          {isExpanded ? "▼" : "▶"}
        </span>
      </button>

      {isExpanded && (
        <div className="mt-4">
          {playSessions.length === 0 ? (
            <div className="text-center py-8">
              <Typography.Body level={2} color="light">
                No play sessions found for this world
              </Typography.Body>
            </div>
          ) : (
            <div className="bg-surface-primary rounded-lg border border-accessories-divider overflow-x-auto">
              <Table className="min-w-full">
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-32">User ID</TableHead>
                    <TableHead className="w-24">Status</TableHead>
                    <TableHead className="w-32">Session ID</TableHead>
                    <TableHead className="w-32">Play Time</TableHead>
                    <TableHead className="w-40">Started</TableHead>
                    <TableHead className="w-40">Ended</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {playSessions.map((session) => {
                    const data = session.data();
                    const isCurrentUser = data.userId === userId;

                    return (
                      <TableRow
                        key={session.id}
                        className={`${isCurrentUser ? "bg-blue-50/10" : ""} hover:bg-gray-50/5`}
                      >
                        <TableCell className="font-medium w-32">
                          <div className="flex items-center gap-2 min-w-0">
                            <span className="text-brand truncate">
                              {data.userId}
                            </span>
                            {isCurrentUser && (
                              <Badge
                                variant="secondary"
                                className="text-xs flex-shrink-0"
                              >
                                You
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="w-24">
                          <Badge className={getSessionStatusColor(data)}>
                            {getSessionStatus(data)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground w-32">
                          <span
                            className="truncate block font-mono text-xs"
                            title={session.id}
                          >
                            {session.id}
                          </span>
                        </TableCell>
                        <TableCell className="text-sm w-32">
                          <Typography.Body level={2} color="light">
                            {formatPlayTime(data.playTimeSeconds)}
                          </Typography.Body>
                        </TableCell>
                        <TableCell className="text-sm w-40">
                          <span className="text-xs">
                            {formatPlaySessionDate(data.startedAt)}
                          </span>
                        </TableCell>
                        <TableCell className="text-sm w-40">
                          <span className="text-xs">
                            {data.endedAt
                              ? formatPlaySessionDate(data.endedAt)
                              : "-"}
                          </span>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const WorldDetails = ({
  worldId,
  currentUserId,
}: {
  worldId: string;
  currentUserId: string | undefined;
}) => {
  const [user] = useAuthState(auth);
  const userId = user?.uid;
  const world = useStreamDocumentById(worldsCollection, worldId).value?.data();
  const score = useStreamDocumentById(
    worldScoresCollection,
    worldId
  ).value?.data();
  const liked = useStreamDocument(
    userId ? worldUserRelationDoc({ kind: "like", worldId, userId }) : undefined
  ).value?.exists();
  const isOwner = currentUserId && world?.ownerId === currentUserId;
  const navigate = useNavigate();

  const {
    isDeleting,
    showDeleteConfirm,
    handleDeleteWorld,
    openDeleteConfirm,
    closeDeleteConfirm,
  } = useDeleteWorld({
    onSuccess: () => {
      // Navigate back to worlds list after successful deletion
      navigate("/internal/worlds");
    },
  });

  if (!world) {
    return (
      <div className="text-center py-12">
        <Typography.Body level={2} color="light">
          Loading world details...
        </Typography.Body>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* World Header */}
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <div>
            <Typography.Heading
              level={2}
              color="light"
              weight="medium"
              className="mb-2"
            >
              {world.name || "Unnamed World"}
            </Typography.Heading>
            <Typography.Label level={2} color="light" className="font-mono">
              <FirestoreDocumentUrl
                collection={DbCollections.worlds}
                id={worldId}
              >
                {worldId}
              </FirestoreDocumentUrl>
            </Typography.Label>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={world.isPublic ? "bg-green-500" : "bg-red-500"}>
              {world.isPublic ? "Public" : "Private"}
            </Badge>
            {world.isFeatured && (
              <Badge className="bg-yellow-500">Featured</Badge>
            )}
            {isOwner && <Badge className="bg-blue-500">Your World</Badge>}
          </div>
        </div>

        {/* Owner Information */}
        <div className="flex items-center gap-2">
          <UserIcon className="w-4 h-4" />
          <Typography.Label level={2} color="light">
            Owner:
          </Typography.Label>
          <WorldOwner worldId={worldId} />
        </div>

        {/* Parent World Information */}
        {world.fromWorldId && (
          <div className="space-y-2">
            <Typography.Label level={2} color="light" weight="medium">
              Cloned from:
            </Typography.Label>
            <div className="flex items-center gap-2">
              <Typography.Label level={3} color="light" className="font-mono">
                #{world.fromWorldId}
              </Typography.Label>
              <Typography.Label level={3} color="light">
                by
              </Typography.Label>
              <WorldOwner worldId={world.fromWorldId} />
            </div>
          </div>
        )}

        {/* Creation Date */}
        <div className="flex items-center gap-2">
          <Typography.Label level={2} color="light" weight="medium">
            Created:
          </Typography.Label>
          <Typography.Label level={2} color="light">
            {formatDate(world.createdAt)}
          </Typography.Label>
        </div>
      </div>

      {/* Stats Section */}
      {score && (
        <div className="bg-nilo-fill-secondary rounded-lg p-4">
          <Typography.Heading
            level={3}
            color="light"
            weight="medium"
            className="mb-4"
          >
            Statistics
          </Typography.Heading>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <Typography.Label level={2} color="light" weight="medium">
                Score
              </Typography.Label>
              <Typography.Body
                level={1}
                color="light"
                className="text-green-400 text-2xl"
              >
                {score.score}
              </Typography.Body>
            </div>
            <div className="text-center">
              <Typography.Label level={2} color="light" weight="medium">
                Likes
              </Typography.Label>
              <Typography.Body
                level={1}
                color="light"
                className="text-blue-400 text-2xl"
              >
                {score.likes}
              </Typography.Body>
            </div>
            <div className="text-center">
              <Typography.Label level={2} color="light" weight="medium">
                Total Play Time
              </Typography.Label>
              <Typography.Body
                level={1}
                color="light"
                className="text-yellow-400 text-2xl"
              >
                {formatPlayTime(score.totalPlayTimeSeconds)}
              </Typography.Body>
            </div>
          </div>
        </div>
      )}

      {/* Actions Section */}
      <div className="space-y-4">
        <Typography.Heading level={3} color="light" weight="medium">
          Actions
        </Typography.Heading>
        <div className="flex gap-4">
          {/* Play Actions */}
          <div className="space-y-2">
            <Typography.Label level={2} color="light" weight="medium">
              Play World
            </Typography.Label>
            <div className="flex gap-2">
              <Link to={`/play/${worldId}`}>
                <Button variant="primary" className="flex items-center gap-2">
                  <PlayIcon className="w-4 h-4" />
                  Play
                </Button>
              </Link>
              {world.liveblocksRoomId && (
                <Link to={`/${world.liveblocksRoomId}`}>
                  <Button
                    variant="secondary"
                    className="flex items-center gap-2"
                  >
                    <PlayIcon className="w-4 h-4" />
                    Play (Legacy)
                  </Button>
                </Link>
              )}
            </div>
          </div>

          {/* Social Actions */}
          <div className="space-y-2">
            <Typography.Label level={2} color="light" weight="medium">
              Social
            </Typography.Label>
            <div className="flex gap-2">
              <ActionButton
                variant="secondary"
                onClick={async () => {
                  if (!userId) {
                    throw new Error("User not found");
                  }
                  const worldUserRelationRef = worldUserRelationDoc({
                    kind: "like",
                    worldId,
                    userId,
                  });
                  if (liked) {
                    await deleteDoc(worldUserRelationRef);
                  } else {
                    await setDoc(worldUserRelationRef, {
                      createdAt: serverTimestamp(),
                      kind: "like",
                      worldId,
                      userId,
                    });
                  }
                }}
              >
                <div className="flex items-center gap-2">
                  {liked ? (
                    <HeartIcon className="w-4 h-4 text-red-500" />
                  ) : (
                    <HeartOffIcon className="w-4 h-4" />
                  )}
                  {liked ? "Unlike" : "Like"}
                </div>
              </ActionButton>

              <ActionButton
                variant="secondary"
                onClick={async () => {
                  await cloneWorld({ worldId });
                }}
              >
                <div className="flex items-center gap-2">
                  <CopyPlusIcon className="w-4 h-4" />
                  Clone
                </div>
              </ActionButton>
            </div>
          </div>

          <div className="space-y-2">
            <Typography.Label level={2} color="light" weight="medium">
              Delete World
            </Typography.Label>
            <div className="flex gap-2">
              <ActionButton
                variant="secondary"
                disabled={isDeleting || !isOwner}
                onClick={() => {
                  openDeleteConfirm();
                }}
              >
                <div className="flex items-center gap-2">
                  {isDeleting ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <TrashIcon className="w-4 h-4 text-red-500 stroke-red-500" />
                  )}
                  {isDeleting ? "Deleting..." : "Delete"}
                </div>
              </ActionButton>
            </div>
          </div>

          {/* Management Actions */}
          <div className="space-y-2">
            <Typography.Label level={2} color="light" weight="medium">
              Management
            </Typography.Label>
            <div className="flex gap-2">
              {isOwner && (
                <ActionButton
                  variant="secondary"
                  onClick={async () => {
                    await updateDoc(doc(worldsCollection, worldId), {
                      isPublic: !world.isPublic,
                    });
                  }}
                >
                  <div className="flex items-center gap-2">
                    <ShareIcon className="w-4 h-4" />
                    {world.isPublic ? "Make Private" : "Make Public"}
                  </div>
                </ActionButton>
              )}

              <ActionButton
                variant="secondary"
                onClick={async () => {
                  if (!userId) {
                    throw new Error("User not found");
                  }
                  const playTimeSeconds = Math.floor(Math.random() * 1000);
                  const session: WithFieldValue<DbWorldPlaySession> = {
                    worldId,
                    userId,
                    startedAt: serverTimestamp(),
                    updatedAt: serverTimestamp(),
                    playTimeSeconds,
                  };
                  if (Math.random() > 0.5) {
                    session.endedAt = serverTimestamp();
                  }
                  await addDoc(worldPlaySessionsCollection, session);
                }}
              >
                <div className="flex items-center gap-2">
                  <span className="text-lg">+</span>
                  Add Test Session
                </div>
              </ActionButton>
            </div>
          </div>
        </div>
      </div>

      {/* Technical Details */}
      <div className="bg-nilo-fill-secondary rounded-lg p-4">
        <Typography.Heading
          level={3}
          color="light"
          weight="medium"
          className="mb-4"
        >
          Technical Details
        </Typography.Heading>
        <div className="space-y-2">
          <div className="flex justify-between">
            <Typography.Label level={2} color="light">
              World ID:
            </Typography.Label>
            <Typography.Label level={2} color="light" className="font-mono">
              {worldId}
            </Typography.Label>
          </div>
          <div className="flex justify-between">
            <Typography.Label level={2} color="light">
              Owner ID:
            </Typography.Label>
            <Typography.Label level={2} color="light" className="font-mono">
              {world.ownerId}
            </Typography.Label>
          </div>
          {world.liveblocksRoomId && (
            <div className="flex justify-between">
              <Typography.Label level={2} color="light">
                Liveblocks Room ID:
              </Typography.Label>
              <Typography.Label level={2} color="light" className="font-mono">
                <LiveblocksRoomUrl roomId={world.liveblocksRoomId}>
                  {world.liveblocksRoomId}
                </LiveblocksRoomUrl>
              </Typography.Label>
            </div>
          )}
          <div className="flex justify-between">
            <Typography.Label level={2} color="light">
              Created:
            </Typography.Label>
            <Typography.Label level={2} color="light">
              {formatDate(world.createdAt)}
            </Typography.Label>
          </div>
          <div className="flex justify-between">
            <Typography.Label level={2} color="light">
              Updated:
            </Typography.Label>
            <Typography.Label level={2} color="light">
              {formatDate(world.updatedAt)}
            </Typography.Label>
          </div>
        </div>
      </div>

      {/* Liveblocks Room Data */}
      {world.liveblocksRoomId && (
        <RoomProvider
          id={world.liveblocksRoomId}
          initialPresence={{
            entities: {},
            frame: 0,
          }}
          initialStorage={{ entities: new LiveMap() }}
        >
          <LiveblocksRoomData />
        </RoomProvider>
      )}

      {/* World Play Sessions */}
      <WorldPlaySessionsSection worldId={worldId} />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        open={showDeleteConfirm}
        onOpenChange={closeDeleteConfirm}
        title="Delete World"
        description={`Are you sure you want to delete "${world?.displayName || world?.name || "Unnamed World"}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={async () => {
          if (currentUserId) {
            await handleDeleteWorld(worldId, currentUserId);
          }
        }}
        loading={isDeleting}
        variant="destructive"
      />
    </div>
  );
};

export function World() {
  const { id } = useParams();
  const [user] = useAuthState(auth);
  const userId = user?.uid;
  const { imgUrl, loading: imgLoading } = useWorldPreviewScreenshotSD(id || "");
  if (!id) {
    return (
      <div className="h-screen bg-black text-icon-primary overflow-y-auto">
        <div className="mx-auto space-y-6 pt-8 pb-24 max-w-7xl px-4">
          <Typography.Title level={1} color="light">
            World Not Found
          </Typography.Title>
          <Typography.Body level={1} color="light">
            No world ID provided.
          </Typography.Body>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-black text-icon-primary overflow-y-auto">
      <div className="mx-auto space-y-6 pt-8 pb-24 max-w-7xl px-4">
        {/* Header */}
        <header className="flex justify-between items-center">
          <div>
            <Typography.Title level={1} color="light" className="mb-2">
              Details
            </Typography.Title>
            <Typography.Body level={1} color="light" weight="medium">
              <FirestoreDocumentUrl collection={DbCollections.worlds} id={id}>
                {id}
              </FirestoreDocumentUrl>
            </Typography.Body>
          </div>
          <Link to="/internal/worlds">
            <Button variant="secondary">← Back to Worlds</Button>
          </Link>
        </header>

        {/* World Screenshot */}
        {imgUrl && (
          <div className="bg-surface-primary rounded-lg border border-accessories-divider p-6">
            <Typography.Title level={2} color="light" className="mb-4">
              Screenshot
            </Typography.Title>
            <div className="relative aspect-[4/3] rounded-lg overflow-hidden">
              {imgLoading ? (
                <div className="w-full h-full bg-zinc-900 animate-pulse flex items-center justify-center">
                  <div className="text-icon-secondary">
                    Loading screenshot...
                  </div>
                </div>
              ) : (
                <img
                  src={imgUrl}
                  alt="World Screenshot"
                  className="w-full h-full object-cover"
                />
              )}
            </div>
          </div>
        )}

        {/* World Details Card */}
        <div className="bg-surface-primary rounded-lg border border-accessories-divider p-6">
          <WorldDetails worldId={id} currentUserId={userId} />
        </div>
      </div>
    </div>
  );
}
