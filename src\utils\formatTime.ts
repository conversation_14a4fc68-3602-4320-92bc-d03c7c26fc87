/**
 * Formats seconds into a human-readable time string
 * @param seconds - The number of seconds to format
 * @returns A formatted string like "5h 23m 45s" or "23m 45s" or "45s"
 */
export function formatTimeRemaining(seconds: number | null): string {
  if (seconds === null || seconds <= 0) {
    return "0s";
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  const parts: string[] = [];

  if (hours > 0) {
    parts.push(`${hours}h`);
  }
  if (minutes > 0) {
    parts.push(`${minutes}m`);
  }
  if (secs > 0 || parts.length === 0) {
    parts.push(`${secs}s`);
  }

  return parts.join(" ");
}

/**
 * Formats seconds into a more detailed human-readable time string
 * @param seconds - The number of seconds to format
 * @returns A formatted string like "5 hours, 23 minutes" or "23 minutes, 45 seconds"
 */
export function formatTimeRemainingDetailed(seconds: number | null): string {
  if (seconds === null || seconds <= 0) {
    return "any moment now";
  }

  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  const parts: string[] = [];

  if (days > 0) {
    parts.push(`${days} ${days === 1 ? "day" : "days"}`);
  }
  if (hours > 0) {
    parts.push(`${hours} ${hours === 1 ? "hour" : "hours"}`);
  }
  if (minutes > 0 && days === 0) {
    // Only show minutes if no days
    parts.push(`${minutes} ${minutes === 1 ? "minute" : "minutes"}`);
  }
  if (secs > 0 && days === 0 && hours === 0) {
    // Only show seconds if no days or hours
    parts.push(`${secs} ${secs === 1 ? "second" : "seconds"}`);
  }

  if (parts.length === 0) {
    return "any moment now";
  }

  // Join with comma and "and" for the last item
  if (parts.length === 1) {
    return parts[0];
  } else if (parts.length === 2) {
    return `${parts[0]} and ${parts[1]}`;
  } else {
    const lastPart = parts.pop();
    return `${parts.join(", ")}, and ${lastPart}`;
  }
}
