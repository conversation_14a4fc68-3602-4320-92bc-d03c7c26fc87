import {
  Color,
  ColorRepresentation,
  DoubleSide,
  InstancedMesh,
  InstancedMeshEventMap,
  LinearFilter,
  Matrix4,
  MeshBasicMaterial,
  PlaneGeometry,
  Texture,
  TextureLoader,
} from "three";

export class InstancedParticleMeshController {
  public readonly mesh: InstancedMesh<
    PlaneGeometry,
    MeshBasicMaterial,
    InstancedMeshEventMap
  >;
  private currentTextureUrl: string | null = null;

  public constructor(count: number) {
    const geometry = new PlaneGeometry(1, 1);

    const defaultTexture = new Texture(new Image());
    defaultTexture.needsUpdate = true;

    const material = new MeshBasicMaterial({
      color: new Color("#FFFFFF"),
      map: defaultTexture,
      transparent: true,
      side: DoubleSide,
      depthWrite: true,
      depthTest: true,
      alphaTest: 0.01,
      opacity: 1,
    });

    this.mesh = new InstancedMesh(geometry, material, count);
    this.mesh.frustumCulled = false;
  }

  public setMatrixAt(index: number, matrix: Matrix4) {
    this.mesh.setMatrixAt(index, matrix);
    this.mesh.instanceMatrix.needsUpdate = true;
  }

  public async setTextureUrl(textureUrl: string) {
    this.currentTextureUrl = textureUrl;

    try {
      const texture = await new TextureLoader().loadAsync(textureUrl);

      // Only update if this is still the current texture URL
      // This prevents race condition when you change the texture url
      // multiple times, before the previous texture is loaded.
      if (this.currentTextureUrl !== textureUrl) {
        return;
      }

      texture.minFilter = LinearFilter;
      texture.magFilter = LinearFilter;

      this.mesh.material.map = texture;
      this.mesh.material.needsUpdate = true;
    } catch (error) {
      console.warn(
        "🎯 ParticleSystem: Failed to load particle texture:",
        error
      );
    }
  }

  public setColor(color: ColorRepresentation) {
    this.mesh.material.color = new Color(color);
    this.mesh.material.needsUpdate = true;
  }

  public setOpacity(opacity: number) {
    this.mesh.material.opacity = opacity;
    this.mesh.material.needsUpdate = true;
  }

  public dispose() {
    this.mesh.parent?.remove(this.mesh);

    this.mesh.geometry.dispose();
    this.mesh.material.dispose();
  }
}
