import { BoltIcon as BoltIconSolid } from "@heroicons/react/24/solid";
import { type ComponentType, type MouseEvent, useState } from "react";

import { getDefaultWorldThumbnailOptions } from "./WorldThumbnailOptions";
import { defaultWorldPreviewScreenshotSD } from "@/hooks/useWorldPreviewScreenshotSD";
import { useNiloPlatformNavigate } from "@/platform/hook/useNiloPlatformNavigate";

export const NewWorldThumbnail = () => {
  const { navigateToCreateWorld } = useNiloPlatformNavigate();
  const [imgLoaded, setImgLoaded] = useState(false);
  const options = getDefaultWorldThumbnailOptions();

  const handleCreateWorld = (openInNewTabFlag = false) => {
    navigateToCreateWorld({ inNewTab: openInNewTabFlag });
  };

  const handleThumbnailClick = () => {
    handleCreateWorld(false);
  };

  const handleMiddleClick = (e: MouseEvent) => {
    if (e.button === 1) {
      handleCreateWorld(true);
    }
  };

  return (
    <div
      className={`group relative ${options.thumbnailAspectRatio} rounded-2xl overflow-hidden hover:shadow-lg transition-shadow duration-200`}
    >
      <button
        onClick={handleThumbnailClick}
        onMouseDown={handleMiddleClick}
        className="absolute inset-0 w-full h-full cursor-pointer transition-all duration-200"
      >
        <img
          src={defaultWorldPreviewScreenshotSD}
          alt="Create New World"
          onLoad={() => setImgLoaded(true)}
          className={`w-full h-full object-cover transition-opacity duration-700 ${imgLoaded ? "opacity-100" : "opacity-0"} group-hover:brightness-120 group-hover:saturate-150`}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
      </button>

      <div className="absolute top-2 left-2 flex items-center space-x-2 z-10">
        <LabelEyeCatch
          icon={BoltIconSolid}
          label="Start Fresh"
          color="text-black"
          bgColor="bg-nilo-green"
        />
      </div>

      <div className="absolute inset-0 flex flex-row h-full w-full p-4 pointer-events-none">
        <div className="flex flex-col justify-end flex-1 min-w-0">
          <div className="flex items-center">
            <h2 className="text-white font-semibold text-lg truncate drop-shadow mr-4">
              Create New World
            </h2>
          </div>
        </div>
      </div>
    </div>
  );
};

const LabelEyeCatch = ({
  icon: Icon,
  label,
  color = "text-white",
  bgColor = "bg-black/60",
}: {
  icon: ComponentType<{ className?: string }>;
  label: string;
  color?: string;
  bgColor?: string;
}) => (
  <div
    className={`flex items-center space-x-2 ${bgColor} rounded-full px-2 py-1`}
  >
    <Icon className={`${color} w-3 h-3`} />
    <span className={`${color} text-xs font-bold drop-shadow`}>{label}</span>
  </div>
);
