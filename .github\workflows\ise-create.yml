# This is run for release branches and pull requests to initialize their ISE:
# - it creates a database and deploys its rules and indexes
# - it deploys the functions
# - it creates a bucket and deploys its rules
# We don't need this for "main" and "prod" because they already have an environment
# Note: this workflow does not deploy the server. Currently the server is only deployed if it has changed.

# TODO: consider deploying the server here too

name: Create ISE
on:
  workflow_dispatch: {}
  pull_request:
    types: [opened, reopened]

concurrency:
  group: ise-create-${{ github.head_ref || github.ref_name }}

jobs:
  create_database_and_deploy_rules:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-gcp-auth
      - uses: ./.github/actions/setup-node
      - uses: ./.github/actions/get-isolated-environment
        id: isolated-env
        with:
          branch: ${{ github.head_ref || github.ref_name }}
          updateFirebaseJson: true
      - name: Create database
        id: create-database
        run: |
          if pnpm exec firebase firestore:databases:get '${{ steps.isolated-env.outputs.database }}' &> /dev/null; then
            echo "Database already exists"
            echo "created=false" >> $GITHUB_OUTPUT
          else
            echo "Creating database"
            pnpm exec firebase firestore:databases:create --location ${{ steps.isolated-env.outputs.databaseLocation }} ${{ steps.isolated-env.outputs.database }}
            echo "created=true" >> $GITHUB_OUTPUT
          fi
      - if: steps.create-database.outputs.created == 'true'
        name: Deploy firestore rules and indexes
        uses: ./.github/actions/run-single-command-with-retry
        with:
          command: pnpm exec firebase deploy --non-interactive --force --only firestore

  deploy_functions:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-gcp-auth
      - uses: ./.github/actions/setup-node
      - uses: ./.github/actions/get-isolated-environment
        id: isolated-env
        with:
          branch: ${{ github.head_ref || github.ref_name }}
          emitEnvCodeTo: serverless/functions/src/isolatedEnv.ts
      - name: Deploy functions
        uses: ./.github/actions/run-single-command-with-retry
        with:
          command: pnpm exec firebase deploy --non-interactive --force --debug --only functions:${{ steps.isolated-env.outputs.functions }}

  create_bucket_and_deploy_rules:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - id: auth
        uses: ./.github/actions/setup-gcp-auth
        with:
          token_format: access_token
      - uses: ./.github/actions/setup-node
      - uses: ./.github/actions/get-isolated-environment
        id: isolated-env
        with:
          branch: ${{ github.head_ref || github.ref_name }}
          updateFirebaseJson: true
      - uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: "nilo-technologies"
      - name: Create bucket
        id: create-bucket
        run: |
          if gcloud storage buckets describe gs://${{ steps.isolated-env.outputs.bucket }} &> /dev/null; then
            echo "Bucket already exists"
            echo "created=false" >> $GITHUB_OUTPUT
          else
            echo "Creating bucket"
            gcloud storage buckets create gs://${{ steps.isolated-env.outputs.bucket }} \
              --project=nilo-technologies \
              --location=eu
            gsutil cors set serverless/storage/cors.json gs://${{ steps.isolated-env.outputs.bucket }}

            echo "Adding Firebase support to bucket"
            curl -X POST \
              -H "Authorization: Bearer ${{ steps.auth.outputs.access_token }}" \
              -H "Content-Type: application/json" \
              "https://firebasestorage.googleapis.com/v1beta/projects/nilo-technologies/buckets/${{ steps.isolated-env.outputs.bucket }}:addFirebase" \
              --data-binary "{}"

            echo "created=true" >> $GITHUB_OUTPUT
          fi
      - if: steps.create-bucket.outputs.created == 'true'
        run: pnpm exec firebase target:apply storage ${{ steps.isolated-env.outputs.name }} ${{ steps.isolated-env.outputs.bucket }}
      - if: steps.create-bucket.outputs.created == 'true'
        name: Deploy storage rules
        uses: ./.github/actions/run-single-command-with-retry
        with:
          command: pnpm exec firebase deploy --non-interactive --force --only storage:${{ steps.isolated-env.outputs.name }}
