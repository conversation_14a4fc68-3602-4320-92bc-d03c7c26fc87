import { Vector2, Vector2Like } from "three";

const ZERO = new Vector2(0, 0);

const points = [
  new Vector2(), // left-top corner begin (bottom-left part)
  new Vector2(), // left-top corner (top-left part)
  new Vector2(), // left-top corner end (top-right part)

  new Vector2(), // right-top corner begin (top-left part)
  new Vector2(), // right-top corner (top-right part)
  new Vector2(), // right-top corner end (bottom-right part)

  new Vector2(), // right-bottom corner begin (top-right part)
  new Vector2(), // right-bottom corner (bottom-right part)
  new Vector2(), // right-bottom corner end (bottom-left part)

  new Vector2(), // left-bottom corner begin (bottom-right part)
  new Vector2(), // left-bottom corner (bottom-left part)
  new Vector2(), // left-bottom corner end (top-left part)
];

export const getArcAngle = (arcLength: number, radius: number) => {
  return arcLength / radius;
};

export const getArcLength = (angle: number, radius: number) => {
  return angle * radius;
};

interface SegmentOptions {
  /** Inner radius of the segment */
  innerRadius: number;

  /** Outer radius of the segment */
  outerRadius: number;

  /** Border radius of the segment */
  borderRadius: number;

  /** Whether the segment is centered or not */
  isCentered: boolean;
}

interface SegmentsGenerationOptions extends SegmentOptions {
  /** Total number of segments to generate */
  total: number;

  /** Gap between segments */
  gap: number;

  /** Start angle of the segments */
  startAngle: number;

  /** End angle of the segments */
  endAngle: number;
}

interface SegmentGenerationOptions extends SegmentOptions {
  /** Rotation of the segment, in degrees */
  angle: number;

  /** Angle of the segment, in degrees */
  arcAngle: number;

  /** Offset for the segment's position on the X axis */
  offsetX: number;

  /** Offset for the segment's position on the Y axis */
  offsetY: number;

  /** Gap between segments */
  gap: number;
}

export interface Segment {
  /** Path of the segment */
  path: string;

  /** Hover line path of the segment */
  hoverLinePath: string;

  /** Start angle of the segment */
  startAngle: number;

  /** End angle of the segment (useful for sub-level segments) */
  endAngle: number;

  /** Middle angle of the segment */
  middleAngle: number;

  /** Center of the segment */
  center: Vector2Like;

  /** Absolute center of the segment */
  absoluteCenter: Vector2Like;

  /** Difference between outer and inner radius */
  deltaRadius: number;
}

interface Segments {
  /** List of segments */
  segments: Segment[];

  /** Total number of segments */
  total: number;

  /** Size of the radial menu */
  size: Vector2Like;
}

interface WedgedPointsOptions {
  /** Total number of points */
  total: number;

  /** Gap between points */
  gap: number;

  /** Size of each point */
  size: number;

  /** Middle angle */
  angle: number;

  /** Radius of the arc to generate points on */
  radius: number;
}

export class RadialUtils {
  public static generateSegment(opts: SegmentGenerationOptions): Segment {
    const {
      angle,
      arcAngle,

      innerRadius,
      outerRadius,
      borderRadius,

      offsetX = 0,
      offsetY = 0,

      gap,

      isCentered,
    } = opts;

    // const angleRange = arcAngle * MathUtils.DEG2RAD;

    const g0 = getArcAngle(gap, innerRadius); // inner gap angle
    const g1 = getArcAngle(gap, innerRadius + borderRadius); // inner gap angle with border radius
    const g2 = getArcAngle(gap, outerRadius - borderRadius); // outer gap angle with border radius
    const g3 = getArcAngle(gap, outerRadius); // outer gap angle

    const a0 = arcAngle - g0; // inner arc angle without gap
    const a1 = arcAngle - g1; // inner arc angle with border radius
    const a2 = arcAngle - g2; // outer arc angle with border radius
    const a3 = arcAngle - g3; // outer arc angle without gap

    const ha0 = a0 / 2; // inner half arc angle without gap
    const ha1 = a1 / 2; // inner half arc angle with border radius
    const ha2 = a2 / 2; // outer half arc angle with border radius
    const ha3 = a3 / 2; // outer half arc angle without gap

    const ihab = ha0 - getArcAngle(borderRadius, innerRadius) / 2; // inner half arc angle with border radius
    const ohab = ha3 - getArcAngle(borderRadius, outerRadius) / 2; // outer half arc angle with border radius

    // CORNER 1
    points[0].set(
      (outerRadius - borderRadius) * Math.cos(-ha2),
      (outerRadius - borderRadius) * Math.sin(-ha2)
    );

    points[1].set(outerRadius * Math.cos(-ha3), outerRadius * Math.sin(-ha3));

    points[2].set(outerRadius * Math.cos(-ohab), outerRadius * Math.sin(-ohab));

    // CORNER 2
    points[3].set(outerRadius * Math.cos(ohab), outerRadius * Math.sin(ohab));

    points[4].set(outerRadius * Math.cos(ha3), outerRadius * Math.sin(ha3));

    points[5].set(
      (outerRadius - borderRadius) * Math.cos(ha2),
      (outerRadius - borderRadius) * Math.sin(ha2)
    );

    // CORNER 3
    points[6].set(
      (innerRadius + borderRadius) * Math.cos(ha1),
      (innerRadius + borderRadius) * Math.sin(ha1)
    );

    points[7].set(innerRadius * Math.cos(ha0), innerRadius * Math.sin(ha0));

    points[8].set(innerRadius * Math.cos(ihab), innerRadius * Math.sin(ihab));

    // CORNER 4
    points[9].set(innerRadius * Math.cos(-ihab), innerRadius * Math.sin(-ihab));

    points[10].set(innerRadius * Math.cos(-ha0), innerRadius * Math.sin(-ha0));

    points[11].set(
      (innerRadius + borderRadius) * Math.cos(-ha1),
      (innerRadius + borderRadius) * Math.sin(-ha1)
    );

    // Rotate points around the center based on the start angle
    const angleOffset = angle + arcAngle * 0.5 * Number(!isCentered); // + index * halfAngle * 2;
    // + halfAngle * Number(isCentered);

    points.forEach((point) => {
      point.rotateAround(ZERO, angleOffset);
      // point.x += outerRadius;
      // point.y += outerRadius;
      point.x += offsetX; // Apply X offset
      point.y += offsetY; // Apply Y offset
    });

    const path = [
      `M ${points[0].x} ${points[0].y}`,
      `Q ${points[1].x} ${points[1].y} ${points[2].x} ${points[2].y}`,
      `A ${outerRadius} ${outerRadius} 0 0 1 ${points[3].x} ${points[3].y}`,
      `Q ${points[4].x} ${points[4].y} ${points[5].x} ${points[5].y}`,
      `L ${points[6].x} ${points[6].y}`,
      `Q ${points[7].x} ${points[7].y} ${points[8].x} ${points[8].y}`,
      `A ${innerRadius} ${innerRadius} 0 0 0 ${points[9].x} ${points[9].y}`,
      `Q ${points[10].x} ${points[10].y} ${points[11].x} ${points[11].y}`,
      `L ${points[0].x} ${points[0].y}`,
    ].join(" ");

    const hoverLinePath = [
      `M ${points[1].x} ${points[1].y}`,
      `A ${outerRadius} ${outerRadius} 0 0 1 ${points[4].x} ${points[4].y}`,
    ].join(" ");

    const size = outerRadius - innerRadius;
    const radius = innerRadius + size / 2;

    const x = radius * Math.cos(angleOffset);
    const y = radius * Math.sin(angleOffset);

    return {
      path,
      hoverLinePath,

      startAngle: angle - arcAngle / 2,
      endAngle: angle + arcAngle / 2,
      middleAngle: angle,

      center: { x, y },
      absoluteCenter: { x: x + outerRadius, y: y + outerRadius },
      deltaRadius: outerRadius - innerRadius,
    };
  }

  public static generateSegments(opts: SegmentsGenerationOptions): Segments {
    const segments: Segment[] = [];

    const { startAngle, endAngle, gap } = opts;
    // const middleRadius = (opts.outerRadius + opts.innerRadius) / 2;
    // const gapAngle = getArcAngle(gap, middleRadius);
    const angleDt = (endAngle - startAngle) / opts.total; // Total angle divided by the number of segments

    for (let i = 0; i < opts.total; i++) {
      const angleOffset = startAngle + i * angleDt; // + i * gapAngle;

      const segment = RadialUtils.generateSegment({
        angle: angleOffset,
        arcAngle: angleDt, // - gapAngle,

        innerRadius: opts.innerRadius,
        outerRadius: opts.outerRadius,
        borderRadius: opts.borderRadius,

        offsetX: 0,
        offsetY: 0,

        gap,

        isCentered: opts.isCentered, // Whether the segment is centered or not
      });
      segments.push(segment);
    }

    return {
      segments,
      total: opts.total,
      size: {
        x: opts.outerRadius * 2,
        y: opts.outerRadius * 2,
      },
    };
  }

  public static generateWedgedPoints(opts: WedgedPointsOptions): Vector2Like[] {
    const points: Vector2Like[] = [];
    const { angle, radius, gap, size, total } = opts;

    const totalLength = total * (size + gap) - gap; // Total length of the points

    const totalAngle = getArcAngle(totalLength, radius); // Total angle of the points
    const angleDelta = totalAngle / total; // Angle step for each point

    const startAngle = angle - totalAngle / 2 + angleDelta / 2; // Start angle for the first point
    const angleStep = (totalAngle - angleDelta) / (total - 1); // Angle step for each point

    for (let i = 0; i < total; i++) {
      const point = new Vector2();
      const deltaAngle = startAngle + i * angleStep; // Calculate the angle for each point
      point.x = radius * Math.cos(deltaAngle);
      point.y = radius * Math.sin(deltaAngle);
      points.push(point);
    }

    return points;
  }
}
