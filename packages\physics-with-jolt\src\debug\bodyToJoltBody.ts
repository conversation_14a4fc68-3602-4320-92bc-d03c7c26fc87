import { Quaternion, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Vector3 } from "three";
import Jolt from "jolt-physics";
import { JoltBody, JoltShape, JoltShapeType } from "../types/debug";
import { BodyGeometry, BodyTransform } from "../types/Body";
import { ensureQuaternionLike } from "../webworker/webworker_utils";
import { PhysicsInvalidGeometryError } from "../types/errors";

const _position = new Vector3();
const _rotation = new Quaternion();
const _quaternion2 = new Quaternion();

export function bodyToJoltBody(
  body: Jolt.Body,
  joltModule: typeof Jolt,
  includeShape: boolean = true
): JoltBody {
  let motionType: "static" | "kinematic" | "dynamic";
  switch (body.GetMotionType()) {
    case joltModule.EMotionType_Static:
      motionType = "static";
      break;
    case joltModule.EMotionType_Kinematic:
      motionType = "kinematic";
      break;
    case joltModule.EMotionType_Dynamic:
      motionType = "dynamic";
      break;
    default:
      throw new Error("Invalid motion type"); // Should be impossible
  }

  const originalShape = getOriginalShape(body.GetShape(), joltModule);

  const transform = getBodyTransform(body, joltModule);

  // const centerOfMass = body.GetShape().GetCenterOfMass();
  // (transform.position as Vector3).add(
  //   new Vector3(centerOfMass.GetX(), centerOfMass.GetY(), centerOfMass.GetZ())
  // );
  const shape = includeShape
    ? buildJoltShape(originalShape, joltModule)
    : undefined;
  return {
    id: body.GetID().GetIndex(),
    transform: transform,
    type: "body",
    motionType: motionType,
    shape: shape,
  } as JoltBody;
}

function getOriginalShape(
  shape: Jolt.Shape,
  joltModule: typeof Jolt
): Jolt.Shape {
  //Go through decorated shapes inner shapes recursively
  if (shape.GetType() === joltModule.EShapeType_Decorated) {
    const decoratedShape = joltModule.castObject(
      shape,
      joltModule.DecoratedShape
    );
    return getOriginalShape(decoratedShape.GetInnerShape(), joltModule);
  }

  return shape;
}

function getShapeType(
  shape: Jolt.Shape,
  joltModule: typeof Jolt
): JoltShapeType {
  switch (shape.GetSubType()) {
    case joltModule.EShapeSubType_Box:
      return "box";
    case joltModule.EShapeSubType_Sphere:
      return "sphere";
    case joltModule.EShapeSubType_Capsule:
      return "capsule";
    case joltModule.EShapeSubType_Cylinder:
      return "cylinder";
    case joltModule.EShapeSubType_ConvexHull:
      return "convex";
    case joltModule.EShapeSubType_MutableCompound:
      return "mutable_compound";
    case joltModule.EShapeSubType_StaticCompound:
      return "static_compound";
    case joltModule.EShapeSubType_RotatedTranslated:
      return "rotated_translated";
    case joltModule.EShapeSubType_Scaled:
      return "scaled";
    case joltModule.EShapeSubType_OffsetCenterOfMass:
      return "offset_center_of_mass";
    case joltModule.EShapeSubType_Mesh:
      return "mesh";
    case joltModule.EShapeSubType_Empty:
      return "empty";
    default:
      console.error("Invalid shape type", shape.GetSubType());
      throw new Error(`Invalid shape type: ${shape.GetSubType()}`);
  }
}

export function buildJoltShape(
  shape: Jolt.Shape,
  joltModule: typeof Jolt
): JoltShape {
  const shapeType = getShapeType(shape, joltModule);
  switch (shapeType) {
    case "box": {
      const boxShape = joltModule.castObject(shape, joltModule.BoxShape);
      const extent = boxShape.GetHalfExtent();
      const size = new Vector3(
        extent.GetX() * 2,
        extent.GetY() * 2,
        extent.GetZ() * 2
      );
      return {
        type: "box",
        size: {
          x: size.x,
          y: size.y,
          z: size.z,
        },
      };
    }
    case "sphere": {
      const sphereShape = joltModule.castObject(shape, joltModule.SphereShape);
      const radius = sphereShape.GetRadius();
      return {
        type: "sphere",
        radius,
      };
    }
    case "capsule": {
      const capsuleShape = joltModule.castObject(
        shape,
        joltModule.CapsuleShape
      );
      const radius = capsuleShape.GetRadius();
      const height = 2 * capsuleShape.GetHalfHeightOfCylinder();
      return {
        type: "capsule",
        radius,
        height,
      };
    }
    case "cylinder": {
      const cylinderShape = joltModule.castObject(
        shape,
        joltModule.CylinderShape
      );
      const radius = cylinderShape.GetRadius();
      const height = 2 * cylinderShape.GetHalfHeight();
      return {
        type: "cylinder",
        radius,
        height,
      };
    }
    case "mesh": {
      return {
        type: "mesh",
        geometry: convertThreeJSGeometryToBodyGeometry(
          createMeshForShape(shape, joltModule),
          `shape_${shape.GetSubType()}`
        ),
      };
    }
    case "convex": {
      return {
        type: "convex",
        geometry: convertThreeJSGeometryToBodyGeometry(
          createMeshForShape(shape, joltModule),
          `shape_${shape.GetSubType()}`
        ),
      };
    }
    case "mutable_compound": {
      return {
        type: "mutable_compound",
        geometry: convertThreeJSGeometryToBodyGeometry(
          createMeshForShape(shape, joltModule),
          `shape_${shape.GetSubType()}`
        ),
      };
    }
    case "static_compound": {
      return {
        type: "static_compound",
        geometry: convertThreeJSGeometryToBodyGeometry(
          createMeshForShape(shape, joltModule),
          `shape_${shape.GetSubType()}`
        ),
      };
    }
    case "rotated_translated": {
      return buildJoltShape(
        joltModule
          .castObject(shape, joltModule.RotatedTranslatedShape)
          .GetInnerShape(),
        joltModule
      );
    }
    case "empty": {
      return {
        type: "empty",
        geometry: null,
      };
    }
    default: {
      throw new Error(`Invalid shape type for buildJoltShape(): ${shapeType}`);
    }
  }
}

function createMeshForShape(
  shape: Jolt.Shape,
  joltModule: typeof Jolt
): BufferGeometry {
  // Get triangle data
  const scale = new joltModule.Vec3(1, 1, 1);
  const triContext = new joltModule.ShapeGetTriangles(
    shape,
    joltModule.AABox.prototype.sBiggest(),
    shape.GetCenterOfMass(),
    joltModule.Quat.prototype.sIdentity(),
    scale
  );
  joltModule.destroy(scale);

  // Get a view on the triangle data (does not make a copy)
  const vertices = new Float32Array(
    joltModule.HEAPF32.buffer,
    triContext.GetVerticesData(),
    triContext.GetVerticesSize() / Float32Array.BYTES_PER_ELEMENT
  );

  // Now move the triangle data to a buffer and clone it so that we can free the memory from the C++ heap
  const buffer = new BufferAttribute(vertices, 3).clone();
  joltModule.destroy(triContext);

  // Create a three mesh
  const geometry = new BufferGeometry();
  geometry.setAttribute("position", buffer);
  geometry.computeVertexNormals();

  return geometry;
}

export function convertThreeJSGeometryToBodyGeometry(
  geometry: BufferGeometry,
  geometryId: string
): BodyGeometry {
  const positionAttribute = geometry.getAttribute("position");
  if (!positionAttribute) {
    throw new PhysicsInvalidGeometryError(
      "Geometry must have a position attribute"
    );
  }

  const vertices = new Float32Array(positionAttribute.array);
  const indices = geometry.index ? new Uint32Array(geometry.index.array) : null;

  // If no indices, create them (assuming triangles)
  let finalIndices: Uint32Array<ArrayBuffer>;
  if (indices) {
    finalIndices = indices;
  } else {
    // Create indices for non-indexed geometry (assuming triangles)
    const triangleCount = vertices.length / 9; // 3 vertices * 3 components per triangle
    finalIndices = new Uint32Array(triangleCount * 3);
    for (let i = 0; i < triangleCount; i++) {
      finalIndices[i * 3] = i * 3;
      finalIndices[i * 3 + 1] = i * 3 + 1;
      finalIndices[i * 3 + 2] = i * 3 + 2;
    }
  }

  return {
    geometryId,
    vertices: vertices.buffer,
    indices: finalIndices.buffer,
  };
}

export function getBodyTransform(
  body: Jolt.Body,
  joltModule: typeof Jolt
): BodyTransform {
  const position = body.GetPosition();
  const rotation = body.GetRotation();
  //const scale = getScale(body.GetShape(), joltModule);

  //Apply offsets and rotations to the position and rotation if it has decorated shapes

  const transform = {
    id: body.GetID().GetIndex(),
    position: {
      x: position.GetX(),
      y: position.GetY(),
      z: position.GetZ(),
    },
    rotation: {
      x: rotation.GetX(),
      y: rotation.GetY(),
      z: rotation.GetZ(),
      w: rotation.GetW(),
    },
    scale: { x: 1, y: 1, z: 1 }, //Scale is in decoratedShapes
  } as BodyTransform;

  applyDecoratedShapeChanges(body.GetShape(), joltModule, transform);

  return transform;
}

function applyDecoratedShapeChanges(
  shape: Jolt.Shape,
  joltModule: typeof Jolt,
  transform: BodyTransform
) {
  const applyScale = true;
  const applyRotation = true;
  const applyTranslation = true;

  if (shape.GetType() !== joltModule.EShapeType_Decorated) {
    return transform;
  }
  let decoratedShape: Jolt.DecoratedShape;
  if (shape.GetSubType() === joltModule.EShapeSubType_Scaled && applyScale) {
    const scaledShape = joltModule.castObject(shape, joltModule.ScaledShape);
    decoratedShape = scaledShape;
    transform.scale = {
      x: (transform.scale?.x ?? 1) * scaledShape.GetScale().GetX(),
      y: (transform.scale?.y ?? 1) * scaledShape.GetScale().GetY(),
      z: (transform.scale?.z ?? 1) * scaledShape.GetScale().GetZ(),
    };
  } else if (
    shape.GetSubType() === joltModule.EShapeSubType_RotatedTranslated
  ) {
    const rotatedTranslatedShape = joltModule.castObject(
      shape,
      joltModule.RotatedTranslatedShape
    );
    decoratedShape = rotatedTranslatedShape;

    if (applyTranslation) {
      const offset = _position
        .set(
          rotatedTranslatedShape.GetPosition().GetX(),
          rotatedTranslatedShape.GetPosition().GetY(),
          rotatedTranslatedShape.GetPosition().GetZ()
        )
        .applyQuaternion(transform.rotation);

      transform.position = {
        x: transform.position.x + offset.x,
        y: transform.position.y + offset.y,
        z: transform.position.z + offset.z,
      };
    }

    if (applyRotation) {
      _rotation.set(
        rotatedTranslatedShape.GetRotation().GetX(),
        rotatedTranslatedShape.GetRotation().GetY(),
        rotatedTranslatedShape.GetRotation().GetZ(),
        rotatedTranslatedShape.GetRotation().GetW()
      );
      _quaternion2.copy(transform.rotation);
      _quaternion2.multiply(_rotation);
      transform.rotation = ensureQuaternionLike(_quaternion2);
    }
  } else {
    decoratedShape = joltModule.castObject(shape, joltModule.DecoratedShape);
  }

  return applyDecoratedShapeChanges(
    decoratedShape.GetInnerShape(),
    joltModule,
    transform
  );
}
