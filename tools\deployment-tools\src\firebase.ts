import * as fs from "fs";
import * as path from "path";

export interface FirebaseProjectConfig {
  projects: {
    [alias: string]: string;
  };
  default?: string;
}

export interface FirebaseToolsConfig {
  activeProjects: {
    [directory: string]: string;
  };
}

/**
 * Determines the current Firebase project by checking multiple sources:
 * 1. .firebaserc file in current directory or parent directories
 * 2. Firebase CLI configuration file
 */
export function getCurrentFirebaseProject(): string | null {
  // First, try to find .firebaserc file in current directory or parent directories
  let currentDir = process.cwd();
  const rootDir = path.parse(currentDir).root;

  while (currentDir !== rootDir) {
    const firebasercPath = path.join(currentDir, ".firebaserc");

    if (fs.existsSync(firebasercPath)) {
      try {
        const content = fs.readFileSync(firebasercPath, "utf8");
        const config: FirebaseProjectConfig = JSON.parse(content);

        // Return the default project or the first project if no default is set
        if (config.default) {
          return config.projects[config.default];
        } else if (Object.keys(config.projects).length > 0) {
          return Object.values(config.projects)[0];
        }
      } catch (error) {
        console.warn(
          `Warning: Could not parse .firebaserc file at ${firebasercPath}`,
          {
            error: error instanceof Error ? error.message : String(error),
          }
        );
      }
    }

    currentDir = path.dirname(currentDir);
  }

  // If no .firebaserc found, try Firebase CLI configuration
  const configPath = path.join(
    process.env.HOME || process.env.USERPROFILE || "",
    ".config",
    "configstore",
    "firebase-tools.json"
  );

  if (fs.existsSync(configPath)) {
    try {
      const content = fs.readFileSync(configPath, "utf8");
      const config: FirebaseToolsConfig = JSON.parse(content);
      const activeProjects = config.activeProjects || {};
      const currentDir = process.cwd();

      // Find the most specific directory match
      let bestMatch = "";
      let projectId = "";

      for (const [dir, project] of Object.entries(activeProjects)) {
        if (currentDir.startsWith(dir) && dir.length > bestMatch.length) {
          bestMatch = dir;
          projectId = project;
        }
      }

      if (projectId) {
        return projectId;
      }
    } catch (error) {
      console.warn("Warning: Could not parse Firebase CLI configuration file", {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  return null;
}
