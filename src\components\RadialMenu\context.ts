import EventEmitter from "eventemitter3";
import { createContext, MouseEvent, useContext } from "react";
import { Vector2, Vector2Tuple } from "three";

export interface RadialItemEvents {
  /** Click event for the main menu button */
  click?: (event: MouseEvent) => void;
}

interface RadialMenuContextState {
  /** Events for main menu button */
  events: EventEmitter<RadialItemEvents>;

  /** Angle of the main menu button, in degrees */
  angle: number;

  /** Radius of the main menu button, e.g. outer radius of the main menu */
  radius: number;

  /** Whether the main menu button is hovered or not */
  isTriggerHovered: boolean;

  /** Function to close the submenu */
  close: () => void;

  /** Lock function to prevent closing the menu */
  lock: (value: boolean) => void;
}

export const RadialItemContext = createContext<RadialMenuContextState>({
  events: new EventEmitter(),
  angle: 0,
  radius: 0,
  isTriggerHovered: false,
  close: () => {},
  lock: () => {}, // Lock function to prevent closing the menu
});

export const RadialItemProvider = RadialItemContext.Provider;
export const useRadialItem = () => useContext(RadialItemContext);

interface RadialMenuContextData {
  setOpacity: (opacity: number) => void;
  close: () => void;
  closeSubmenu: () => void;
  lock: (isLocked: boolean) => void;
  isPointerOutside: (pointer: Vector2Tuple) => boolean;
  getScreenPosition: () => Vector2; // Function to get the screen position of the radial menu
}

const RadialMenuContext = createContext<RadialMenuContextData>({
  setOpacity: () => {},
  close: () => {},
  closeSubmenu: () => {},
  lock: () => {},
  isPointerOutside: () => false,
  getScreenPosition: () => new Vector2(),
});

export const RadialMenuProvider = RadialMenuContext.Provider;
export const useRadialMenu = () => useContext(RadialMenuContext);
