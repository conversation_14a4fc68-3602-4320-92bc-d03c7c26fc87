import { DocumentTextIcon } from "@heroicons/react/24/solid";
import { Bug, Keyboard } from "lucide-react";
import { type ReactNode } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useEditorUIActions } from "@/contexts/EditorUIContext";

export function HelpButton() {
  return (
    <HelpMenu>
      <Button
        variant="secondary"
        size="icon"
        className="fixed bottom-4 right-4 rounded-full flex items-center justify-center select-none bg-nilo-fill-tertiary"
      >
        <span className="text-2xl leading-none">?</span>
        <span className="sr-only">Help</span>
      </Button>
    </HelpMenu>
  );
}

export function HelpMenu({ children }: { children: ReactNode }) {
  const { toggleKeyboardShortcuts, toggleFeedback } = useEditorUIActions();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="bg-nilo-fill-tertiary border-nilo-border-secondary text-nilo-text-secondary"
        side="top"
      >
        <DropdownMenuItem onClick={() => toggleKeyboardShortcuts(true)}>
          <Keyboard className="mr-2 h-4 w-4" />
          Keyboard Shortcuts
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => toggleFeedback(true)}>
          <Bug className="mr-2 h-4 w-4" />
          Report an Issue
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() =>
            window.open(
              "https://shy-port-273.notion.site/Nilo-Getting-Started-Guide-26cc41fe2429802bbcb2f419f640a014",
              "_blank"
            )
          }
        >
          <DocumentTextIcon className="mr-2 h-4 w-4" />
          Creator Documentation
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
