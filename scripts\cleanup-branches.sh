#!/bin/bash

# Cleanup Branches Script
# Safely deletes all local branches matching a pattern
# Usage: ./scripts/cleanup-branches.sh <pattern> [--force] [--dry-run]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_help() {
    echo "Cleanup Branches Script"
    echo "======================="
    echo ""
    echo "This script safely deletes all local branches matching a pattern."
    echo ""
    echo "Usage:"
    echo "  $0 <pattern> [--force] [--dry-run] [--help]"
    echo ""
    echo "Parameters:"
    echo "  <pattern>   Branch pattern to match (e.g., 'release', 'fabio', 'feature')"
    echo ""
    echo "Options:"
    echo "  --force     Skip confirmation prompt and delete branches immediately"
    echo "  --dry-run   Show what would be deleted without actually deleting"
    echo "  --help, -h  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 release            # Delete all branches containing 'release'"
    echo "  $0 fabio --dry-run    # Preview deletion of branches containing 'fabio'"
    echo "  $0 feature --force    # Delete all 'feature' branches without confirmation"
    echo "  $0 hotfix             # Interactive deletion of 'hotfix' branches"
    echo ""
    echo "Pattern Matching:"
    echo "  • Matches branches containing the pattern anywhere in the name"
    echo "  • Examples: 'release' matches 'release/v1.0', 'my-release-branch'"
    echo "  • Examples: 'fabio' matches 'fabio/feature', 'feature-fabio'"
    echo ""
    echo "Safety:"
    echo "  • Only deletes branches matching the specified pattern"
    echo "  • Preserves current branch (switches away if needed)"
    echo "  • Shows detailed information before deletion"
    echo "  • Handles cases where no matching branches exist"
}

# Parse command line arguments
BRANCH_PATTERN=""
FORCE_DELETE=false
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --force)
            FORCE_DELETE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help|-h)
            print_help
            exit 0
            ;;
        -*)
            print_error "Unknown option: $1"
            echo ""
            print_help
            exit 1
            ;;
        *)
            if [ -z "$BRANCH_PATTERN" ]; then
                BRANCH_PATTERN="$1"
            else
                print_error "Too many arguments. Only one branch pattern is allowed."
                echo ""
                print_help
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required parameters
if [ -z "$BRANCH_PATTERN" ]; then
    print_error "Branch pattern is required."
    echo ""
    print_help
    exit 1
fi

# Validate pattern format
if [[ ! "$BRANCH_PATTERN" =~ ^[a-zA-Z0-9._/-]+$ ]]; then
    print_error "Invalid branch pattern. Use only letters, numbers, dots, hyphens, slashes, and underscores."
    exit 1
fi

echo "🧹 Cleanup Branches: '$BRANCH_PATTERN'"
echo "=================================="
echo ""

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "Not in a git repository"
    exit 1
fi

print_success "In git repository"

# Get current branch
CURRENT_BRANCH=$(git branch --show-current)
print_info "Current branch: $CURRENT_BRANCH"

# Find all local branches matching the pattern
MATCHING_BRANCHES=$(git branch --list | sed 's/^[* ] //' | grep "$BRANCH_PATTERN" | grep -v "^$" || true)

if [ -z "$MATCHING_BRANCHES" ]; then
    print_info "No local branches found matching pattern '$BRANCH_PATTERN'"
    echo ""
    echo "💡 Nothing to clean up!"
    exit 0
fi

# Count branches
BRANCH_COUNT=$(echo "$MATCHING_BRANCHES" | wc -l | tr -d ' ')

echo "📋 Found $BRANCH_COUNT branch(es) matching '$BRANCH_PATTERN':"
echo "$MATCHING_BRANCHES" | while read branch; do
    if [ -n "$branch" ]; then
        # Check if it's the current branch
        if [ "$branch" = "$CURRENT_BRANCH" ]; then
            echo "  • $branch (current branch)"
        else
            echo "  • $branch"
        fi
    fi
done

echo ""

# Dry run mode
if $DRY_RUN; then
    print_info "DRY RUN MODE - No branches will be deleted"
    echo ""
    echo "Would delete the following branches:"
    echo "$MATCHING_BRANCHES" | while read branch; do
        if [ -n "$branch" ]; then
            echo "  git branch -D $branch"
        fi
    done
    
    if echo "$MATCHING_BRANCHES" | grep -q "^$CURRENT_BRANCH$"; then
        echo ""
        print_warning "Current branch '$CURRENT_BRANCH' matches the pattern"
        echo "Would switch to 'main' or 'prod' before deletion"
    fi
    
    echo ""
    print_info "Run without --dry-run to actually delete these branches"
    exit 0
fi

# Check if current branch matches the pattern
SWITCH_NEEDED=false
if echo "$MATCHING_BRANCHES" | grep -q "^$CURRENT_BRANCH$"; then
    SWITCH_NEEDED=true
    print_warning "Current branch '$CURRENT_BRANCH' matches the pattern '$BRANCH_PATTERN'"
    echo "Will switch to a safe branch before deletion"
fi

# Confirmation prompt (unless --force is used)
if ! $FORCE_DELETE; then
    echo "⚠️  This will permanently delete $BRANCH_COUNT branch(es) matching '$BRANCH_PATTERN'"
    echo ""
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Operation cancelled"
        exit 0
    fi
    echo ""
fi

# Switch away from matching branch if needed
if $SWITCH_NEEDED; then
    echo "🔄 Switching away from matching branch..."
    
    # Try to switch to main, then prod, then any other branch
    if git show-ref --verify --quiet refs/heads/main; then
        git checkout main
        print_success "Switched to main branch"
    elif git show-ref --verify --quiet refs/heads/prod; then
        git checkout prod
        print_success "Switched to prod branch"
    else
        # Find any branch that doesn't match the pattern
        OTHER_BRANCH=$(git branch --list | grep -v "$BRANCH_PATTERN" | head -n 1 | sed 's/^[* ] //' | tr -d ' ')
        if [ -n "$OTHER_BRANCH" ]; then
            git checkout "$OTHER_BRANCH"
            print_success "Switched to $OTHER_BRANCH branch"
        else
            print_error "No branches found that don't match pattern '$BRANCH_PATTERN'"
            print_error "Cannot delete current matching branch"
            exit 1
        fi
    fi
    echo ""
fi

# Delete matching branches
echo "🗑️  Deleting branches matching '$BRANCH_PATTERN'..."
DELETED_COUNT=0
FAILED_COUNT=0

echo "$MATCHING_BRANCHES" | while read branch; do
    if [ -n "$branch" ]; then
        if git branch -D "$branch" 2>/dev/null; then
            echo "  ✅ Deleted: $branch"
            DELETED_COUNT=$((DELETED_COUNT + 1))
        else
            echo "  ❌ Failed to delete: $branch"
            FAILED_COUNT=$((FAILED_COUNT + 1))
        fi
    fi
done

# Note: Due to subshell, we need to recount
REMAINING_BRANCHES=$(git branch --list | sed 's/^[* ] //' | grep "$BRANCH_PATTERN" | grep -v "^$" || true)
if [ -n "$REMAINING_BRANCHES" ]; then
    REMAINING_COUNT=$(echo "$REMAINING_BRANCHES" | wc -l | tr -d ' ')
else
    REMAINING_COUNT=0
fi
ACTUAL_DELETED=$((BRANCH_COUNT - REMAINING_COUNT))

echo ""
echo "📊 Summary:"
echo "==========="
echo "• Total branches matching '$BRANCH_PATTERN': $BRANCH_COUNT"
echo "• Successfully deleted: $ACTUAL_DELETED"

if [ $ACTUAL_DELETED -eq $BRANCH_COUNT ]; then
    print_success "All matching branches deleted successfully!"
else
    REMAINING=$((BRANCH_COUNT - ACTUAL_DELETED))
    print_warning "$REMAINING branch(es) could not be deleted"
    
    # Show remaining branches
    if [ -n "$REMAINING_BRANCHES" ] && [ "$REMAINING_COUNT" -gt 0 ]; then
        echo ""
        echo "Remaining branches matching '$BRANCH_PATTERN':"
        echo "$REMAINING_BRANCHES" | while read branch; do
            if [ -n "$branch" ]; then
                echo "  • $branch"
            fi
        done
    fi
fi

echo ""
print_info "Current branch: $(git branch --show-current)"

if [ $ACTUAL_DELETED -gt 0 ]; then
    echo ""
    echo "💡 Tip: Use 'git remote prune origin' to clean up remote tracking branches"
fi
