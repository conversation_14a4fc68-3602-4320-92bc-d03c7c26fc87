import { Vector3, Quaternion } from "three";
import { PromptBehavior } from "../../ecs/behaviors/prompt";
import { TransformBehavior } from "../behaviors";
import { Behavior, EntityId, Prefab } from "@nilo/ecs";

/**
 * Helper function to create a prompt entity prefab with common behaviors
 */
export function createPromptEntityPrefab(options: {
  position?: Vector3;
  rotation?: Quaternion;
  scale?: Vector3;
  prompt?: string;
  userId: EntityId;
  color?: number;
  isStatic?: boolean;
}): Prefab {
  const {
    position,
    rotation,
    scale,
    prompt = "",
    userId,
    color = 0xffffff,
    isStatic = false,
  } = options;

  const behaviors: Behavior[] = [
    // Prevent accidental reference sharing across frames
    new TransformBehavior(position?.clone(), rotation?.clone(), scale?.clone()),
    new PromptBehavior(prompt, userId, color, isStatic),
  ];

  return new Prefab("promptEntity", behaviors);
}
