import { SelectedPromptEntityUI } from "./SelectedPromptEntityUI";
import { EntityType } from "@/liveblocks.config";
import { Client } from "@/core/client";
import { GameEntity } from "@/core/entity";

export function PromptEntityUI({ id }: { id: string }) {
  const entity = Client.getEntity(id) as GameEntity;

  if (!entity) {
    return null;
  }

  if (entity.type !== EntityType.PromptEntity) {
    console.warn(`PromptEntityUI: entity ${id} is not a PromptEntity`);
    return null;
  }

  return <SelectedPromptEntityUI id={id} />;
}
