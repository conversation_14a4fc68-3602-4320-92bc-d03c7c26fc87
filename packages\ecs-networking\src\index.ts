import { ByteStream<PERSON>eader, ByteStreamWriter, Schema } from "@evenstar/byteform";
import { encode as encodeCBO<PERSON>, decode as decode<PERSON>BO<PERSON> } from "cbor-x";
import {
  Component,
  System,
  SystemContext,
  World,
  Query,
  EntityId,
} from "@nilo/ecs";
import { ComponentKey } from "@nilo/ecs/archetype";

/** A schema that may be either a Byteform schema or a CBOR schema */
export class FriendlySchema<T> {
  /** An optional Byteform schema. CBOR will be used if not present. */
  private readonly _schema?: Schema<T>;

  /** Creates a new schema that may be either a Byteform schema or a CBOR schema */
  constructor(schema?: Schema<T>) {
    this._schema = schema;
  }

  /** Getter for Byteform schema, if available. */
  public get byteformSchema(): Schema<T> | undefined {
    return this._schema;
  }

  /** Helper method to directly serialize data into a ByteStreamWriter */
  public serialize(writer: ByteStreamWriter, data: T): void {
    if (this._schema) {
      writer.writeSchema(this._schema, data);
    } else {
      writer.writeBytes(encodeCBOR(data));
    }
  }

  /**
   * Helper method to directly serialize to Uint8Array
   * @note This method is faster than serialize() because it avoids the overhead of managing a ByteStreamWriter
   */
  public serializeBytes(data: T): Uint8Array {
    if (this._schema) {
      writeBuf.reset();
      writeBuf.writeSchema(this._schema, data);
      return writeBuf.commit();
    } else {
      return encodeCBOR(data);
    }
  }

  /** Helper method to directly deserialize data from a ByteStreamReader */
  public deserializeBytes(data: Uint8Array): T {
    if (this._schema) {
      const reader = new ByteStreamReader(data);
      return reader.readSchema(this._schema);
    } else {
      return decodeCBOR(data);
    }
  }
}

/** Reusable buffer for message serialization throughout the library */
const writeBuf: ByteStreamWriter = new ByteStreamWriter(256, {
  maxByteLength: 64 * 1024, // 64KiB *should* be plenty
});

export class NetworkComponent<T, D> extends Component<T> {
  /** The set of all network components that have been defined. */
  public static readonly ALL_NETWORK_COMPONENTS = new Set<
    NetworkComponent<unknown, unknown>
  >();

  /** Conversion method to create serialized data from a component. */
  readonly toData: (self: T) => D;

  /** Conversion method to create a component from its serialized data. */
  readonly fromData: (data: D) => T;

  /** Schema for network data (the D type). */
  readonly schema: FriendlySchema<D>;

  /** If set to true, always eagerly serializes even if no changes are detected. */
  public eagerlySerialize: boolean = false;

  constructor(
    name: string,
    toData: (self: T) => D,
    fromData: (data: D) => T,
    schema?: Schema<D>
  ) {
    super(name);
    this.schema = new FriendlySchema(schema);
    this.toData = toData;
    this.fromData = fromData;
    NetworkComponent.ALL_NETWORK_COMPONENTS.add(
      this as NetworkComponent<unknown, unknown>
    );
  }

  /**
   * Helper constructor for when the schema is of the component itself.
   */
  public static SelfData<T>(
    name: string,
    schema?: Schema<T>
  ): NetworkComponent<T, T> {
    return new NetworkComponent(
      name,
      (d) => d,
      (d) => d,
      schema
    );
  }

  /** Sets this component to eagerly serialize without change detection. */
  public withEagerlySerialize(): this {
    this.eagerlySerialize = true;
    return this;
  }

  /** Helper method to directly serialize a component into a ByteStreamWriter */
  public serialize(writer: ByteStreamWriter, component: T): void {
    return this.schema.serialize(writer, this.toData(component));
  }

  /**
   * Helper method to directly serialize to Uint8Array
   * @note This method is faster than serialize() because it avoids the overhead of managing a ByteStreamWriter
   */
  public serializeBytes(component: T): Uint8Array {
    return this.schema.serializeBytes(this.toData(component));
  }
}

export class EntitySerializationState {
  public components: Map<ComponentKey, ComponentSerializationState> = new Map();

  public setComponent(key: ComponentKey, data: Uint8Array): boolean {
    // look up component data
    const component = this.components.get(key);

    // if component is not available, init as dirty
    if (!component) {
      this.components.set(key, { dirty: true, data });
      return true;
    }

    // if data matches existing data, return as not dirty
    if (
      component.data &&
      component.data.length === data.length &&
      component.data.every((byte, index) => byte === data[index])
    ) {
      return false;
    }

    // otherwise, set new dirty contents
    component.dirty = true;
    component.data = data;
    return true;
  }

  public removeComponent(key: ComponentKey): boolean {
    const component = this.components.get(key);
    if (component && !component.data) {
      return false;
    } else {
      this.components.set(key, { dirty: true, data: undefined });
      return true;
    }
  }
}

export interface ComponentSerializationState {
  dirty: boolean;
  data?: Uint8Array;
}

export const EntitySerializationComponent =
  new Component<EntitySerializationState>(
    "entity_serialization"
  ).withDescription("Contains instantaneous network data for an entity.");

export interface ComponentSerialization {
  component: NetworkComponent<unknown, unknown>;
  query: Query<{ netComponent: NetworkComponent<unknown, unknown> }>;
}

/** ECS system implementing serialization of all NetworkComponents in a world. */
export class ComponentSerializeSystem implements System {
  name: string = "ComponentSerializeSystem";
  private _entitySerializationQuery = new Query([EntitySerializationComponent]);
  private _components: Map<ComponentKey, ComponentSerialization> = new Map();

  init(world: World, _ctx: SystemContext): void {
    // network components on registration
    world.onRegisterComponent(this.onRegisterComponent.bind(this));

    // register all currently-known network components
    for (const component of NetworkComponent.ALL_NETWORK_COMPONENTS) {
      world.registerComponent(component);
    }
  }

  run(world: World, _ctx: SystemContext): void {
    // track dirtiness across each entity's serialization state
    const dirtyEntities = new Set<EntityId>();

    // update entity serialization for all changed network components
    for (const serialization of this._components.values()) {
      serialization.query.forEach(world, (entity, { netComponent }) => {
        // get or initialize the entity's EntityState
        let state = entity.getComponent(EntitySerializationComponent);
        if (!state) {
          state = new EntitySerializationState();
          entity.addComponent(EntitySerializationComponent, state);
        }

        // serialize the network component's data
        const data = serialization.component.serializeBytes(netComponent);

        // insert the component data into entity state
        // track dirtiness to minimize change propagation
        if (state.setComponent(serialization.component.KEY, data)) {
          dirtyEntities.add(entity.id());
        }
      });
    }

    // remove components from all active serialized components
    this._entitySerializationQuery.forEach(world, (entity, [state]) => {
      // test if each component in the state has been removed
      for (const componentKey of state.components.keys()) {
        // look up networking component info for this state's component by ID
        const netComponent = this._components.get(componentKey);

        // silently ignore components on the state that we are not tracking
        // we should be tracking all network components, so this must be inserted manually
        if (!netComponent) {
          continue;
        }

        // if the entity does not have the component, remove it from the state
        if (!entity.hasComponent(netComponent.component)) {
          // track dirtiness to minimize change propagation
          if (state.removeComponent(componentKey)) {
            dirtyEntities.add(entity.id());
          }
        }
      }
    });

    // flush dirty entities to world's dirty tracking
    for (const entity of dirtyEntities) {
      world.markDirty(entity, EntitySerializationComponent);
    }
  }

  private onRegisterComponent(component: Component<unknown>) {
    // only handle NetworkComponent instances
    if (!(component instanceof NetworkComponent)) {
      return;
    }

    // create a query over this component
    const query = new Query({ netComponent: component });

    // unless eagerly serializing, track changes
    if (!component.eagerlySerialize) {
      query.trackModified();
    }

    // add new component serialization for this component
    this._components.set(component.KEY, {
      component,
      query,
    });
  }
}
