import { DeathE<PERSON>, OnDeath, OnRespawn, RespawnEvent } from "../events";
import { Client } from "@/core/client";
import {
  GameEntityComponent,
  HealthComponent,
  HealthStateComponent,
  PendingDamageBuffer,
} from "@/core/components";
import { PendingDamageElement } from "@/types/DealDamageData";
import { System, SystemContext, World, EntityData, Query } from "@nilo/ecs";

/**
 * DamageSystem processes pending damage and applies it to entity health
 *
 * For entities with both HealthComponent and PendingDamageComponent:
 * - Processes all pending damage amounts in the buffer
 * - Reduces health by total pending damage
 * - Clears the pending damage buffer
 */
export class HealthDamageSystem implements System {
  name = "HealthDamageSystem";
  entityFilter = [GameEntityComponent, HealthComponent, OnRespawn];

  // Query for iterating over entities with pending damage
  private damageQuery = new Query({
    gameEntity: GameEntityComponent,
    health: HealthComponent,
    pendingDamage: PendingDamageBuffer,
    onDeath: OnDeath,
  });

  onEntityAdded(
    world: World,
    entity: EntityData,
    systemContext: SystemContext
  ): void {
    entity.addComponent(PendingDamageBuffer, new Set<PendingDamageElement>());

    // Subscribe to respawn events to reset health
    const onRespawnHandler = (event: RespawnEvent) => {
      if (event.entity.id === entity.id()) {
        const health = entity.getComponent(HealthComponent);
        if (health) {
          systemContext.debug(
            `Resetting health for respawned entity ${entity.id()}`
          );
          world.setComponent(entity.id(), HealthComponent, {
            health: health.maxHealth,
            maxHealth: health.maxHealth,
          });
        }
      }
    };

    entity.addComponent(HealthStateComponent, { onRespawnHandler });

    // Subscribe to the respawn event using the delegate pattern
    const onRespawnComponent = entity.getComponent(OnRespawn)!;
    onRespawnComponent.on(onRespawnHandler);

    systemContext.debug("Entity added to HealthDamageSystem");
  }

  onEntityRemoved(
    _world: World,
    entity: EntityData,
    systemContext: SystemContext
  ): void {
    entity.removeComponent(PendingDamageBuffer);

    // Properly unsubscribe from respawn events using component storage
    const state = entity.getComponent(HealthStateComponent);
    if (state?.onRespawnHandler) {
      const onRespawnComponent = entity.getComponent(OnRespawn);
      if (
        onRespawnComponent &&
        onRespawnComponent.has(state.onRespawnHandler)
      ) {
        onRespawnComponent.off(state.onRespawnHandler);
      }
    }
    entity.removeComponent(HealthStateComponent);

    systemContext.debug("Entity removed from HealthDamageSystem");
  }

  // Run continuously to process pending damage
  run(world: World, systemContext: SystemContext): void {
    this.damageQuery.forEach(
      world,
      (entityData, { health, pendingDamage, onDeath }) => {
        // Skip if no pending damage
        if (pendingDamage.size === 0) {
          return;
        }

        const entityId = entityData.id();

        // Calculate total damage
        let totalDamage = 0;
        for (const element of pendingDamage) {
          totalDamage += element.damageAmount;
        }

        // Apply damage to health
        const newHealth = health.health - totalDamage;

        systemContext.debug(
          `Applying ${totalDamage} damage to entity ${entityId}: ${health} -> ${newHealth}`
        );

        // Update health component
        world.setComponent(entityId, HealthComponent, {
          health: newHealth,
          maxHealth: health.maxHealth,
        });

        // Clear pending damage buffer
        pendingDamage.clear();
        world.markDirty(entityId, PendingDamageBuffer);

        // Log if entity was destroyed
        if (newHealth <= 0) {
          systemContext.debug(`Entity ${entityData.id()} destroyed by damage`);
          onDeath.invoke(
            new DeathEvent(entityData, Client.clock.getElapsedTime())
          );
        }

        // todo: emit damage event
      }
    );
  }
}
