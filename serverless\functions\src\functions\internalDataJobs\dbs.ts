// eslint-disable-next-line no-restricted-imports -- used for pulling data from other ISEs
import { getFirestore } from "firebase-admin/firestore";
import { logger } from "firebase-functions/v2";
import { firestore_v1, google } from "googleapis";
import { z } from "zod";

const FirestoreDatabaseSchema = z
  .object({
    /**
     * The name of the database.
     * @example "projects/nilo-technologies/databases/(default)"
     * @example "projects/nilo-technologies/databases/pr115"
     */
    name: z.string(),
  })
  .passthrough();
type FirestoreDatabase = z.infer<typeof FirestoreDatabaseSchema>;

const FirestoreDatabasesResponseSchema = z
  .object({
    databases: z.array(FirestoreDatabaseSchema),
  })
  .passthrough();

export type FirestoreDatabaseId = string | undefined;

export async function listFirestoreDatabases(): Promise<FirestoreDatabaseId[]> {
  const auth = new google.auth.GoogleAuth({
    scopes: ["https://www.googleapis.com/auth/cloud-platform"],
  });

  const authClient = await auth.getClient();
  const firestore = google.firestore({
    version: "v1",
    auth: authClient,
  } as firestore_v1.Options);

  const apiResponse = await firestore.projects.databases.list({
    parent: `projects/nilo-technologies`,
  });
  logger.debug("Firestore databases listed", apiResponse.data);
  const response = FirestoreDatabasesResponseSchema.parse(apiResponse.data);
  return response.databases.map(extractDatabaseIdFromDescription);
}

function extractDatabaseIdFromDescription(
  description: FirestoreDatabase
): FirestoreDatabaseId {
  const id = description.name.split("/").pop()!;
  return id === "(default)" ? undefined : id;
}

export function getFirestoreFromDatabaseId(
  databaseId: FirestoreDatabaseId | null
) {
  return databaseId === null ||
    databaseId === undefined ||
    databaseId === "(default)"
    ? getFirestore()
    : getFirestore(databaseId);
}
