/* eslint-disable react/prop-types */
import { proxy } from "valtio";
import {
  DrawIcon,
  // EraserIcon,
  // GrassIcon,
  // WallIcon,
  ExportIcon,
  FlameIcon,
  ImportIcon,
  PaintBrushIcon,
  PrimitiveIcon,
  PrimitiveIcons,
  StarsIcon,
  TextureIcon,
  WriteIcon,
} from "../icons";
import { RadialMenu, RadialSection } from "../RadialMenu";
import { radial } from "../modules";
import { useRadialMenu } from "../context";
import { ButtonsItem, RadialButtonsItem } from "../modules/ButtonsModule";
import { Client } from "@/core/client";
import { PrimitiveType } from "@/liveblocks.config";
import { runCommands } from "@/core/command";
import { ANGLE_STATIC } from "@/core/entity";
import SelectEntitiesCommand from "@/core/command/commands/selectEntities";
import { EntitySelectionMode } from "@/core/util/EntitySelectionMode";
import {
  openDoodle2DPanel,
  openLiveCanvasPanel,
} from "@/core/util/openDoodle2DPanel";
import { ExportFormat } from "@/core/util/export";
import { createPrimitiveEntityPrefab } from "@/core/client/helpers/createPrimitiveEntityPrefab";
import { SpawnPrefabCommand } from "@/core/command/commands";
import {
  trackCoreBuilderAction,
  ActionKey,
} from "@/utils/tracking/radialMenuUtils";

const PrimitiveButtonsProps: Omit<
  RadialButtonsItem,
  "type" | "Component" | "layers"
> = {
  offset: 20,
  angleOffset: 100,

  layersGap: 12,
  buttonsGap: 12,

  buttonSize: 72,
};

const buildCreateObject = (): RadialSection => {
  const createObjectItems: ButtonsItem[] = [
    {
      icon: <WriteIcon />,
      tooltip: "From text",
      onClick: () => {
        trackCoreBuilderAction(
          "global",
          "create",
          "generate_from_text",
          "create_object"
        );
        Client.userEntity.beginGenerate("text_to_model");
      },
    },
    {
      icon: <TextureIcon />,
      tooltip: "From image",
      onClick: () => {
        trackCoreBuilderAction(
          "global",
          "create",
          "generate_from_image",
          "create_object"
        );
        Client.userEntity.beginGenerate("image_to_model");
      },
    },
    {
      icon: <FlameIcon />,
      tooltip: "Particles",
      onClick: () => {
        trackCoreBuilderAction(
          "global",
          "create",
          "particles",
          "create_object"
        );
        Client.userEntity.createParticleEntity();
      },
    },
  ];

  return {
    icon: <StarsIcon />,
    title: "Create Object",

    item: radial.buttonsSet({
      layers: [createObjectItems],
      closeOnClick: true,
    }),
  };
};

/**
 * Robust mapping from PrimitiveType to ActionKey with comprehensive error handling
 * This ensures all primitive types are tracked and provides clear warnings for missing mappings
 */
function mapPrimitiveTypeToActionKey(primitiveType: PrimitiveType): ActionKey {
  // Complete mapping of all PrimitiveTypes to ActionKeys
  const primitiveToActionKeyMap: Record<PrimitiveType, ActionKey | null> = {
    None: null, // Special case - not a trackable primitive
    Cube: "cube",
    Sphere: "sphere",
    Cylinder: "cylinder",
    Cone: "cone",
    Pipe: "pipe",
    Plane: "plane",
    Torus: "torus",
    TorusKnot: "torus_knot",
    Ring: "ring",
    Capsule: "capsule",
    Wedge: "wedge",
    Octahedron: "octahedron",
    Tetrahedron: "tetrahedron",
  };

  const actionKey = primitiveToActionKeyMap[primitiveType];

  if (actionKey === null) {
    // Handle special cases like "None" that shouldn't be tracked
    console.debug(
      `📊 Tracking: Skipping tracking for primitive type "${primitiveType}" (not trackable)`
    );
    // Return a default action that won't break the tracking
    return "cube"; // Fallback to prevent tracking errors
  }

  if (actionKey === undefined) {
    // This indicates a new primitive type was added but not mapped
    console.error(
      `📊 Tracking: Missing mapping for primitive type "${primitiveType}". ` +
        `Please add it to primitiveToActionKeyMap in globalBuilder.tsx and to ActionKey type in radialMenuUtils.ts`
    );
    // Return a fallback to prevent tracking system from breaking
    return "cube"; // Default fallback
  }

  return actionKey;
}

interface PrimitiveItem {
  icon: string;
  type: PrimitiveType;
}

const buildPrimitives = (): RadialSection => {
  const primitives: PrimitiveItem[] = [
    {
      icon: PrimitiveIcons.cube,
      type: "Cube",
    },
    {
      icon: PrimitiveIcons.sphere,
      type: "Sphere",
    },
    {
      icon: PrimitiveIcons.cylinder,
      type: "Cylinder",
    },
    {
      icon: PrimitiveIcons.cone,
      type: "Cone",
    },
    {
      icon: PrimitiveIcons.tube,
      type: "Pipe",
    },
    {
      icon: PrimitiveIcons.plane,
      type: "Plane",
    },
    {
      icon: PrimitiveIcons.torus,
      type: "Torus",
    },
    {
      icon: PrimitiveIcons.knot,
      type: "TorusKnot",
    },
    {
      icon: PrimitiveIcons.ring,
      type: "Ring",
    },
    {
      icon: PrimitiveIcons.capsule,
      type: "Capsule",
    },
    {
      icon: PrimitiveIcons.wedge,
      type: "Wedge",
    },
    {
      icon: PrimitiveIcons.octo,
      type: "Octahedron",
    },
    // {
    //   icon: icons.primitives.????,
    //   type: "Tetrahedron",
    // },
  ];

  return {
    icon: <PrimitiveIcon />,
    title: "Create Primitive",

    reduceMenuOpacity: true,
    menuOpacityOutside: 0.25,

    item: radial.buttons({
      layers: [primitives.length],
      closeOnClick: true,
      onUnmount: () => {
        Client.userEntity.revertPrimitivePreviews(
          primitives.map((p) => p.type)
        );
      },
      Component: ({ itemIndex }) => {
        const { getScreenPosition } = useRadialMenu();

        return (
          <div
            onPointerEnter={() => {
              const ndc = getScreenPosition();
              ndc.x = (ndc.x / window.innerWidth) * 2 - 1;
              ndc.y = -(ndc.y / window.innerHeight) * 2 + 1;

              const ground = Client.raycastGround(ndc)[0];
              if (ground) {
                Client.userEntity.previewPrimitive(
                  primitives[itemIndex].type,
                  ground
                );
              }
            }}
            onPointerLeave={() => {
              Client.userEntity.revertPrimitive(primitives[itemIndex].type);
            }}
            onClick={() => {
              // Track primitive creation with robust mapping
              const primitiveType = primitives[itemIndex].type;
              const actionKey = mapPrimitiveTypeToActionKey(primitiveType);

              trackCoreBuilderAction(
                "global",
                "create",
                actionKey,
                "create_primitive"
              );

              const previewPrimitive = Client.scene.getObjectByName(
                primitives[itemIndex].type
              );
              const [position, vAngle] =
                Client.userEntity.getRadialMenuCoordinates();
              if (!position) return;
              previewPrimitive?.getWorldPosition(position);
              //TODO: take into account originType
              Client.userEntity.revertPrimitive(primitives[itemIndex].type);

              const prefab = createPrimitiveEntityPrefab({
                position: position.clone(),
                primitiveType: primitives[itemIndex].type,
                color: 0xffffff,
                userId: Client.userEntity.id,
                physicsUserData: {
                  physics: {
                    isStatic:
                      !Client.composer.options.enablePhysicsOnCreate ||
                      vAngle > ANGLE_STATIC,
                    isDisabled:
                      !Client.composer.options.enableCollidersOnCreate,
                    shapeType: Client.composer.getShapeOnCreate(),
                  },
                },
              });
              const createCommand = new SpawnPrefabCommand(prefab);
              createCommand.onSpawnCallback((entity) => {
                const id = entity.id();
                runCommands(
                  new SelectEntitiesCommand([id], EntitySelectionMode.REPLACE),
                  false
                );
              });

              runCommands(createCommand);
            }}
            className="w-full h-full overflow-hidden flex justify-center items-center border-solid border-2 border-nilo-fill-secondary hover:border-nilo-neutral-white active:opacity-25 rounded-full transition-opacity duration-200 ease-in-out"
          >
            <img
              src={primitives[itemIndex].icon}
              alt={`${primitives[itemIndex].type} Icon`}
              draggable="false"
            />
          </div>
        );
      },

      ...PrimitiveButtonsProps,
    }),
  };
};

const buildWorldTools = (): RadialSection => {
  const createObjectItems: ButtonsItem[] = [
    // Doodle
    {
      icon: <DrawIcon />,
      tooltip: "Live Canvas",
      onClick: () => {
        trackCoreBuilderAction("global", "create", "live_canvas");
        openLiveCanvasPanel();
      },
    },
    {
      icon: <DrawIcon />,
      tooltip: "Doodle",
      onClick: () => openDoodle2DPanel(),
    },
    // Builder tools
    /*
    {
      icon: <EraserIcon />,
      tooltip: "Eraser",
      onClick: () =>
        Client.userEntity.setCurrentAction(CurrentAction.BRUSHSTROKE_ERASER),
    },
    {
      icon: <WallIcon />,
      tooltip: "Walls",
      onClick: () =>
        Client.userEntity.setCurrentAction(CurrentAction.BRUSHSTROKE_WALL),
    },
    {
      icon: <GrassIcon />,
      tooltip: "Grass",
      onClick: () =>
        Client.userEntity.setCurrentAction(CurrentAction.BRUSHSTROKE_GRASS),
    },
    */
  ];

  return {
    icon: <PaintBrushIcon />,
    title: "Tools",

    item: radial.buttonsSet({
      layers: [createObjectItems],
      closeOnClick: true,
    }),
  };
};

const buildMore = (): RadialSection[] => {
  const items: ButtonsItem[] = [
    {
      tooltip: "Export",
      icon: <ExportIcon />,
      onClick: () => {
        //Export all
        Client.userEntity.export(ExportFormat.GLTF);
      },
    },
    {
      tooltip: "Import",
      icon: <ImportIcon />,
      onClick: () => {
        trackCoreBuilderAction("global", "create", "import_3d_objects");
        Client.userEntity.onClickImport();
      },
    },
  ];

  return items.map(
    (item) =>
      ({
        icon: item.icon,
        title: item.tooltip,
        onClick: item.onClick,
      }) as RadialSection
  );
};

export const buildGlobalRadialMenu = () => {
  const state = proxy<RadialSection[]>([
    buildPrimitives(),
    buildCreateObject(),
    buildWorldTools(),
    ...buildMore(),
  ]);

  return <RadialMenu sections={state} />;
};
