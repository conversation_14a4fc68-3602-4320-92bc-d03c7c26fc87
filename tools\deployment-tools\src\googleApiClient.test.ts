import { describe, it, expect } from "@jest/globals";
import { objectAgeSeconds } from "./googleApiClient";

describe("objectAgeSeconds", () => {
  const mockNow = new Date("2024-01-15T12:00:00.000Z");

  describe("when object has updateTime", () => {
    it("should return age in seconds based on updateTime", () => {
      const object = {
        updateTime: "2024-01-15T11:30:00.000Z", // 30 minutes ago
        createTime: "2024-01-15T10:00:00.000Z", // 2 hours ago
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(1800); // 30 minutes = 1800 seconds
    });

    it("should return age in seconds when only updateTime is present", () => {
      const object = {
        updateTime: "2024-01-15T11:45:00.000Z", // 15 minutes ago
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(900); // 15 minutes = 900 seconds
    });

    it("should handle fractional seconds correctly", () => {
      const object = {
        updateTime: "2024-01-15T11:59:29.500Z", // 30.5 seconds ago
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(30); // Should floor to 30 seconds
    });
  });

  describe("when object has only createTime (no updateTime)", () => {
    it("should return age in seconds based on createTime", () => {
      const object = {
        createTime: "2024-01-15T10:30:00.000Z", // 1.5 hours ago
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(5400); // 1.5 hours = 5400 seconds
    });

    it("should return age in seconds when createTime is much older", () => {
      const object = {
        createTime: "2024-01-14T12:00:00.000Z", // 24 hours ago
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(86400); // 24 hours = 86400 seconds
    });
  });

  describe("when object has neither updateTime nor createTime", () => {
    it("should return undefined", () => {
      const object = {};

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBeUndefined();
    });

    it("should return undefined when both times are null", () => {
      const object = {
        updateTime: null,
        createTime: null,
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBeUndefined();
    });

    it("should return undefined when both times are undefined", () => {
      const object = {
        updateTime: undefined,
        createTime: undefined,
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBeUndefined();
    });
  });

  describe("edge cases", () => {
    it("should handle very recent updates (less than 1 second)", () => {
      const object = {
        updateTime: "2024-01-15T11:59:59.500Z", // 0.5 seconds ago
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(0); // Should floor to 0 seconds
    });

    it("should handle future timestamps (negative age)", () => {
      const object = {
        updateTime: "2024-01-15T12:30:00.000Z", // 30 minutes in the future
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(-1800); // Negative 1800 seconds
    });

    it("should handle same timestamp as now", () => {
      const object = {
        updateTime: "2024-01-15T12:00:00.000Z", // Same as mockNow
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(0); // 0 seconds difference
    });
  });

  describe("alternative timestamp field names", () => {
    it("should use 'updated' field when present", () => {
      const object = {
        updated: "2024-01-15T11:30:00.000Z", // 30 minutes ago
        createTime: "2024-01-15T10:00:00.000Z", // 2 hours ago
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(1800); // Should use 'updated' field
    });

    it("should use 'timeCreated' field when present", () => {
      const object = {
        timeCreated: "2024-01-15T10:30:00.000Z", // 1.5 hours ago
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(5400); // Should use 'timeCreated' field
    });

    it("should prioritize 'updated' over 'timeCreated'", () => {
      const object = {
        updated: "2024-01-15T11:30:00.000Z", // 30 minutes ago
        timeCreated: "2024-01-15T10:00:00.000Z", // 2 hours ago
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(1800); // Should use 'updated' (more recent)
    });

    it("should prioritize 'updateTime' over 'updated'", () => {
      const object = {
        updateTime: "2024-01-15T11:30:00.000Z", // 30 minutes ago
        updated: "2024-01-15T11:00:00.000Z", // 1 hour ago
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(1800); // Should use 'updateTime' (more recent)
    });

    it("should prioritize 'createTime' over 'timeCreated'", () => {
      const object = {
        createTime: "2024-01-15T11:30:00.000Z", // 30 minutes ago
        timeCreated: "2024-01-15T10:00:00.000Z", // 2 hours ago
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(1800); // Should use 'createTime' (more recent)
    });

    it("should use the most recent timestamp when multiple are present", () => {
      const object = {
        updateTime: "2024-01-15T11:00:00.000Z", // 1 hour ago
        updated: "2024-01-15T11:30:00.000Z", // 30 minutes ago
        createTime: "2024-01-15T10:00:00.000Z", // 2 hours ago
        timeCreated: "2024-01-15T10:30:00.000Z", // 1.5 hours ago
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(1800); // Should use 'updated' (most recent)
    });
  });

  describe("type safety", () => {
    it("should handle objects with optional string properties", () => {
      const object: {
        createTime?: string | null;
        updateTime?: string | null;
      } = {
        updateTime: "2024-01-15T11:30:00.000Z",
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(1800);
    });

    it("should handle objects with null values", () => {
      const object = {
        updateTime: null,
        createTime: "2024-01-15T11:30:00.000Z",
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(1800); // Should use createTime since updateTime is null
    });

    it("should handle objects with all timestamp fields", () => {
      const object: {
        createTime?: string | null;
        updateTime?: string | null;
        timeCreated?: string | null;
        updated?: string | null;
      } = {
        updateTime: "2024-01-15T11:30:00.000Z",
        updated: "2024-01-15T11:00:00.000Z",
        createTime: "2024-01-15T10:30:00.000Z",
        timeCreated: "2024-01-15T10:00:00.000Z",
      };

      const result = objectAgeSeconds(object, mockNow);

      expect(result).toBe(1800); // Should use updateTime (most recent)
    });
  });
});
