import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { toast } from "react-hot-toast";
import { signInWithDiscordCode } from "@/utils/discordAuth";

export const DiscordCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const handleCallback = async () => {
      const code = searchParams.get("code");
      const error = searchParams.get("error");

      if (error) {
        console.error("Discord OAuth error:", error);
        toast.error("Discord authentication was cancelled or failed");
        navigate("/login", { replace: true });
        return;
      }

      if (!code) {
        console.error("No authorization code received");
        toast.error("Invalid authentication response");
        navigate("/login", { replace: true });
        return;
      }

      try {
        await signInWithDiscordCode(code);

        // Get the stored redirect destination
        const redirectTo = localStorage.getItem("discord_auth_redirect") || "/";
        localStorage.removeItem("discord_auth_redirect");

        toast.success("Successfully signed in with Discord!");
        navigate(redirectTo, { replace: true });
      } catch (error) {
        console.error("Discord auth error:", error);
        toast.error("Failed to sign in with Discord");
        navigate("/login", { replace: true });
      } finally {
        setIsLoading(false);
      }
    };

    handleCallback();
  }, [navigate, searchParams]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#1a1a1a] flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-white text-xl font-semibold mb-2">
            Completing Discord Authentication...
          </h2>
        </div>
      </div>
    );
  }

  return null;
};

export default DiscordCallback;
