import { Vector3, Quatern<PERSON> } from "three";
import { nanoid } from "nanoid/non-secure";
import { duplicateEntityToData } from "./DuplicateControlsInstantCommit";
import { duplicateControlsInstantBoundingBoxSize } from "./DuplicateControlsInstantBoundingBox";
import { beginTransaction } from "@/core/command/transaction";
import CreateEntityCommand from "@/core/command/commands/createEntity";
import { runCommands } from "@/core/command";
import { ScriptComponent } from "@/core/components";
import { GameEntity } from "@/core/entity";

const POSITION_CHANGE_THRESHOLD = 0.5;
// Percentage of bounding box size to use as threshold
const BOUNDING_BOX_THRESHOLD_RATIO = 0.5;

// Avoiding allocations
const _bboxSize = new Vector3();

export class DuplicateControlsWhileDragging {
  private static lastDuplicationPositions = new Map<string, Vector3>();
  /**
   * Duplicates entities at their current dragged positions while continuing to drag the originals
   * Only creates duplicates if entities have moved above a threshold since the last duplication
   */
  static commit(entities: GameEntity[]) {
    if (entities.length === 0) {
      return;
    }

    // Check if any entity has moved enough to warrant duplication
    let shouldDuplicate = false;
    const currentPositions = new Map<string, Vector3>();

    for (const entity of entities) {
      const currentPosition = new Vector3();
      entity.getPosition(currentPosition);
      currentPositions.set(entity.id, currentPosition);

      const lastPosition = this.lastDuplicationPositions.get(entity.id);
      if (lastPosition) {
        duplicateControlsInstantBoundingBoxSize(entity, _bboxSize);

        const entitySize = Math.max(_bboxSize.x, _bboxSize.y, _bboxSize.z);

        const dynamicThreshold = Math.max(
          POSITION_CHANGE_THRESHOLD,
          entitySize * BOUNDING_BOX_THRESHOLD_RATIO
        );

        if (currentPosition.distanceTo(lastPosition) >= dynamicThreshold) {
          shouldDuplicate = true;
        }
      } else {
        // First time duplicating this entity
        shouldDuplicate = true;
      }
    }

    if (!shouldDuplicate) {
      return;
    }

    const transaction = beginTransaction("duplicate-while-dragging");

    // Duplicate all entities in the selection exactly in place
    for (const entity of entities) {
      const entityData = duplicateEntityToData(entity);
      if (!entityData) {
        console.warn("Failed to get entity data for duplication");
        continue;
      }

      // Get exact current position to ensure no offset
      const currentPos = new Vector3();
      const currentQuaternion = new Quaternion();
      const currentScale = new Vector3();

      entity.getPosition(currentPos);
      entity.getOrientation(currentQuaternion);
      entity.getScale(currentScale);

      const command = new CreateEntityCommand(
        {
          ...entityData,
          id: nanoid(),
          // Override with exact current transform to ensure no offset
          positionX: currentPos.x,
          positionY: currentPos.y,
          positionZ: currentPos.z,
          orientationX: currentQuaternion.x,
          orientationY: currentQuaternion.y,
          orientationZ: currentQuaternion.z,
          orientationW: currentQuaternion.w,
          scaleX: currentScale.x,
          scaleY: currentScale.y,
          scaleZ: currentScale.z,
        },
        entity.data().getComponent(ScriptComponent)?.toString() ?? null
      );

      runCommands(command);

      // Update the last duplication position for this entity
      const currentPosition = currentPositions.get(entity.id);
      if (currentPosition) {
        this.lastDuplicationPositions.set(entity.id, currentPosition.clone());
      }
    }

    transaction.end();
  }

  static clearAllDuplicationPositions(): void {
    this.lastDuplicationPositions.clear();
  }
}
