import { useStorage } from "@liveblocks/react";
import React from "react";

import { <PERSON>uatern<PERSON>, Vector3 } from "three";
import { BasePanel } from "./BasePanel";
import { PhysicsShapeType } from "packages/physics-with-jolt/src/types/PhysicsOptions";

import {
  CharacterRunningIcon,
  CodeIcon,
  ControllerIcon,
  DuplicateIcon,
  ExportIcon,
  MaterialGlassIcon,
  MaterialIcon,
  MaterialWireframeIcon,
  ResetIcon,
  SetupIcon,
  TrashCanIcon,
  VehicleIcon,
} from "@/components/RadialMenu/icons";
import { ColorPicker } from "@/components/ui-nilo/ColorPicker";
import { PanelCollapsible } from "@/components/ui-nilo/PanelCollapsible";
import { Typography } from "@/components/ui-nilo/Typography";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { useEditorUIActions } from "@/contexts/EditorUIContext";
import { Client } from "@/core/client";
import { runCommands } from "@/core/command";
import {
  SetEntityOrientationCommand,
  SetEntityPositionCommand,
  SetEntityScaleCommand,
} from "@/core/command/commands";
import SetEntityColorCommand from "@/core/command/commands/setEntityColor";
import SetEntityDetailCommand from "@/core/command/commands/setEntityDetail";
import SetEntityMaterialCommand from "@/core/command/commands/setEntityMaterial";
import SetEntityOriginTypeCommand from "@/core/command/commands/setEntityOriginType";
import SetEntityPhysicsFlagsCommand from "@/core/command/commands/setEntityPhysicsFlags";
import SetEntityPhysicsShapeCommand from "@/core/command/commands/setEntityPhysicsShape";
import { beginTransaction } from "@/core/command/transaction";
import { selectedEntitiesDelete } from "@/core/commands/SelectedEntitiesDelete";
import { TriggerComponent } from "@/core/components";
import {
  EntityOriginTypes,
  GameEntity,
  MeshEntity,
  PrimitiveEntity,
} from "@/core/entity";
import { CurrentAction } from "@/core/util/CurrentAction";
import { ExportFormat } from "@/core/util/export";
import {
  MATERIAL_OPTIONS_BY_PRESET_NAME,
  MATERIAL_PRESET_NAMES,
  materialOptionsEquals,
  MaterialPresetName,
} from "@/core/util/MaterialUtils";
import { checkEntityForRig } from "@/core/util/modelGeneration/checkEntityForRig";
import { isEntityWithPhysics } from "@/core/util/setPhysicsStaticFlag";
import { entityTransformReset } from "@/core/util/TransformUtils";
import { GameEntityData } from "@/liveblocks.config";
import { canShapeTypeBeDynamic } from "@/physics/helpers/canBeDynamic";
import { useFeatureFlag } from "@/hooks/useFeatureFlags";

const allowDisablingInspector = true;

// Global state to remember which sections are open
const openSections = new Map<string, boolean>();

export function InspectorPanel({
  selectedEntities,
}: {
  selectedEntities: GameEntity[];
}) {
  const { toggleInspectorHidden } = useEditorUIActions();

  const Panel = ({ children }: { children: React.ReactNode }) => (
    <BasePanel
      title="Inspector"
      className="mb-14 h-auto"
      onClose={
        allowDisablingInspector ? () => toggleInspectorHidden(true) : undefined
      }
    >
      {children}
    </BasePanel>
  );

  if (selectedEntities.length <= 0) {
    return (
      <Panel>
        <div className="p-4 text-center">
          <Typography.Body level={2} className="text-nilo-text-secondary">
            No entities selected
          </Typography.Body>
        </div>
      </Panel>
    );
  }

  if (selectedEntities.length > 1) {
    return (
      <Panel>
        <div className="p-4 text-center">
          <Typography.Body level={2} className="text-nilo-text-secondary">
            Multiple entities selected
          </Typography.Body>
        </div>
      </Panel>
    );
  }

  const firstSelectedEntity = selectedEntities[0];
  if (!firstSelectedEntity) {
    return (
      <Panel>
        <div className="p-4 text-center">
          <Typography.Body level={2} className="text-nilo-text-secondary">
            Error: Selected entity is null-ish
          </Typography.Body>
        </div>
      </Panel>
    );
  }

  return (
    <Panel>
      <SingleEntityInspectorPanelContents
        selectedEntity={firstSelectedEntity}
      />
    </Panel>
  );
}

export function SingleEntityInspectorPanelContents({
  selectedEntity,
}: {
  selectedEntity: GameEntity;
}) {
  // Get current entity data from storage for the selected entity
  const currentEntityData = useStorage<GameEntityData | null>((root) => {
    const entityId = selectedEntity.id;
    return root.entities.get(entityId) || null;
  });

  if (!currentEntityData) {
    return (
      <div className="p-4 text-center">
        <Typography.Body level={2} className="text-nilo-text-secondary">
          <p>Entity data not found with id</p>
          <pre>{selectedEntity.id}</pre>
        </Typography.Body>
      </div>
    );
  }

  const entity = Client.getEntity(currentEntityData.id);
  if (!entity) {
    return (
      <div className="p-4 text-center">
        <Typography.Body level={2} className="text-nilo-text-secondary">
          <p>Entity not found with id</p>
          <pre>{selectedEntity.id}</pre>
          <p>in this world</p>
        </Typography.Body>
      </div>
    );
  }

  const title =
    currentEntityData.type ||
    (currentEntityData as { constructor?: { name: string } }).constructor
      ?.name ||
    "Unknown";

  const activePieces = pieces.filter((piece) => {
    if (
      piece.entityType &&
      !piece.entityType.includes(currentEntityData.type)
    ) {
      return false;
    }
    return true;
  });

  const groupedPieces = activePieces.reduce(
    (acc, piece) => {
      const sectionKey = piece.section || "null";
      const section = acc[sectionKey] || (acc[sectionKey] = []);
      section.push(piece);
      return acc;
    },
    {} as Record<string, InspectorPieceConfig[]>
  );

  return (
    <div className="space-y-4 text-nilo-text-secondary">
      <Separator />

      <Typography.Heading
        level={1}
        className="text-start text-xl text-nilo-text-secondary"
      >
        {title}
      </Typography.Heading>

      {Object.entries(groupedPieces).map(([sectionKey, pieces]) => {
        if (sectionKey === "null") {
          return (
            <div key="null-section">
              {pieces.map((piece, i) => (
                <piece.component
                  key={i}
                  data={currentEntityData}
                  entity={entity}
                />
              ))}
            </div>
          );
        }

        return (
          <React.Fragment key={sectionKey}>
            <Separator />
            <PanelCollapsible
              title={sectionKey}
              defaultOpen={openSections.get(sectionKey) ?? false}
              onOpenChange={(isOpen) => openSections.set(sectionKey, isOpen)}
            >
              {pieces.map((piece, pieceIndex) => (
                <piece.component
                  key={piece.name ?? pieceIndex}
                  data={currentEntityData}
                  entity={entity}
                />
              ))}
            </PanelCollapsible>
          </React.Fragment>
        );
      })}
    </div>
  );
}

type InspectorPieceConfig = {
  section: string | null;
  name?: string;
  component: React.FC<{
    data: GameEntityData;
    entity: GameEntity;
  }>;
  entityType?: GameEntityData["type"][];
};

const pieces: InspectorPieceConfig[] = [
  {
    section: null,
    component: LockToggle,
  },
  {
    section: null,
    component: ControlsButton,
  },
  {
    section: null,
    component: TransformSection,
  },
  {
    section: "Code",
    component: CodeSection,
  },
  {
    section: "Model",
    component: PivotPointControls,
  },
  {
    section: "Model",
    component: LODControls,
  },
  {
    section: "Model",
    component: RigControls,
  },
  {
    section: "Model",
    component: MaterialPicker,
  },
  {
    section: "Model",
    component: ColorPickerComponent,
  },
  {
    section: "Physics",
    component: PhysicsToggle,
  },
  {
    section: "Physics",
    component: ShapeTypeToggle,
  },
  {
    section: "Physics",
    component: TriggerToggle,
  },
  {
    section: "Nerd things",
    component: DebugSection,
  },
  {
    section: "Actions",
    component: ActionsSection,
  },
];

function LockToggle({ entity }: { entity: GameEntity }) {
  const handleLockChange = (checked: boolean) => {
    entity.setLocked(checked);
  };

  return (
    <PropertyRow
      label="Locked"
      value={
        <Switch
          checked={entity.getLocked()}
          onCheckedChange={handleLockChange}
        />
      }
    />
  );
}

function ControlsButton({ entity }: { entity: GameEntity }) {
  // Check if there are active controls (same logic as radial menu)
  if (Client.playerControls.hasActiveControls()) {
    if (Client.playerControls.getCurrentControlledEntity() === entity) {
      // Show "Remove" button if this entity is currently controlled
      return (
        <div className="space-y-2">
          <Button
            onClick={() => {
              Client.playerControls.destroyActivePlayerControls();
              Client.userEntity.deselectAllEntities();
            }}
            variant="destructive"
            className="w-full"
          >
            <ControllerIcon className="h-4 w-4 mr-2" />
            Remove Controls
          </Button>
        </div>
      );
    }
    // Don't show anything if another entity is controlled
    return null;
  }

  // Check if entity is rigged (same logic as radial menu)
  if (checkEntityForRig(entity)) {
    return (
      <div className="space-y-2">
        <Button
          onClick={() => {
            Client.playerControls.setActivePlayerControlsTypeTo(
              "character",
              entity
            );
            Client.userEntity.deselectAllEntities();
          }}
          variant="default"
          className="w-full"
        >
          <CharacterRunningIcon className="h-4 w-4 mr-2" />
          Take Control (Character)
        </Button>
      </div>
    );
  }

  // Check if entity is a MeshEntity (same logic as radial menu)
  if (entity instanceof MeshEntity) {
    return (
      <div className="space-y-2">
        <Button
          onClick={() => {
            Client.playerControls.setActivePlayerControlsTypeTo(
              "hovercar",
              entity
            );
            Client.userEntity.deselectAllEntities();
          }}
          variant="default"
          className="w-full"
        >
          <VehicleIcon className="h-4 w-4 mr-2" />
          Take Control (Hovercar)
        </Button>
      </div>
    );
  }

  // Don't show controls for other entity types
  return null;
}

function PivotPointControls({ data }: { data: GameEntityData }) {
  const handleOriginTypeChange = (originType: EntityOriginTypes) => {
    const transaction = beginTransaction("set-origin-type");
    runCommands([new SetEntityOriginTypeCommand(data.id, originType)]);
    transaction.end();
  };

  const currentOriginType =
    (data.originType as string) || EntityOriginTypes.NativeOrigin;

  return (
    <div className="space-y-2">
      <Typography.Label level={3} className="text-nilo-text-secondary">
        Pivot Point
      </Typography.Label>
      <ToggleGroup
        type="single"
        value={currentOriginType}
        onValueChange={handleOriginTypeChange}
        className="w-full"
      >
        <ToggleGroupItem
          value={EntityOriginTypes.NativeOrigin}
          className="flex-1"
          aria-label="Native Origin"
        >
          Native
        </ToggleGroupItem>
        <ToggleGroupItem
          value={EntityOriginTypes.GeometryCenter}
          className="flex-1"
          aria-label="Geometry Center"
        >
          Center
        </ToggleGroupItem>
        <ToggleGroupItem
          value={EntityOriginTypes.GeometryBottomCenter}
          className="flex-1"
          aria-label="Bottom Center"
        >
          Bottom
        </ToggleGroupItem>
      </ToggleGroup>
    </div>
  );
}

function LODControls({ entity }: { entity: GameEntity }) {
  // Only show LOD controls for MeshEntity with detail controller
  if (!("detailController" in entity) || !entity.detailController) {
    return null;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const meshEntity = entity as MeshEntity; // Type assertion for detail controller access
  const currentDetail = meshEntity.detailController.detail();
  const PRESETS = [0.02, 0.05, 0.1, 0.25, 0.5, 1];

  // Find the closest preset index
  const foundIndex = PRESETS.findIndex(
    (preset) => Math.abs(preset - currentDetail) < 0.01
  );
  const currentIndex = foundIndex !== -1 ? foundIndex : PRESETS.length - 1;

  const handleLODChange = (value: number[]) => {
    const detailValue = PRESETS[value[0]];
    const transaction = beginTransaction("set-entity-detail");
    runCommands([
      new SetEntityDetailCommand(entity.id, { detail: detailValue }),
    ]);
    transaction.end();
  };

  const formatLODValue = (detail: number) => {
    return `${Math.round(detail * 100)}%`;
  };

  const isRigged = checkEntityForRig(meshEntity);
  const isRigging = meshEntity.isRigging();
  if (isRigged || isRigging) {
    return null;
  }

  return (
    <div className="space-y-2">
      <Typography.Label level={3} className="text-nilo-text-secondary">
        Level of Detail
      </Typography.Label>
      <div className="space-y-2">
        <Slider
          value={[currentIndex]}
          onValueChange={handleLODChange}
          max={PRESETS.length - 1}
          min={0}
          step={1}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-nilo-text-secondary">
          <span>{formatLODValue(PRESETS[0])}</span>
          <span className="font-mono">{formatLODValue(currentDetail)}</span>
          <span>{formatLODValue(PRESETS[PRESETS.length - 1])}</span>
        </div>
      </div>
    </div>
  );
}

function RigControls({ entity }: { entity: GameEntity }) {
  const rigCharacterModelEnabled = useFeatureFlag("rigCharacterModel");

  // Only show rig controls for MeshEntity
  if (!(entity instanceof MeshEntity)) {
    return null;
  }

  const meshEntity = entity as MeshEntity;

  // Check if entity is already rigged (same logic as radial menu)
  const isRigged = checkEntityForRig(meshEntity);

  // Check if entity is currently rigging (same logic as radial menu)
  const isRigging = meshEntity.isRigging();

  // Only show rig controls if feature flag is enabled and entity is riggable
  if (!rigCharacterModelEnabled) {
    return null;
  }

  const handleRig = async () => {
    try {
      await meshEntity.rig();
    } catch (error) {
      console.error("Error rigging character", error);
    }
  };

  return (
    <div className="space-y-2">
      <Button
        onClick={handleRig}
        variant={isRigged || isRigging ? "secondary" : "default"}
        className="w-full"
        disabled={isRigged || isRigging}
      >
        <SetupIcon className="h-4 w-4 mr-2" />
        {isRigging ? "Rigging..." : isRigged ? "Rigged!" : "Rig Character"}
      </Button>
    </div>
  );
}

function MaterialPicker({ entity }: { entity: GameEntity }) {
  // Only show material picker for PrimitiveEntity or MeshEntity (same logic as radial menu)
  if (!(entity instanceof PrimitiveEntity || entity instanceof MeshEntity)) {
    return null;
  }

  const meshEntity = entity as MeshEntity | PrimitiveEntity;

  // Check if entity has material controller
  if (!("materialController" in meshEntity) || !meshEntity.materialController) {
    return null;
  }

  const materialIcons: Record<MaterialPresetName, React.ReactNode> = {
    Default: <MaterialIcon />,
    Glass: <MaterialGlassIcon />,
    Wireframe: <MaterialWireframeIcon />,
  };

  // Find current material preset by comparing current options with preset options
  //TODO: fix this so that we don't need to access a private property
  // @ts-ignore
  const currentOptions = meshEntity.materialController._materialOptions;
  const currentPreset =
    (currentOptions &&
      MATERIAL_PRESET_NAMES.find((presetName) =>
        materialOptionsEquals(
          currentOptions,
          MATERIAL_OPTIONS_BY_PRESET_NAME[presetName]
        )
      )) ||
    "Default"; // Fallback to Default if no match found

  const handleMaterialChange = (presetName: MaterialPresetName) => {
    const materialOptions = MATERIAL_OPTIONS_BY_PRESET_NAME[presetName];
    const transaction = beginTransaction("set-entity-material");
    runCommands([new SetEntityMaterialCommand(entity.id, materialOptions)]);
    transaction.end();
  };

  return (
    <div className="space-y-2">
      <Typography.Label level={3} className="text-nilo-text-secondary">
        Material
      </Typography.Label>
      <Select value={currentPreset} onValueChange={handleMaterialChange}>
        <SelectTrigger>
          <SelectValue>
            <div className="flex items-center gap-2">
              {materialIcons[currentPreset as MaterialPresetName]}
              <span>{currentPreset}</span>
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {MATERIAL_PRESET_NAMES.map((presetName) => (
            <SelectItem key={presetName} value={presetName}>
              <div className="flex items-center gap-2">
                {materialIcons[presetName]}
                <span>{presetName}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}

function ColorPickerComponent({ entity }: { entity: GameEntity }) {
  if (!(entity instanceof PrimitiveEntity)) {
    return null;
  }

  // Get current color and convert to hex string
  const currentColor = entity.getColor();
  // Handle case where getColor() might return undefined or null
  const safeColor = currentColor ?? 0xffffff; // Default to white if undefined
  const colorHex = "#" + safeColor.toString(16).padStart(6, "0");

  const handleColorChange = (colorValue: string) => {
    // Convert hex string to number (same logic as radial menu)
    const colorNumber = Number("0x" + colorValue.slice(1));
    const transaction = beginTransaction("set-entity-color");
    runCommands([new SetEntityColorCommand(entity.id, colorNumber)]);
    transaction.end();
  };

  return (
    <div className="space-y-2">
      <Typography.Label level={3} className="text-nilo-text-secondary">
        Color
      </Typography.Label>
      <ColorPicker
        id={`color-picker-${entity.id}`}
        placeholder="Enter hex color"
        defaultColor={colorHex}
        value={colorHex}
        onChange={handleColorChange}
      />
    </div>
  );
}

function PhysicsToggle({ entity }: { entity: GameEntity }) {
  // Only show physics toggle for entities with physics (same logic as radial menu)
  if (!isEntityWithPhysics(entity)) {
    return null;
  }

  const bodyControl = entity.getPhysicsBodyControl();
  if (!bodyControl) {
    return null;
  }

  // Get current physics state (same logic as radial menu)
  const isStatic = bodyControl.getPhysicsOptions().isStatic;
  const isEnabled = !isStatic; // Physics is enabled when not static

  const handlePhysicsToggle = (enabled: boolean) => {
    const transaction = beginTransaction("set-entity-physics-flags");
    runCommands([new SetEntityPhysicsFlagsCommand(entity.id, !enabled)]);
    transaction.end();
  };

  return (
    <div className="space-y-2">
      <PropertyRow
        label="Physics"
        value={
          <Switch checked={isEnabled} onCheckedChange={handlePhysicsToggle} />
        }
      />
    </div>
  );
}

function ShapeTypeToggle({ entity }: { entity: GameEntity }) {
  // Only show shape type toggle for entities with physics (same logic as radial menu)
  if (!isEntityWithPhysics(entity)) {
    return null;
  }

  const bodyControl = entity.getPhysicsBodyControl();
  if (!bodyControl) {
    return null;
  }

  // Get current shape type (same logic as radial menu)
  const currentShapeType = bodyControl.getPhysicsOptions().shapeType;
  const isConvexHull = currentShapeType === "convexHull";
  const canBeDynamic = canShapeTypeBeDynamic(currentShapeType);

  const handleShapeTypeToggle = () => {
    const newShapeType: PhysicsShapeType = isConvexHull
      ? "decomp"
      : "convexHull";
    const transaction = beginTransaction("set-entity-physics-shape");
    runCommands([new SetEntityPhysicsShapeCommand(entity.id, newShapeType)]);
    transaction.end();
  };

  // Don't show if shape type cannot be dynamic (same logic as radial menu)
  if (!canBeDynamic) {
    return null;
  }

  return (
    <div className="space-y-2">
      <PropertyRow
        label="Shape Type"
        value={
          <Button
            onClick={handleShapeTypeToggle}
            variant="outline"
            size="sm"
            className="w-full"
          >
            {isConvexHull ? "Set Decomposition" : "Set Convex Hull"}
          </Button>
        }
      />
    </div>
  );
}

function TriggerToggle({ entity }: { entity: GameEntity }) {
  // Only show trigger toggle for MeshEntity or PrimitiveEntity (same logic as radial menu)
  if (!(entity instanceof MeshEntity || entity instanceof PrimitiveEntity)) {
    return null;
  }

  // Check if entity has trigger component (same logic as radial menu)
  const entityData = Client.world.entityData(entity.id);
  const hasTrigger = entityData?.hasComponent(TriggerComponent) ?? false;

  const handleTriggerToggle = (enabled: boolean) => {
    Client.room.batch(() => {
      if (entity instanceof MeshEntity || entity instanceof PrimitiveEntity) {
        entity.triggerSetup(enabled);
      }
    });
  };

  return (
    <div className="space-y-2">
      <PropertyRow
        label="Trigger"
        value={
          <Switch checked={hasTrigger} onCheckedChange={handleTriggerToggle} />
        }
      />
    </div>
  );
}

const POSITION_ZERO = new Vector3(0, 0, 0);
const QUATERNION_IDENTITY = new Quaternion().identity();
const SCALE_ONE = new Vector3(1, 1, 1);
function TransformSection({ data }: { data: GameEntityData }) {
  const formatVector3 = (x: number, y: number, z: number) => {
    return `(${x.toFixed(1)}, ${y.toFixed(1)}, ${z.toFixed(1)})`;
  };

  const formatQuaternion = (x: number, y: number, z: number, w: number) => {
    return `(${x.toFixed(1)}, ${y.toFixed(1)}, ${z.toFixed(1)}, ${w.toFixed(1)})`;
  };

  const resetPosition = () => {
    runCommands([new SetEntityPositionCommand(data.id, POSITION_ZERO)]);
  };

  const resetRotation = () => {
    runCommands([
      new SetEntityOrientationCommand(data.id, QUATERNION_IDENTITY),
    ]);
  };

  const resetScale = () => {
    runCommands([new SetEntityScaleCommand(data.id, SCALE_ONE)]);
  };

  const ResetButton = ({
    onClick,
    title,
  }: {
    onClick: () => void;
    title: string;
  }) => (
    <Button
      variant="secondary"
      size="sm"
      onClick={onClick}
      className="h-5 p-2"
      title={title}
    >
      Reset
      {/* <RotateCcw className="w-2 h-2" /> */}
    </Button>
  );

  return (
    <div>
      <Typography.Heading level={3} className="mb-3">
        Transform
      </Typography.Heading>
      <div className="space-y-2">
        <div className="p-1">
          <div className="space-y-1 text-xs">
            <div className="flex justify-between items-center">
              <span className="text-nilo-text-secondary">Position:</span>
              <div className="flex items-center gap-2">
                <span className="font-mono text-white/80">
                  {formatVector3(
                    data.positionX || 0,
                    data.positionY || 0,
                    data.positionZ || 0
                  )}
                </span>
                <ResetButton
                  onClick={resetPosition}
                  title="Reset position to (0, 0, 0)"
                />
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-nilo-text-secondary">Rotation:</span>
              <div className="flex items-center gap-2">
                <span className="font-mono text-white/80">
                  {formatQuaternion(
                    data.orientationX || 0,
                    data.orientationY || 0,
                    data.orientationZ || 0,
                    data.orientationW || 1
                  )}
                </span>
                <ResetButton
                  onClick={resetRotation}
                  title="Reset rotation to identity"
                />
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-nilo-text-secondary">Scale:</span>
              <div className="flex items-center gap-2">
                <span className="font-mono text-white/80">
                  {formatVector3(
                    data.scaleX || 1,
                    data.scaleY || 1,
                    data.scaleZ || 1
                  )}
                </span>
                <ResetButton
                  onClick={resetScale}
                  title="Reset scale to (1, 1, 1)"
                />
              </div>
            </div>
          </div>
        </div>
        {data.transformable !== undefined && (
          <PropertyRow
            label="Transformable"
            value={data.transformable ? "Yes" : "No"}
          />
        )}
      </div>
    </div>
  );
}

function CodeSection({ entity }: { entity: GameEntity }) {
  // Check if entity has a script (same logic as used in collaborative editor)
  const hasScript = Client.hasEntityScript(entity.id);
  const scriptText = hasScript
    ? Client.getEntityScript(entity.id).toString()
    : "";
  const hasScriptContent = hasScript && scriptText.trim().length > 0;

  const handleOpenEditor = () => {
    Client.openCollaborativeEditor(entity.id);
  };

  return (
    <div className="space-y-2">
      {hasScriptContent ? (
        <div
          className="p-2 bg-nilo-fill-secondary/20 rounded-lg border border-nilo-stroke/20 cursor-pointer transition-all duration-200 hover:border-nilo-stroke/60 hover:ring-2 hover:ring-nilo-border-primary/60 hover:bg-nilo-fill-secondary/30"
          onClick={handleOpenEditor}
        >
          <div className="text-[10px] font-mono text-nilo-text-secondary/60 overflow-hidden max-h-32 whitespace-pre leading-nilo-tight">
            {scriptText}
          </div>
        </div>
      ) : (
        <div className="text-xs text-nilo-text-tertiary italic">
          No code on this entity
        </div>
      )}

      <Button
        onClick={handleOpenEditor}
        variant={hasScriptContent ? "default" : "outline"}
        className="w-full"
      >
        <CodeIcon className="h-4 w-4 mr-2" />
        Open Code Editor
      </Button>
    </div>
  );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function DebugSection({ data }: { data: any }) {
  const formatColor = (color: number) => {
    const hex = color.toString(16).padStart(6, "0");
    return `#${hex}`;
  };

  return (
    <div>
      <div className="space-y-2">
        <PropertyRow label="ID" value={data.id} />

        {data.getPrompt && data.getPrompt() && data.getPrompt().trim() && (
          <div className="p-2 rounded">
            <Typography.Label
              level={3}
              className="text-nilo-text-secondary mb-1 block"
            >
              Prompt
            </Typography.Label>
            <Typography.Body
              level={2}
              className="whitespace-pre-wrap break-words"
            >
              {data.getPrompt()}
            </Typography.Body>
          </div>
        )}

        {data.tags && data.tags.length > 0 && (
          <div className="p-2 rounded">
            <Typography.Label
              level={3}
              className="text-nilo-text-secondary mb-1 block"
            >
              Tags
            </Typography.Label>
            <div className="flex flex-wrap gap-1">
              {data.tags.map((tag: string, index: number) => (
                <span
                  key={index}
                  className="px-2 py-1 text-nilo-text-primary text-xs rounded border border-nilo-stroke/20"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="space-y-2">
        {data.childrenIds?.length > 0 && (
          <PropertyRow label="Children" value={data.childrenIds.length} />
        )}
        {data.parentId && (
          <PropertyRow label="Parent ID" value={data.parentId} />
        )}
      </div>

      {data.modelData && (
        <div className="space-y-2">
          {data.modelData.url && (
            <div className="p-1">
              <Typography.Label
                level={3}
                className="text-nilo-text-secondary mb-1 block"
              >
                Model URL
              </Typography.Label>
              <Typography.Body level={2} className="font-mono text-xs">
                <a
                  href={data.modelData.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:text-blue-300 underline truncate block"
                  title={data.modelData.url}
                >
                  {data.modelData.url}
                </a>
              </Typography.Body>
            </div>
          )}
          {data.modelData.provider && (
            <PropertyRow
              label="Generation Provider"
              value={`${data.modelData.provider.name} ${data.modelData.provider.version}`}
            />
          )}
          {data.modelData.cacheKey && (
            <PropertyRow label="Cache Key" value={data.modelData.cacheKey} />
          )}
        </div>
      )}

      <div className="space-y-2">
        <PropertyRow
          label="Static"
          value={data.physicsUserData?.physics?.isStatic ? "Yes" : "No"}
        />
        <PropertyRow
          label="Disabled"
          value={data.physicsUserData?.physics?.isDisabled ? "Yes" : "No"}
        />
        <PropertyRow
          label="Shape Type"
          value={data.physicsUserData?.physics?.shapeType || "N/A"}
        />
        {!data.physicsUserData?.isUserImported &&
          data.physicsUserData?.isAIGenerated && (
            <PropertyRow label="AI Generated" value="Yes" />
          )}
        {data.physicsUserData?.isUserImported && (
          <PropertyRow label="User Imported" value="Yes" />
        )}
      </div>

      <div className="space-y-2">
        {data.color !== undefined && (
          <PropertyRow
            label="Color"
            value={
              <div className="flex items-center gap-2">
                <span>{formatColor(data.color)}</span>
                <div
                  className="w-4 h-4 rounded-full border border-nilo-stroke/30"
                  style={{ backgroundColor: formatColor(data.color) }}
                />
              </div>
            }
          />
        )}
        {data.primitiveType && (
          <PropertyRow label="Primitive" value={data.primitiveType} />
        )}
        {data.materialMetalness !== undefined && (
          <PropertyRow label="Metalness" value={data.materialMetalness} />
        )}
        {data.materialRoughness !== undefined && (
          <PropertyRow label="Roughness" value={data.materialRoughness} />
        )}
      </div>
    </div>
  );
}

function ActionsSection({ entity }: { entity: GameEntity }) {
  const handleExport = () => {
    Client.userEntity.export(ExportFormat.GLTF, [entity]);
  };

  const handleDuplicate = () => {
    Client.userEntity.setCurrentAction(CurrentAction.DUPLICATE_CONTROLS);
  };

  const handleResetTransform = () => {
    Client.room.batch(() => {
      entityTransformReset(entity);
    });
  };

  const handleDelete = () => {
    selectedEntitiesDelete();
  };

  return (
    <div className="space-y-2">
      <Button onClick={handleExport} variant="outline" className="w-full">
        <ExportIcon className="h-4 w-4 mr-2" />
        Export
      </Button>

      <Button
        onClick={handleDuplicate}
        variant="outline"
        className="w-full"
        ///TEMP: hidden until ux is better for this
        hidden={true}
      >
        <DuplicateIcon className="h-4 w-4 mr-2" />
        Duplicate
      </Button>

      <Button
        onClick={handleResetTransform}
        variant="outline"
        className="w-full"
        disabled={!entity.transformable}
      >
        <ResetIcon className="h-4 w-4 mr-2" />
        Reset Transform
      </Button>

      <Button onClick={handleDelete} variant="outline" className="w-full">
        <TrashCanIcon className="h-4 w-4 mr-2" />
        Delete
      </Button>
    </div>
  );
}

function PropertyRow({
  label,
  value,
}: {
  label: React.ReactNode;
  value: React.ReactNode;
}) {
  return (
    <div className="flex items-center justify-between p-1">
      <Typography.Label level={3} className="text-nilo-text-secondary" as="div">
        {label}
      </Typography.Label>
      <Typography.Body level={2} className="font-mono text-sm" as="div">
        {value}
      </Typography.Body>
    </div>
  );
}
