import {
  buildButtonsModule,
  buildButtonsSetModule,
  RadialButtonsItem,
  RadialButtonsModule,
} from "./ButtonsModule";
import {
  buildSliderModule,
  RadialSliderItem,
  RadialSliderModule,
} from "./SliderModule";
import {
  buildSwitchModule,
  RadialSwitchItem,
  RadialSwitchModule,
} from "./SwitchModule";
import { RadialItemType } from "./types";

export type RadialItem =
  | RadialButtonsItem
  | RadialSwitchItem
  | RadialSliderItem;

export const renderRadialItem = (item: RadialItem) => {
  switch (item.type) {
    case RadialItemType.Buttons:
      return <RadialButtonsModule item={item} />;
    case RadialItemType.Switch:
      return <RadialSwitchModule item={item} />;
    case RadialItemType.Slider:
      return <RadialSliderModule item={item} />;
    default:
      // Reflect.get to omit the TS error for unknown types
      throw new Error(`Unknown radial item type: ${Reflect.get(item, "type")}`);
  }
};

export const radial = {
  buttons: buildButtonsModule,
  buttonsSet: buildButtonsSetModule,
  slider: buildSliderModule,
  switch: buildSwitchModule,
};
