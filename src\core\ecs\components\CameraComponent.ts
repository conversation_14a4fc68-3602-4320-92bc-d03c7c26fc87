import { Component } from "@nilo/ecs";
import { CameraControls } from "@/core/util/controls/CameraControls";

/**
 * Component that holds camera controls for an entity.
 * Used by the CameraControlsSystem to update camera controls in the ECS loop.
 */
export const CameraComponent = new Component<CameraControls>(
  "camera_controls"
).withDescription("Contains camera controls for updating in the ECS loop");
