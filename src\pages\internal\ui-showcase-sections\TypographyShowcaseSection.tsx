import React, { useState } from "react";
import { toast } from "react-hot-toast";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Typography } from "@/components/ui-nilo/Typography";

const TypographyShowcaseSection = () => {
  const [titleText, setTitleText] = useState("Lorem ipsum dolor sit amet");
  const [bodyText, setBodyText] = useState(
    "The quick brown fox jumps over the lazy dog"
  );

  const titleHeadingComponents = [
    { Component: Typography.Title, name: "Title", level: 1 as const },
    { Component: Typography.Title, name: "Title", level: 2 as const },
    { Component: Typography.Title, name: "Title", level: 3 as const },
    { Component: Typography.Heading, name: "Heading", level: 1 as const },
    { Component: Typography.Heading, name: "Heading", level: 2 as const },
    { Component: Typography.Heading, name: "Heading", level: 3 as const },
    { Component: Typography.Heading, name: "Heading", level: 4 as const },
  ];

  const bodyLabelComponents = [
    { Component: Typography.Body, name: "Body", level: 1 as const },
    { Component: Typography.Body, name: "Body", level: 2 as const },
    { Component: Typography.Body, name: "Body", level: 3 as const },
    { Component: Typography.Body, name: "Body", level: 4 as const },
    { Component: Typography.Label, name: "Label", level: 1 as const },
    { Component: Typography.Label, name: "Label", level: 2 as const },
    { Component: Typography.Label, name: "Label", level: 3 as const },
    { Component: Typography.Label, name: "Label", level: 4 as const },
  ];

  const copyToClipboard = async (
    componentName: string,
    props: Record<string, unknown>
  ) => {
    const propString = Object.entries(props)
      .map(([key, value]) => `${key}="${value}"`)
      .join(" ");

    const componentCode = `<${componentName}${propString ? ` ${propString}` : ""}></${componentName}>`;

    try {
      await navigator.clipboard.writeText(componentCode);
      toast.success(componentCode);
    } catch (err) {
      console.error("🔴 Failed to copy to clipboard:", err);
      toast.error("❌ Failed to copy to clipboard");
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto space-y-8">
      {/* Controls Card */}
      <Card className="bg-[#1e1e1e] border-nilo-border-secondary">
        <CardHeader>
          <CardTitle className="text-white">Typography Controls</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="titleText" className="text-white">
                Title & Heading Text
              </Label>
              <Input
                id="titleText"
                value={titleText}
                onChange={(e) => setTitleText(e.target.value)}
                placeholder="Enter text for titles and headings"
                className="bg-nilo-fill-tertiary border-nilo-border-secondary text-white placeholder:text-nilo-text-placeholder"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="bodyText" className="text-white">
                Body & Label Text
              </Label>
              <Input
                id="bodyText"
                value={bodyText}
                onChange={(e) => setBodyText(e.target.value)}
                placeholder="Enter text for body and labels"
                className="bg-nilo-fill-tertiary border-nilo-border-secondary text-white placeholder:text-nilo-text-placeholder"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Title & Heading Components */}
      <Card className="bg-[#1e1e1e] border-nilo-border-secondary">
        <CardHeader>
          <CardTitle className="text-white">
            Title & Heading Components
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            {titleHeadingComponents.map(({ Component, name, level }) => (
              <div key={`${name}-${level}`} className="space-y-4">
                <h3 className="text-lg font-semibold text-white border-b border-nilo-border-secondary pb-2">
                  {name} {level}
                </h3>
                <div className="space-y-2">
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "bold",
                        color: "light",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level as 1 | 2 | 3}
                      weight="bold"
                      color="light"
                      title="Color = Light (default), Weight = Bold"
                    >
                      {titleText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "medium",
                        color: "light",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level as 1 | 2 | 3}
                      weight="medium"
                      color="light"
                      title="Color = Light (default), Weight = Medium"
                    >
                      {titleText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "bold",
                        color: "dark",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level as 1 | 2 | 3}
                      weight="bold"
                      color="dark"
                      title="Color = Dark, Weight = Bold"
                    >
                      {titleText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "medium",
                        color: "dark",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level as 1 | 2 | 3}
                      weight="medium"
                      color="dark"
                      title="Color = Dark, Weight = Medium"
                    >
                      {titleText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "bold",
                        color: "brand",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level as 1 | 2 | 3}
                      weight="bold"
                      color="brand"
                      title="Color = Brand, Weight = Bold"
                    >
                      {titleText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "medium",
                        color: "brand",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level as 1 | 2 | 3}
                      weight="medium"
                      color="brand"
                      title="Color = Brand, Weight = Medium"
                    >
                      {titleText}
                    </Component>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Body & Label Components */}
      <Card className="bg-[#1e1e1e] border-nilo-border-secondary">
        <CardHeader>
          <CardTitle className="text-white">Body & Label Components</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            {bodyLabelComponents.map(({ Component, name, level }) => (
              <div key={`${name}-${level}`} className="space-y-4">
                <h3 className="text-lg font-semibold text-white border-b border-nilo-border-secondary pb-2">
                  {name} {level}
                </h3>
                <div className="space-y-2 flex flex-col">
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "light",
                        color: "light",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level}
                      weight="light"
                      color="light"
                      title="Color = Light (default), Weight = Light"
                    >
                      {bodyText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "normal",
                        color: "light",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level}
                      weight="normal"
                      color="light"
                      title="Color = Light (default), Weight = Normal"
                    >
                      {bodyText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "medium",
                        color: "light",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level}
                      weight="medium"
                      color="light"
                      title="Color = Light (default), Weight = Medium"
                    >
                      {bodyText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "bold",
                        color: "light",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level}
                      weight="bold"
                      color="light"
                      title="Color = Light (default), Weight = Bold"
                    >
                      {bodyText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "light",
                        color: "dark",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level}
                      weight="light"
                      color="dark"
                      title="Color = Dark, Weight = Light"
                    >
                      {bodyText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "normal",
                        color: "dark",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level}
                      weight="normal"
                      color="dark"
                      title="Color = Dark, Weight = Normal"
                    >
                      {bodyText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "medium",
                        color: "dark",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level}
                      weight="medium"
                      color="dark"
                      title="Color = Dark, Weight = Medium"
                    >
                      {bodyText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "bold",
                        color: "dark",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level}
                      weight="bold"
                      color="dark"
                      title="Color = Dark, Weight = Bold"
                    >
                      {bodyText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "light",
                        color: "brand",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level}
                      weight="light"
                      color="brand"
                      title="Color = Brand, Weight = Light"
                    >
                      {bodyText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "normal",
                        color: "brand",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level}
                      weight="normal"
                      color="brand"
                      title="Color = Brand, Weight = Normal"
                    >
                      {bodyText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "medium",
                        color: "brand",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level}
                      weight="medium"
                      color="brand"
                      title="Color = Brand, Weight = Medium"
                    >
                      {bodyText}
                    </Component>
                  </div>
                  <div
                    onClick={() =>
                      copyToClipboard(`${name}${level}`, {
                        level,
                        weight: "bold",
                        color: "brand",
                      })
                    }
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                  >
                    <Component
                      level={level}
                      weight="bold"
                      color="brand"
                      title="Color = Brand, Weight = Bold"
                    >
                      {bodyText}
                    </Component>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TypographyShowcaseSection;
