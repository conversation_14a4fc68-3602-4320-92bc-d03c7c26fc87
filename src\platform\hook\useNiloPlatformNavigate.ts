import { useNavigateInSameTab } from "./useNavigateInSameTab";
import { navigateInNewTab } from "@/platform/utils/navigateInNewTab";

interface NiloPlatformNavigateOptions {
  inNewTab?: boolean;
  urlParams?: Record<string, string>;
}

export const useNiloPlatformNavigate = () => {
  const navigateInSameTab = useNavigateInSameTab();

  const navigateToWorld = (
    id: string,
    options: NiloPlatformNavigateOptions = {}
  ) => {
    const { inNewTab = false, urlParams } = options;

    if (inNewTab) {
      navigateInNewTab(`/play/${id}`, {
        urlParams,
        preserveUrlParams: true,
      });
    } else {
      navigateInSameTab(`/play/${id}`, {
        urlParams,
        preserveUrlParams: true,
      });
    }
  };

  const navigateToUserProfile = (
    userId: string,
    options: NiloPlatformNavigateOptions = {}
  ) => {
    const { inNewTab = false, urlParams } = options;

    if (inNewTab) {
      navigateInNewTab(`/user/${userId}`, {
        urlParams,
        preserveUrlParams: true,
      });
    } else {
      navigateInSameTab(`/user/${userId}`, {
        urlParams,
        preserveUrlParams: true,
      });
    }
  };

  const navigateToCreateWorld = (options: NiloPlatformNavigateOptions = {}) => {
    const { inNewTab = false, urlParams } = options;

    if (inNewTab) {
      navigateInNewTab("/create-world", {
        urlParams,
        preserveUrlParams: true,
      });
    } else {
      navigateInSameTab("/create-world", {
        urlParams,
        preserveUrlParams: true,
      });
    }
  };

  const navigateToCreateRemixedWorld = (
    remixSourceWorldId: string,
    options: NiloPlatformNavigateOptions = {}
  ) => {
    const { inNewTab = false, urlParams } = options;
    const remixParams = { remixFrom: remixSourceWorldId, ...urlParams };

    if (inNewTab) {
      navigateInNewTab("/create-world", {
        urlParams: remixParams,
        preserveUrlParams: true,
      });
    } else {
      navigateInSameTab("/create-world", {
        urlParams: remixParams,
        preserveUrlParams: true,
      });
    }
  };

  return {
    navigateToWorld,
    navigateToUserProfile,
    navigateToCreateWorld,
    navigateToCreateRemixedWorld,
  };
};
