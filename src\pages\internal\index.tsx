import { Navigate } from "react-router-dom";
import { Worlds, World } from "@/pages/internal/worlds";
import AuthGuard from "@/providers/auth-guard";
import InternalLayout from "@/pages/internal/InternalLayout";
import UIShowcase from "@/pages/internal/UIShowcase";
import TripoTasks from "@/pages/internal/TripoTasks";
import BuilderBagDebug from "@/pages/internal/BuilderBagDebug";
import GenerationTasks from "@/pages/internal/GenerationTasks";
import GenerationTaskStats from "@/pages/internal/GenerationTaskStats";
import EnvInfo from "@/pages/internal/EnvInfo";
import UserInfo from "@/pages/internal/UserInfo";
import DataJobs from "@/pages/internal/DataJobs";
import DataJob from "@/pages/internal/DataJob";
import NewDataJob from "@/pages/internal/NewDataJob";
import Fixtures from "@/pages/internal/Fixtures";
import { DevUrls } from "@/pages/internal/devUrls";
import { Button } from "@/components/ui/button";
import Whitelist from "@/pages/internal/Whitelist";
import Maintenance from "@/pages/internal/Maintenance";

const SentryTesting = () => {
  return (
    <div
      style={{
        backgroundColor: "#1a1a1a",
        color: "#fff",
        padding: "20px",
        minHeight: "100vh",
      }}
    >
      <Button
        onClick={() => {
          throw new Error("Just testing");
        }}
      >
        Explode
      </Button>
    </div>
  );
};

export const internalRoutes = {
  path: "internal",
  element: (
    <AuthGuard>
      <InternalLayout />
    </AuthGuard>
  ),
  children: [
    {
      index: true,
      element: <Navigate to="envInfo" replace />,
    },
    {
      path: "tripo",
      element: <TripoTasks />,
    },
    {
      path: "builderBag",
      element: <BuilderBagDebug />,
    },
    {
      path: "tasks",
      element: <GenerationTasks />,
    },
    {
      path: "taskStats",
      element: <GenerationTaskStats />,
    },
    {
      path: "worlds",
      element: <Worlds />,
    },
    {
      path: "worlds/:id",
      element: <World />,
    },
    {
      path: "sentry",
      element: <SentryTesting />,
    },
    {
      path: "uiShowcase",
      children: [
        {
          index: true,
          element: <Navigate to="shadcn" replace />,
        },
        {
          path: "shadcn",
          element: <UIShowcase />,
        },
        {
          path: "typography",
          element: <UIShowcase />,
        },
        {
          path: "colors",
          element: <UIShowcase />,
        },
      ],
    },
    {
      path: "envInfo",
      element: <EnvInfo />,
    },
    {
      path: "userInfo",
      element: <UserInfo />,
    },
    {
      path: "dataJobs",
      element: <DataJobs />,
    },
    {
      path: "dataJobs/new",
      element: <NewDataJob />,
    },
    {
      path: "dataJobs/:id",
      element: <DataJob />,
    },
    {
      path: "fixtures",
      element: <Fixtures />,
    },
    {
      path: "devUrls",
      element: <DevUrls />,
    },
    {
      path: "whitelist",
      element: <Whitelist />,
    },
    {
      path: "maintenance",
      element: <Maintenance />,
    },
    {
      path: "*",
      element: <Navigate to="/internal/envInfo" replace />,
    },
  ],
};
