name: "Prod Merge"

on:
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches:
      - "prod" # Only run this check on PRs targeting the 'prod' branch

permissions:
  issues: write
  pull-requests: write
  contents: write # Required for creating and pushing tags

jobs:
  check-branch-name:
    name: "Check Source Branch Name"
    runs-on: ubuntu-latest
    steps:
      - name: "Validate branch name"
        run: |
          # The source branch (use payload for reliability)
          SOURCE_BRANCH="${{ github.event.pull_request.head.ref }}"

          # Define allowed prefixes (easier to maintain)
          ALLOWED_PREFIXES=("release/" "hotfix/")

          # Function to check if branch matches any allowed prefix
          is_valid_branch() {
            local branch="$1"
            for prefix in "${ALLOWED_PREFIXES[@]}"; do
              if [[ "$branch" == ${prefix}* ]]; then
                return 0
              fi
            done
            return 1
          }

          # Validate branch name
          if is_valid_branch "$SOURCE_BRANCH"; then
            echo "✅ Branch name '$SOURCE_BRANCH' is valid for merging into 'prod'."
            echo "::notice::Branch validation passed for '$SOURCE_BRANCH'"
          else
            echo "❌ ERROR: Branch '$SOURCE_BRANCH' cannot be merged into 'prod'."
            echo "::error::Only branches starting with the following prefixes are allowed: ${ALLOWED_PREFIXES[*]}"
            echo "::error::Current branch: '$SOURCE_BRANCH'"
            echo ""
            echo "Please rename your branch to follow the naming convention:"
            echo "  - release/your-release-name"
            echo "  - hotfix/your-hotfix-name"
            exit 1
          fi

      - name: "Add PR comment on failure"
        if: failure() && github.event.action != 'closed'
        uses: actions/github-script@v7
        with:
          script: |
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });

            // Check if we've already commented
            const botComment = comments.find(comment =>
              comment.user?.login === 'github-actions[bot]' &&
              (comment.body || '').includes('Branch Naming Convention')
            );

            if (!botComment) {
              const branch = context.payload.pull_request.head.ref;
              const body = `## ❌ Branch Naming Convention Check Failed

              **Current branch:** \`${branch}\`

              This pull request cannot be merged into \`prod\` because the source branch name doesn't follow our naming convention.

              **Allowed branch prefixes:**
              - \`release/\` - for release branches
              - \`hotfix/\` - for hotfix branches

              **Examples of valid branch names:**
              - \`release/v1.2.0\`
              - \`release/new-feature-set\`
              - \`hotfix/critical-bug-fix\`
              - \`hotfix/security-patch\`

              Please rename your branch and update this pull request.`;
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body,
              });
            }

  create-version-tag:
    name: "Create Version Tag"
    runs-on: ubuntu-latest
    needs: check-branch-name
    if: github.event.pull_request.merged == true && github.event.pull_request.base.ref == 'prod'
    steps:
      - name: "Checkout repository"
        uses: actions/checkout@v4
        with:
          ref: prod
          fetch-depth: 0 # Fetch full history
          fetch-tags: true

      - name: "Get latest version tag"
        id: get-latest-tag
        run: |
          # Get the latest version tag (vMAJOR.MINOR.PATCH)
          LATEST_TAG=$(git tag --sort=-version:refname --list "v[0-9]*.[0-9]*.[0-9]*" | head -1 || echo "v0.0.0")
          echo "Latest tag: $LATEST_TAG"
          echo "latest_tag=$LATEST_TAG" >> $GITHUB_OUTPUT

      - name: "Determine version bump type"
        id: bump-type
        run: |
          SOURCE_BRANCH="${{ github.event.pull_request.head.ref }}"
          if [[ "$SOURCE_BRANCH" == release/* ]]; then
            echo "Bump type: MINOR (release branch)"
            echo "bump_type=minor" >> $GITHUB_OUTPUT
          elif [[ "$SOURCE_BRANCH" == hotfix/* ]]; then
            echo "Bump type: PATCH (hotfix branch)"
            echo "bump_type=patch" >> $GITHUB_OUTPUT
          else
            echo "Unknown branch type, defaulting to PATCH"
            echo "bump_type=patch" >> $GITHUB_OUTPUT
          fi

      - name: "Calculate new version"
        id: new-version
        run: |
          LATEST_TAG="${{ steps.get-latest-tag.outputs.latest_tag }}"
          BUMP_TYPE="${{ steps.bump-type.outputs.bump_type }}"

          # Remove 'v' prefix for calculation
          VERSION="${LATEST_TAG#v}"

          # Validate semantic version format and normalize
          if [[ ! "$VERSION" =~ ^[0-9]+\.[0-9]+(\.[0-9]+)?$ ]]; then
            echo "❌ Invalid version format: $VERSION"
            echo "Expected format: MAJOR.MINOR.PATCH or MAJOR.MINOR"
            exit 1
          fi

          # Parse version components with defaults
          IFS='.' read -r MAJOR MINOR PATCH <<< "$VERSION"

          # Validate numeric components
          if ! [[ "$MAJOR" =~ ^[0-9]+$ ]] || ! [[ "$MINOR" =~ ^[0-9]+$ ]]; then
            echo "❌ Invalid version components: MAJOR=$MAJOR, MINOR=$MINOR"
            exit 1
          fi

          # Default PATCH to 0 if missing (e.g., v1.2 -> v1.2.0)
          PATCH="${PATCH:-0}"
          if ! [[ "$PATCH" =~ ^[0-9]+$ ]]; then
            echo "❌ Invalid PATCH version: $PATCH"
            exit 1
          fi

          echo "Parsed version: MAJOR=$MAJOR, MINOR=$MINOR, PATCH=$PATCH"
          case $BUMP_TYPE in
            "minor")
              NEW_VERSION="$MAJOR.$((MINOR + 1)).0"
              ;;
            "patch")
              NEW_VERSION="$MAJOR.$MINOR.$((PATCH + 1))"
              ;;
            *)
              NEW_VERSION="$MAJOR.$MINOR.$((PATCH + 1))"
              ;;
          esac

          NEW_TAG="v$NEW_VERSION"
          echo "New version: $NEW_VERSION"
          echo "New tag: $NEW_TAG"
          echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "new_tag=$NEW_TAG" >> $GITHUB_OUTPUT

      - name: "Determine commit to tag"
        id: tag-commit
        env:
          SOURCE_BRANCH: ${{ github.event.pull_request.head.ref }}
          PR_HEAD_SHA: ${{ github.event.pull_request.head.sha }}
          PR_MERGED: ${{ github.event.pull_request.merged }}
        run: |
          if [[ "$SOURCE_BRANCH" == release/* ]]; then
            echo "🔍 Release branch detected"

            if [[ "$PR_MERGED" == "true" ]]; then
              echo "🔎 PR merged: Finding latest commit common to main and prod (current HEAD)"

              # Fetch main branch to ensure we have the latest
              git fetch origin main:main

              # Find the latest commit on main that is contained in current prod HEAD
              LATEST_COMMON=""
              for commit in $(git rev-list main); do
                if git merge-base --is-ancestor "$commit" HEAD; then
                  LATEST_COMMON="$commit"
                  break
                fi
              done

              if [[ -z "$LATEST_COMMON" ]]; then
                echo "❌ Could not find a common commit between main and prod HEAD"
                exit 1
              fi

              echo "✅ Found latest common commit: $LATEST_COMMON"
              echo "📝 This commit is on main and is reachable from prod, ensuring the tag won't be lost"

              # Verify the commit exists in both branches
              if git merge-base --is-ancestor "$LATEST_COMMON" main && git merge-base --is-ancestor "$LATEST_COMMON" HEAD; then
                echo "✅ Verified: Commit exists in both main and prod"
              else
                echo "❌ Error: Commit does not exist in both branches"
                exit 1
              fi

              echo "tag_commit=$LATEST_COMMON" >> $GITHUB_OUTPUT
              echo "tag_strategy=latest-common" >> $GITHUB_OUTPUT
            else
              echo "🕒 PR not merged yet: selecting PR head commit from main"
              echo "✅ Using PR head: $PR_HEAD_SHA"
              echo "tag_commit=$PR_HEAD_SHA" >> $GITHUB_OUTPUT
              echo "tag_strategy=pr-head" >> $GITHUB_OUTPUT
            fi
            
          elif [[ "$SOURCE_BRANCH" == hotfix/* ]]; then
            echo "🔧 Hotfix branch detected: Using current HEAD of prod"
            
            CURRENT_HEAD=$(git rev-parse HEAD)
            echo "✅ Using prod HEAD: $CURRENT_HEAD"
            echo "📝 Hotfixes are prod-specific, so tagging the merge commit is appropriate"
            
            echo "tag_commit=$CURRENT_HEAD" >> $GITHUB_OUTPUT
            echo "tag_strategy=prod-head" >> $GITHUB_OUTPUT
            
          else
            echo "⚠️ Unknown branch type, defaulting to prod HEAD"
            CURRENT_HEAD=$(git rev-parse HEAD)
            echo "tag_commit=$CURRENT_HEAD" >> $GITHUB_OUTPUT
            echo "tag_strategy=default" >> $GITHUB_OUTPUT
          fi

      - name: "Resolve final tag (idempotent)"
        id: resolve-tag
        run: |
          TARGET_COMMIT="${{ steps.tag-commit.outputs.tag_commit }}"

          # Find if any existing vX.Y.Z tag already points to TARGET_COMMIT
          EXISTING_TAG=$(git tag --points-at "$TARGET_COMMIT" | grep -E '^v[0-9]+\.[0-9]+\.[0-9]+$' | head -n1 || true)

          if [[ -n "$EXISTING_TAG" ]]; then
            echo "🔁 Found existing tag on target commit: $EXISTING_TAG"
            echo "final_tag=$EXISTING_TAG" >> $GITHUB_OUTPUT
            echo "tag_reused=true" >> $GITHUB_OUTPUT
          else
            echo "🆕 No existing tag on target commit. Will create: ${{ steps.new-version.outputs.new_tag }}"
            echo "final_tag=${{ steps.new-version.outputs.new_tag }}" >> $GITHUB_OUTPUT
            echo "tag_reused=false" >> $GITHUB_OUTPUT
          fi

      - name: "Create and push tag"
        run: |
          NEW_TAG="${{ steps.resolve-tag.outputs.final_tag }}"
          TAG_COMMIT="${{ steps.tag-commit.outputs.tag_commit }}"
          TAG_STRATEGY="${{ steps.tag-commit.outputs.tag_strategy }}"
          TAG_REUSED="${{ steps.resolve-tag.outputs.tag_reused }}"

          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

          echo "=================================================="
          echo "🏷️  Creating tag: $NEW_TAG"
          echo "📍 Target commit: $TAG_COMMIT"
          echo "🎯 Strategy: $TAG_STRATEGY"
          echo "=================================================="

          # Show commit details for verification
          echo "📋 Commit details:"
          git show --no-patch --format="  Hash: %H%n  Author: %an <%ae>%n  Date: %cd%n  Subject: %s" "$TAG_COMMIT"
          echo ""

          if [[ "$TAG_REUSED" == "true" ]]; then
            echo "🔁 Reusing existing tag $NEW_TAG; skipping local tag creation"
          else
            # Remove local tag if it exists (idempotent)
            if git tag -l "$NEW_TAG" | grep -q "^$NEW_TAG$"; then
              echo "🗑️  Removing existing local tag: $NEW_TAG"
              git tag -d "$NEW_TAG"
            fi

            # Create the tag at the specific commit
            git tag -a "$NEW_TAG" "$TAG_COMMIT" -m "Release $NEW_TAG"
            echo "✅ Created local tag: $NEW_TAG at commit $TAG_COMMIT"
          fi

          # Check if tag already exists on remote
          if git ls-remote --exit-code --tags origin "$NEW_TAG" >/dev/null 2>&1; then
            echo "Tag $NEW_TAG already exists on remote. Skipping push."
          else
            # Push the tag to remote
            if git push origin "$NEW_TAG"; then
              echo "✅ Successfully pushed tag: $NEW_TAG"
            else
              echo "❌ Failed to push tag: $NEW_TAG"
              echo "This could be due to a race condition or permissions issue."
              # Check if it was created by another process in the meantime
              if git ls-remote --exit-code --tags origin "$NEW_TAG" >/dev/null 2>&1; then
                echo "✅ Tag now exists on remote (created by another process)"
              else
                echo "❌ Tag push failed and tag doesn't exist on remote"
                exit 1
              fi
            fi
          fi

      - name: "Setup Node.js and pnpm"
        uses: ./.github/actions/setup-node

      - name: "Build vercel-deployment tool"
        run: pnpm --filter vercel-deployment build

      - name: "Set Vercel environment variable and trigger redeploy"
        if: success()
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_NILO_PROD_TOKEN }}
          VERCEL_PROJECT_ID: ${{ vars.VERCEL_NILO_PROD_PROJECT_ID }}
          GITHUB_REPOSITORY_ID: ${{ github.repository_id }}
        run: |
          NEW_TAG="${{ steps.resolve-tag.outputs.final_tag }}"
          echo "🌐 Setting Vercel environment variable: NILO_GIT_TAG=$NEW_TAG"

          node tools/vercel-deployment/dist/index.js set-tag-and-redeploy --tag "$NEW_TAG"

      - name: "Create or update GitHub Release"
        uses: actions/github-script@v7
        env:
          PR_TITLE: ${{ github.event.pull_request.title }}
          PR_BODY: ${{ github.event.pull_request.body }}
          TAG_COMMIT: ${{ steps.tag-commit.outputs.tag_commit }}
          TAG_STRATEGY: ${{ steps.tag-commit.outputs.tag_strategy }}
          SOURCE_BRANCH: ${{ github.event.pull_request.head.ref }}
          FINAL_TAG: ${{ steps.resolve-tag.outputs.final_tag }}
          TAG_REUSED: ${{ steps.resolve-tag.outputs.tag_reused }}
        with:
          script: |
            const newTag = process.env.FINAL_TAG || '${{ steps.new-version.outputs.new_tag }}';
            const prNumber = ${{ github.event.pull_request.number }};
            const prTitle = process.env.PR_TITLE;
            const prBody = process.env.PR_BODY;
            const tagCommit = process.env.TAG_COMMIT;
            const tagStrategy = process.env.TAG_STRATEGY;
            const sourceBranch = process.env.SOURCE_BRANCH;

            // Create strategy description
            let strategyDescription = '';
            if (tagStrategy === 'latest-common') {
              strategyDescription = `
            ### 🎯 Tagging Strategy
            This release was tagged using the **latest-common strategy** because it originated from a \`release/\` branch and the PR was merged.

            - **Tagged Commit:** \`${tagCommit}\`
            - **Strategy:** The tag points to the most recent commit on \`main\` that is reachable from \`prod\`'s current HEAD
            - **Benefit:** Ensures the tag points to a commit that exists in both \`main\` and \`prod\`, so it won't be lost
            `;
            } else if (tagStrategy === 'pr-head') {
              strategyDescription = `
            ### 🎯 Tagging Strategy
            This release was tagged using the **pr-head strategy** because it originated from a \`release/\` branch and the PR was not merged at run time.

            - **Tagged Commit:** \`${tagCommit}\`
            - **Strategy:** The tag points to the PR head commit coming from \`main\`
            - **Benefit:** Guarantees we tag the exact commit that will end up in \`prod\` when the PR merges
            `;
            } else if (tagStrategy === 'prod-head') {
              strategyDescription = `
            ### 🔧 Tagging Strategy
            This release was tagged using the **prod-head strategy** because it originated from a \`hotfix/\` branch.

            - **Tagged Commit:** \`${tagCommit}\`
            - **Strategy:** The tag points to the merge commit on \`prod\`
            - **Reason:** Hotfixes are specific to production and don't need to exist in \`main\`
            `;
            }

            if (process.env.TAG_REUSED === 'true') {
              strategyDescription += `

            ### ♻️ Idempotency
            An existing tag pointing to the same commit was found and reused: **${process.env.FINAL_TAG}**.`;
            }

            const releaseNotes = `## Release ${newTag}

            **Pull Request:** #${prNumber} - ${prTitle}
            **Source Branch:** \`${sourceBranch}\`

            ${prBody || 'No description provided.'}
            ${strategyDescription}
            ---
            This release was automatically created when merging to production.
            `;
            // Try to find an existing release by tag
            try {
              const existing = await github.rest.repos.getReleaseByTag({
                owner: context.repo.owner,
                repo: context.repo.repo,
                tag: newTag,
              });
              // Update existing release
              await github.rest.repos.updateRelease({
                owner: context.repo.owner,
                repo: context.repo.repo,
                release_id: existing.data.id,
                tag_name: newTag,
                name: newTag,
                body: releaseNotes,
                draft: false,
                prerelease: false,
              });
              core.notice(`Updated existing release for ${newTag}`);
            } catch (err) {
              // Create new release if not found
              await github.rest.repos.createRelease({
                owner: context.repo.owner,
                repo: context.repo.repo,
                tag_name: newTag,
                name: newTag,
                body: releaseNotes,
                draft: false,
                prerelease: false,
              });
              core.notice(`Created release for ${newTag}`);
            }
