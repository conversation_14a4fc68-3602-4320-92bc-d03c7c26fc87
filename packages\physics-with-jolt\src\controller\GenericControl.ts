import { Dynamic<PERSON>roper<PERSON><PERSON><PERSON> } from "../types/webworker";

export class GenericControl {
  private activeDynamicProperties: DynamicPropertyKey[] = [];
  constructor() {}

  getActiveDynamicProperties(): DynamicPropertyKey[] {
    return this.activeDynamicProperties;
  }
  addActiveDynamicProperties(dynamicProperties: DynamicPropertyK<PERSON>[]): void {
    this.activeDynamicProperties = [
      ...this.activeDynamicProperties,
      ...dynamicProperties,
    ];
  }
  removeActiveDynamicProperties(dynamicProperties: DynamicPropertyKey[]): void {
    this.activeDynamicProperties = this.activeDynamicProperties.filter(
      (property) => !dynamicProperties.includes(property)
    );
  }
}
