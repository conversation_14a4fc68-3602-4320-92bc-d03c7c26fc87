import {
  deleteDoc,
  doc,
  getDoc,
  serverTimestamp,
  setDoc,
  updateDoc,
  writeBatch,
} from "firebase/firestore";

import {
  CloneWorldRequest,
  CreateWorldRequest,
  CreateWorldResponse,
} from "@nilo/firebase-schema";

import { bindFunction, db } from "@/config/firebase";
import {
  worldsCollection,
  deletedWorldsCollection,
  worldUserRelationDoc,
} from "@/utils/firestoreCollections";
import { generateRandomWorldDisplayName } from "@/platform/utils/generateRandomWorldDisplayName";
import {
  trackWorldCreated,
  trackWorldPublished,
  trackWorldRemixed,
} from "@/utils/tracking/worldUtils";

const cloneWorld = bindFunction<CloneWorldRequest, CreateWorldResponse>(
  "cloneWorld"
);

const createWorld = bindFunction<CreateWorldRequest, CreateWorldResponse>(
  "createWorld"
);

export const createPrivateWorld = async () => {
  const { data } = await createWorld({ isPublic: false });
  if (data.status !== "ok") {
    throw new Error(data.message ?? data.error ?? "Unknown error");
  }

  // Track world creation
  trackWorldCreated(data.worldId, { is_public: false });

  return data.worldId;
};

export const remixWorld = async (
  worldId: string,
  worldDisplayName?: string
) => {
  const result = await cloneWorld({ worldId });
  if (result.data?.status !== "ok") {
    throw new Error("Failed to clone world.");
  }

  const newWorldId = result.data.worldId;
  if (!newWorldId) {
    throw new Error("No new world ID received or error in response.");
  }

  // Set the displayName
  await updateDoc(doc(worldsCollection, newWorldId), {
    displayName: worldDisplayName ?? generateRandomWorldDisplayName(),
  });

  // Track world remixed
  trackWorldRemixed(newWorldId, worldId, {
    display_name: worldDisplayName ?? generateRandomWorldDisplayName(),
  });

  console.debug("✅ World remixed and renamed. New world ID:", newWorldId);

  return newWorldId;
};

export const setWorldPublished = async (
  worldId: string,
  isPublished: boolean,
  additionalData: Record<string, unknown> = {}
) => {
  await updateDoc(doc(worldsCollection, worldId), {
    ...additionalData,
    isPublic: isPublished,
  });

  // Track world published (only when setting to public)
  if (isPublished) {
    trackWorldPublished(worldId, additionalData);
  }
};

export const setWorldFeatured = async (
  worldId: string,
  isFeatured: boolean,
  additionalData: Record<string, unknown> = {}
) => {
  await updateDoc(doc(worldsCollection, worldId), {
    ...additionalData,
    isFeatured: isFeatured,
  });
};

export const setWorldLiked = async (
  worldId: string,
  myUserId: string,
  isLikedByMe: boolean
) => {
  const likeRef = worldUserRelationDoc({
    kind: "like",
    worldId,
    userId: myUserId,
  });

  if (isLikedByMe) {
    await setDoc(likeRef, {
      kind: "like",
      worldId,
      userId: myUserId,
      createdAt: serverTimestamp(),
    });
  } else {
    await deleteDoc(likeRef);
  }
};

export const setWorldSaved = async (
  worldId: string,
  myUserId: string,
  isSavedByMe: boolean
) => {
  const saveRef = worldUserRelationDoc({
    kind: "save",
    worldId,
    userId: myUserId,
  });

  if (isSavedByMe) {
    await setDoc(saveRef, {
      kind: "save",
      worldId,
      userId: myUserId,
      createdAt: serverTimestamp(),
    });
  } else {
    await deleteDoc(saveRef);
  }
};

export const deleteWorld = async (worldId: string, userId: string) => {
  const worldRef = doc(worldsCollection, worldId);
  const worldSnap = await getDoc(worldRef);

  if (!worldSnap.exists()) {
    throw new Error("World not found");
  }

  const worldData = worldSnap.data();

  if (worldData.ownerId !== userId) {
    throw new Error("You don't have permission to delete this world");
  }

  // Use batch operation to ensure atomicity
  const batch = writeBatch(db);
  batch.set(doc(deletedWorldsCollection, worldId), {
    ...worldData,
    updatedAt: serverTimestamp(),
  });
  batch.delete(worldRef);
  await batch.commit();
};
