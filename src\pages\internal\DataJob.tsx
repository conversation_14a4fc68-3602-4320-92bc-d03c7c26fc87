import {
  collection,
  CollectionReference,
  QueryDocumentSnapshot,
  orderBy,
  query,
  addDoc,
  serverTimestamp,
  deleteDoc,
  setDoc,
} from "firebase/firestore";
import { useAuthState } from "react-firebase-hooks/auth";
import { use<PERSON>ara<PERSON>, Link, useNavigate } from "react-router-dom";
import React, { useState } from "react";
import {
  ChevronDown,
  ChevronRight,
  ExternalLink,
  Database,
  FileText,
  RefreshCcwIcon,
} from "lucide-react";
import {
  FirestoreDocumentUrl,
  FirebaseFunctionLogsUrl,
  FirebaseFunctionLogsForInternalDataJobs,
} from "@/pages/internal/devUrls";
import { db, auth } from "@/config/firebase";
import {
  DbCollections,
  DbInternalDataJob,
  DbInternalDataJobLog,
} from "@nilo/firebase-schema";
import { useFirebaseQuery, useStreamDocumentById } from "@/hooks/firebaseHooks";
import { Typography } from "@/components/ui-nilo/Typography";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";

const formatDate = (timestamp: DbInternalDataJob["createdAt"]) => {
  if (!timestamp) return "N/A";
  return new Date(timestamp.toDate()).toLocaleString();
};

const getStatusBackgroundColorClassName = (
  status: DbInternalDataJob["status"]
) => {
  switch (status) {
    case "completed":
      return "bg-green-500";
    case "failed":
      return "bg-red-500";
    case "processing":
      return "bg-yellow-500";
    case "pending":
      return "bg-blue-500";
    default:
      return "bg-gray-500";
  }
};

const getLogLevelBackgroundColorClassName = (
  level: DbInternalDataJobLog["level"]
) => {
  switch (level) {
    case "error":
      return "bg-red-500";
    case "warn":
      return "bg-yellow-500";
    case "info":
      return "bg-blue-500";
    default:
      return "bg-gray-500";
  }
};

const internalDataJobsCollection = collection(
  db,
  DbCollections.internalDataJobs
) as CollectionReference<DbInternalDataJob, DbInternalDataJob>;

const LogRow = ({
  log,
}: {
  log: QueryDocumentSnapshot<DbInternalDataJobLog>;
}) => {
  const data = log.data();

  return (
    <TableRow>
      <TableCell className="w-24">
        <Badge className={getLogLevelBackgroundColorClassName(data.level)}>
          {data.level.toUpperCase()}
        </Badge>
      </TableCell>
      <TableCell className="text-sm text-muted-foreground w-40">
        {formatDate(data.createdAt)}
      </TableCell>
      <TableCell className="font-mono text-sm break-words text-foreground whitespace-pre-wrap max-w-xs">
        {data.message}
      </TableCell>
    </TableRow>
  );
};

const SubJobLogs = ({
  subJobId,
  parentJobId,
}: {
  subJobId: string;
  parentJobId: string;
}) => {
  const subJobLogsCollection = collection(
    db,
    DbCollections.internalDataJobs,
    parentJobId,
    DbCollections.internalSubJobs,
    subJobId,
    DbCollections.internalJobLogs
  ) as CollectionReference<DbInternalDataJobLog, DbInternalDataJobLog>;

  const subJobLogsQuery = query(
    subJobLogsCollection,
    orderBy("createdAt", "desc")
  );
  const subJobLogs = useFirebaseQuery(subJobLogsQuery);

  if (subJobLogs.length === 0) {
    return (
      <div className="space-y-2">
        <Typography.Label level={2} color="light" weight="medium">
          Logs
        </Typography.Label>
        <Typography.Body
          level={3}
          color="light"
          className="text-sm text-muted-foreground"
        >
          No logs available for this sub job.
        </Typography.Body>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Typography.Label level={2} color="light" weight="medium">
        Logs ({subJobLogs.length})
      </Typography.Label>
      <div className="border border-accessories-divider rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-20">Level</TableHead>
              <TableHead className="w-32">Timestamp</TableHead>
              <TableHead className="min-w-0">Message</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {subJobLogs.map((log) => (
              <LogRow key={log.id} log={log} />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default function DataJob() {
  const { id } = useParams<{ id: string }>();
  const [user] = useAuthState(auth);
  const [logLevelFilter, setLogLevelFilter] = useState<string>("all");
  const [isConfigExpanded, setIsConfigExpanded] = useState(false);
  const [isLogsExpanded, setIsLogsExpanded] = useState(false);
  const [isSubJobsExpanded, setIsSubJobsExpanded] = useState(true);
  const [expandedSubJobLogs, setExpandedSubJobLogs] = useState<Set<string>>(
    new Set()
  );
  const [scheduledAgainAsId, setScheduledAgainAsId] = useState<
    string | undefined
  >(undefined);
  const navigate = useNavigate();

  const toggleSubJobLogs = (subJobId: string) => {
    const newExpanded = new Set(expandedSubJobLogs);
    if (newExpanded.has(subJobId)) {
      newExpanded.delete(subJobId);
    } else {
      newExpanded.add(subJobId);
    }
    setExpandedSubJobLogs(newExpanded);
  };

  const job = useStreamDocumentById(internalDataJobsCollection, id);
  const logsCollection =
    job.value &&
    (collection(
      job.value.ref,
      DbCollections.internalJobLogs
    ) as CollectionReference<DbInternalDataJobLog, DbInternalDataJobLog>);

  const logsQuery = logsCollection
    ? query(logsCollection, orderBy("createdAt", "desc"))
    : undefined;
  const logs = useFirebaseQuery(logsQuery);

  const filteredLogs = logs.filter(
    (log) => logLevelFilter === "all" || log.data().level === logLevelFilter
  );

  const subJobsCollection =
    job.value &&
    (collection(
      job.value.ref,
      DbCollections.internalSubJobs
    ) as CollectionReference<DbInternalDataJob, DbInternalDataJob>);

  const subJobsQuery = subJobsCollection
    ? query(subJobsCollection, orderBy("createdAt", "desc"))
    : undefined;
  const subJobs = useFirebaseQuery(subJobsQuery);

  if (!id) {
    return (
      <div className="h-screen bg-black text-icon-primary flex items-center justify-center">
        <Typography.Title level={1} color="light">
          Job ID not provided
        </Typography.Title>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="h-screen bg-black text-icon-primary flex items-center justify-center">
        <Typography.Title level={1} color="light">
          Loading job...
        </Typography.Title>
      </div>
    );
  }

  const data = job.value?.data();
  if (!data) {
    return (
      <div className="h-screen bg-black text-icon-primary flex items-center justify-center">
        <Typography.Title level={1} color="light">
          {job.status === "loading" ? "Loading job..." : "Job not found"}
        </Typography.Title>
      </div>
    );
  }

  const isCurrentUser = data.userId === user?.uid;

  return (
    <div className="h-screen bg-black text-icon-primary overflow-y-auto">
      <div className="mx-auto space-y-6 pt-8 pb-24 max-w-7xl px-4">
        {/* Header */}
        <header className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Link
              to="/internal/dataJobs"
              className="text-icon-secondary hover:text-icon-primary transition-colors"
            >
              ← Back to Jobs
            </Link>
            <div>
              <Typography.Title level={1} color="light">
                Data Job Details
              </Typography.Title>
              <Typography.Body level={1} color="light" weight="medium">
                Job ID:{" "}
                <FirestoreDocumentUrl
                  path={[DbCollections.internalDataJobs, id]}
                >
                  {id}
                </FirestoreDocumentUrl>
              </Typography.Body>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={getStatusBackgroundColorClassName(data.status)}>
              {data.status}
            </Badge>
            {isCurrentUser && (
              <Badge variant="secondary" className="text-xs">
                Your job
              </Badge>
            )}
          </div>
        </header>

        {/* Job Overview */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Typography.Title level={2} color="light">
                Job Overview
              </Typography.Title>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Typography.Label level={2} color="light" weight="medium">
                  Job Type
                </Typography.Label>
                <Badge variant="secondary">{data.config.type}</Badge>
              </div>

              <div className="space-y-2">
                <Typography.Label level={2} color="light" weight="medium">
                  Job Details
                </Typography.Label>
                <div className="space-y-1">
                  <div className="flex justify-between items-center">
                    <Typography.Body
                      level={3}
                      color="light"
                      className="text-xs"
                    >
                      Job ID
                    </Typography.Body>
                    <Typography.Body
                      level={3}
                      color="light"
                      className="font-mono text-xs"
                    >
                      {id}
                    </Typography.Body>
                  </div>
                  <div className="flex justify-between items-center">
                    <Typography.Body
                      level={3}
                      color="light"
                      className="text-xs"
                    >
                      User ID
                    </Typography.Body>
                    <Typography.Body
                      level={3}
                      color="light"
                      className="font-mono text-xs"
                    >
                      {data.userId}
                    </Typography.Body>
                  </div>
                  <div className="flex justify-between items-center">
                    <Typography.Body
                      level={3}
                      color="light"
                      className="text-xs"
                    >
                      Created
                    </Typography.Body>
                    <Typography.Body
                      level={3}
                      color="light"
                      className="text-xs"
                    >
                      {formatDate(data.createdAt)}
                    </Typography.Body>
                  </div>
                  <div className="flex justify-between items-center">
                    <Typography.Body
                      level={3}
                      color="light"
                      className="text-xs"
                    >
                      Updated
                    </Typography.Body>
                    <Typography.Body
                      level={3}
                      color="light"
                      className="text-xs"
                    >
                      {formatDate(data.updatedAt)}
                    </Typography.Body>
                  </div>
                  <div className="flex justify-between items-center">
                    <Typography.Body
                      level={3}
                      color="light"
                      className="text-xs"
                    >
                      Message
                    </Typography.Body>
                    <Typography.Body
                      level={3}
                      color="light"
                      className="font-mono text-xs"
                    >
                      {data.message}
                    </Typography.Body>
                  </div>
                </div>
              </div>

              {(data.config.type === "clone_worlds" ||
                data.config.type === "clone_all_worlds") && (
                <div className="space-y-2">
                  <Typography.Label level={2} color="light" weight="medium">
                    Database Configuration
                  </Typography.Label>
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <Typography.Body
                        level={3}
                        color="light"
                        className="text-xs"
                      >
                        From Database
                      </Typography.Body>
                      <Typography.Body
                        level={3}
                        color="light"
                        className="font-mono text-xs"
                      >
                        {data.config.fromDatabase}
                      </Typography.Body>
                    </div>
                    {data.config.type === "clone_worlds" && (
                      <div className="flex justify-between items-center">
                        <Typography.Body
                          level={3}
                          color="light"
                          className="text-xs"
                        >
                          World IDs
                        </Typography.Body>
                        <Typography.Body
                          level={3}
                          color="light"
                          className="font-mono text-xs break-words max-w-xs text-right"
                        >
                          {data.config.worldIds.join(", ")}
                        </Typography.Body>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {(data.subJobs ?? 0) > 0 && (
                <div className="space-y-2">
                  <Typography.Label level={2} color="light" weight="medium">
                    Progress
                  </Typography.Label>
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <Typography.Body
                        level={3}
                        color="light"
                        className="text-xs"
                      >
                        Total Sub Jobs
                      </Typography.Body>
                      <Typography.Body
                        level={3}
                        color="light"
                        className="text-xs"
                      >
                        {data.subJobs ?? 0}
                      </Typography.Body>
                    </div>
                    <div className="flex justify-between items-center">
                      <Typography.Body
                        level={3}
                        color="light"
                        className="text-xs"
                      >
                        Completed
                      </Typography.Body>
                      <Typography.Body
                        level={3}
                        color="light"
                        className="text-xs text-green-400"
                      >
                        {data.completedSubJobs ?? 0}
                      </Typography.Body>
                    </div>
                    <div className="flex justify-between items-center">
                      <Typography.Body
                        level={3}
                        color="light"
                        className="text-xs"
                      >
                        Failed
                      </Typography.Body>
                      <Typography.Body
                        level={3}
                        color="light"
                        className="text-xs text-red-400"
                      >
                        {data.failedSubJobs ?? 0}
                      </Typography.Body>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="pt-4 border-t border-border">
              <Button
                variant={
                  scheduledAgainAsId === undefined ? "danger" : "default"
                }
                onClick={async () => {
                  if (scheduledAgainAsId !== undefined) {
                    navigate(`/internal/dataJobs/${scheduledAgainAsId}`);
                    setScheduledAgainAsId(undefined);
                    return;
                  }
                  const answer = prompt(
                    "Are you sure you want to schedule this job again?",
                    "yes"
                  );
                  if (answer !== "yes") {
                    return;
                  }
                  const jobsCollection = collection(
                    db,
                    DbCollections.internalDataJobs
                  ) as CollectionReference<
                    DbInternalDataJob,
                    DbInternalDataJob
                  >;
                  const ref = await addDoc(jobsCollection, {
                    status: "pending",
                    config: data.config,
                    userId: data.userId,
                    createdAt: serverTimestamp(),
                    updatedAt: serverTimestamp(),
                  });
                  console.info("Scheduled again", ref.id);
                  setScheduledAgainAsId(ref.id);
                }}
                className="w-full"
              >
                {scheduledAgainAsId !== undefined
                  ? `See New Job (${scheduledAgainAsId})`
                  : "Schedule Again"}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Quick Links */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ExternalLink className="w-5 h-5" />
              Quick Links
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <Button
                variant="outline"
                className="h-auto p-4 justify-start"
                asChild
              >
                <FirestoreDocumentUrl
                  collection={DbCollections.internalDataJobs}
                  id={id}
                  className="group"
                >
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center">
                      <Database className="w-4 h-4" />
                    </div>
                    <div className="text-left">
                      <Typography.Label level={2} color="light" weight="medium">
                        Firestore Document
                      </Typography.Label>
                      <Typography.Body
                        level={3}
                        color="light"
                        className="font-mono text-xs"
                      >
                        {id}
                      </Typography.Body>
                    </div>
                  </div>
                </FirestoreDocumentUrl>
              </Button>

              {subJobs.length > 0 && (
                <Button
                  variant="outline"
                  className="h-auto p-4 justify-start"
                  asChild
                >
                  <FirebaseFunctionLogsForInternalDataJobs
                    jobId={id}
                    className="group"
                  >
                    <div className="flex items-center gap-3">
                      <div className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center">
                        <FileText className="w-4 h-4" />
                      </div>
                      <div className="text-left">
                        <Typography.Label
                          level={2}
                          color="light"
                          weight="medium"
                        >
                          Function Logs
                        </Typography.Label>
                        <Typography.Body
                          level={3}
                          color="light"
                          className="font-mono text-xs"
                        >
                          onDataJobCreated + onDataSubJobCreated
                        </Typography.Body>
                      </div>
                    </div>
                  </FirebaseFunctionLogsForInternalDataJobs>
                </Button>
              )}

              <Button
                variant="outline"
                className="h-auto p-4 justify-start"
                asChild
              >
                <FirebaseFunctionLogsUrl
                  name="onDataJobCreated"
                  filterByLogValues={{ jobId: id }}
                  className="group"
                >
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center">
                      <FileText className="w-4 h-4" />
                    </div>
                    <div className="text-left">
                      <Typography.Label level={2} color="light" weight="medium">
                        Function Logs
                      </Typography.Label>
                      <Typography.Body
                        level={3}
                        color="light"
                        className="font-mono text-xs"
                      >
                        onDataJobCreated
                      </Typography.Body>
                    </div>
                  </div>
                </FirebaseFunctionLogsUrl>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Configuration */}
        <Card className="bg-surface-primary border-accessories-divider">
          <CardHeader>
            <CardTitle>
              <Button
                variant="ghost"
                className="p-0 h-auto justify-start hover:bg-transparent"
                onClick={() => setIsConfigExpanded(!isConfigExpanded)}
              >
                <div className="flex items-center gap-2">
                  {isConfigExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                  <Typography.Title level={2} color="light">
                    Job Configuration
                  </Typography.Title>
                </div>
              </Button>
            </CardTitle>
          </CardHeader>
          {isConfigExpanded && (
            <CardContent>
              <pre className="bg-muted text-muted-foreground p-4 rounded-lg overflow-x-auto text-sm font-mono leading-relaxed border">
                {JSON.stringify(data.config, null, 2)}
              </pre>
            </CardContent>
          )}
        </Card>

        {/* Sub Jobs */}
        {subJobs.length > 0 && (
          <Card className="bg-surface-primary border-accessories-divider">
            <CardHeader>
              <CardTitle>
                <Button
                  variant="ghost"
                  className="p-0 h-auto justify-start hover:bg-transparent"
                  onClick={() => setIsSubJobsExpanded(!isSubJobsExpanded)}
                >
                  <div className="flex items-center gap-2">
                    {isSubJobsExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                    <Typography.Title level={2} color="light">
                      Sub Jobs ({subJobs.length})
                    </Typography.Title>
                  </div>
                </Button>
              </CardTitle>
            </CardHeader>
            {isSubJobsExpanded && (
              <CardContent>
                <div className="border border-accessories-divider rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-32">Sub Job ID</TableHead>
                        <TableHead className="w-24">Status</TableHead>
                        <TableHead className="w-40">Message</TableHead>
                        <TableHead className="w-40">Created</TableHead>
                        <TableHead className="w-24">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {subJobs.map((subJob) => {
                        const subJobData = subJob.data();
                        const isLogsExpanded = expandedSubJobLogs.has(
                          subJob.id
                        );
                        return (
                          <React.Fragment key={subJob.id}>
                            <TableRow
                              onClick={() => toggleSubJobLogs(subJob.id)}
                            >
                              <TableCell className="font-mono text-sm text-foreground">
                                {subJob.id}
                              </TableCell>
                              <TableCell>
                                <Badge
                                  className={getStatusBackgroundColorClassName(
                                    subJobData.status
                                  )}
                                >
                                  {subJobData.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-sm text-muted-foreground">
                                {subJobData.message}
                              </TableCell>
                              <TableCell className="text-sm text-muted-foreground">
                                {formatDate(subJobData.createdAt)}
                              </TableCell>
                              <TableCell onClick={(e) => e.stopPropagation()}>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleSubJobLogs(subJob.id)}
                                  className="h-8 px-2"
                                >
                                  {isLogsExpanded ? (
                                    <ChevronDown className="h-4 w-4" />
                                  ) : (
                                    <ChevronRight className="h-4 w-4" />
                                  )}
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 px-2"
                                  asChild
                                >
                                  <FirestoreDocumentUrl
                                    path={[
                                      DbCollections.internalDataJobs,
                                      id,
                                      DbCollections.internalSubJobs,
                                      subJob.id,
                                    ]}
                                    className="group"
                                  >
                                    <Database className="w-4 h-4" />
                                  </FirestoreDocumentUrl>
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 px-2"
                                  asChild
                                >
                                  <FirebaseFunctionLogsUrl
                                    name="onDataSubJobCreated"
                                    filterByLogValues={{
                                      parentJobId: id,
                                      jobId: subJob.id,
                                    }}
                                    className="group"
                                  >
                                    <FileText className="w-4 h-4" />
                                  </FirebaseFunctionLogsUrl>
                                </Button>
                                {subJobData.status !== "completed" && (
                                  <Button
                                    variant="destructive"
                                    size="sm"
                                    className="h-8 px-2"
                                    onClick={async () => {
                                      if (!user?.uid) {
                                        console.error("User not authenticated");
                                        return;
                                      }
                                      if (!subJobsCollection) {
                                        console.error(
                                          "Sub jobs collection not found"
                                        );
                                        return;
                                      }
                                      await deleteDoc(subJob.ref);
                                      await Promise.all([
                                        setDoc(subJob.ref, {
                                          ...subJobData,
                                          updatedAt: serverTimestamp(),
                                          userId: user.uid,
                                          status: "pending",
                                        }),
                                        addDoc(
                                          collection(
                                            subJob.ref,
                                            DbCollections.internalJobLogs
                                          ),
                                          {
                                            createdAt: serverTimestamp(),
                                            level: "warn",
                                            message: "Rescheduling",
                                          }
                                        ),
                                      ]);
                                    }}
                                  >
                                    <RefreshCcwIcon className="w-4 h-4" />
                                    Reschedule
                                  </Button>
                                )}
                              </TableCell>
                            </TableRow>
                            {isLogsExpanded && (
                              <TableRow>
                                <TableCell colSpan={4} className="p-0">
                                  <div className="bg-muted/50 p-4">
                                    <div className="space-y-3">
                                      <div className="space-y-2">
                                        <Typography.Label
                                          level={2}
                                          color="light"
                                          weight="medium"
                                        >
                                          Configuration
                                        </Typography.Label>
                                        <pre className="bg-muted text-muted-foreground p-3 rounded text-xs font-mono overflow-x-auto">
                                          {JSON.stringify(
                                            subJobData.config,
                                            null,
                                            2
                                          )}
                                        </pre>
                                      </div>
                                      <SubJobLogs
                                        subJobId={subJob.id}
                                        parentJobId={id}
                                      />
                                    </div>
                                  </div>
                                </TableCell>
                              </TableRow>
                            )}
                          </React.Fragment>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            )}
          </Card>
        )}

        {/* Logs Table */}
        <Card className="bg-surface-primary border-accessories-divider">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>
                <Button
                  variant="ghost"
                  className="p-0 h-auto justify-start hover:bg-transparent"
                  onClick={() => setIsLogsExpanded(!isLogsExpanded)}
                >
                  <div className="flex items-center gap-2">
                    {isLogsExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                    <Typography.Title level={2} color="light">
                      Job Logs ({filteredLogs.length})
                    </Typography.Title>
                  </div>
                </Button>
              </CardTitle>
              {isLogsExpanded && (
                <div className="flex items-center gap-2">
                  <Select
                    value={logLevelFilter}
                    onValueChange={setLogLevelFilter}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Filter logs" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Levels</SelectItem>
                      <SelectItem value="error">Errors</SelectItem>
                      <SelectItem value="warn">Warnings</SelectItem>
                      <SelectItem value="info">Info</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </CardHeader>
          {isLogsExpanded && (
            <CardContent>
              {filteredLogs.length === 0 ? (
                <Typography.Body
                  level={1}
                  color="light"
                  className="text-center py-8"
                >
                  No logs available for this job.
                </Typography.Body>
              ) : (
                <div className="border border-accessories-divider rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-24">Level</TableHead>
                        <TableHead className="w-40">Timestamp</TableHead>
                        <TableHead className="min-w-0">Message</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredLogs.map((log) => (
                        <LogRow key={log.id} log={log} />
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
}
