import {
  Db<PERSON><PERSON><PERSON><PERSON>,
  DbInternalDataJob,
  DbInternalDataJobLog,
} from "@nilo/firebase-schema";
import { FieldValue } from "firebase-admin/firestore";
import { DocumentReferenceT } from "../../typedFirebase";
import { Logger } from "../../logger";

export type JobType = DbInternalDataJob["config"]["type"];

export abstract class DataJob<T extends JobType> {
  private static readonly maxLogMessageLength = 5000;

  private _hasSubJobs = false;

  constructor(
    protected readonly logger: Logger,
    private readonly jobRef: DocumentReferenceT<DbInternalDataJob>,
    public readonly config: Extract<DbInternalDataJob["config"], { type: T }>
  ) {}

  public abstract run(): Promise<void>;

  public get hasSubJobs(): boolean {
    return this._hasSubJobs;
  }

  public get id(): string {
    return this.jobRef.id;
  }

  public async emitLog(level: DbInternalDataJobLog["level"], message: string) {
    if (message.length > DataJob.maxLogMessageLength) {
      message = message.substring(0, DataJob.maxLogMessageLength - 3) + "...";
    }
    return await this.jobRef
      .collection<DbInternalDataJobLog>(DbCollections.internalJobLogs)
      .add({
        createdAt: FieldValue.serverTimestamp(),
        level,
        message,
      });
  }

  public async setMessage(message: string) {
    await this.jobRef.update({
      message,
      updatedAt: FieldValue.serverTimestamp(),
    });
  }

  public async info(message: string) {
    this.logger.info(message);
    return await this.emitLog("info", message);
  }

  public async warn(message: string) {
    this.logger.warn(message);
    return await this.emitLog("warn", message);
  }

  public async error(message: string) {
    // warn because we don't want to report this as a sentry error
    this.logger.warn(message);
    return await this.emitLog("error", message);
  }

  protected async scheduleSubJob(config: DbInternalDataJob["config"]) {
    this.logger.info("Scheduling sub job", { config });
    this._hasSubJobs = true;
    const userId = (await this.jobRef.data())?.userId;
    if (!userId) {
      throw new Error("User ID is required");
    }
    return await Promise.all([
      this.jobRef
        .collection<DbInternalDataJob>(DbCollections.internalSubJobs)
        .add({
          createdAt: FieldValue.serverTimestamp(),
          updatedAt: FieldValue.serverTimestamp(),
          userId,
          status: "pending",
          config,
        }),
      this.jobRef.update({
        updatedAt: FieldValue.serverTimestamp(),
        subJobs: FieldValue.increment(1),
      }),
    ]);
  }
}
