import {
  DbAsset,
  DbCollections,
  DbModelGenerationTask,
  DbWorld,
} from "@nilo/firebase-schema";
import { DocumentData } from "firebase-admin/firestore";
import { db } from "../..";
import { collection, CollectionReferenceT } from "../../typedFirebase";
import { isolatedEnvironment } from "../../isolatedEnv";
import { isEmulator } from "../../utils";
import { DataJob } from "./types";

export class MigrateDataJob extends DataJob<"migrate"> {
  public override async run(): Promise<void> {
    this.logger.info("Running migrate job", this.config);
    switch (this.config.script) {
      case "nuke_worlds_tasks_assets":
        await this.nukeWorldsTasksAssets();
        break;
      default:
        this.logger.error("Unknown script", this.config.script);
        throw new Error(`Unknown script: ${this.config.script}`);
    }
  }

  async setMessage(message: string) {
    await super.setMessage(`${this.config.script}: ${message}`);
  }

  async nukeWorldsTasksAssets() {
    if (
      !isEmulator &&
      (isolatedEnvironment.name === "main" ||
        isolatedEnvironment.name === "prod")
    ) {
      this.logger.warn(
        `Skipping nukeWorldsTasksAssets in ${isolatedEnvironment.name} isolated environment`
      );
      return;
    }

    let batch = db.batch();
    let batchSize = 0;

    const collections = {
      [DbCollections.worlds]: collection<DbWorld>(DbCollections.worlds),
      [DbCollections.modelGenerationTasks]: collection<DbModelGenerationTask>(
        DbCollections.modelGenerationTasks
      ),
      [DbCollections.assets]: collection<DbAsset>(DbCollections.assets),
    };
    const counts: Record<keyof typeof collections, number> = {
      [DbCollections.worlds]: 0,
      [DbCollections.modelGenerationTasks]: 0,
      [DbCollections.assets]: 0,
    };
    for (const [name, collection] of Object.entries(collections) as [
      keyof typeof collections,
      CollectionReferenceT<DocumentData>,
    ][]) {
      for await (const doc of collection.streamRefs()) {
        doc.delete(batch);
        counts[name]++;
        batchSize++;
        if (batchSize >= 400) {
          this.logger.info(
            `Committing batch of ${batchSize} document deletes`,
            {
              counts: { ...counts },
            }
          );
          await batch.commit();
          batch = db.batch();
          batchSize = 0;
        }
      }
    }

    if (batchSize > 0) {
      this.logger.info(`Committing batch of ${batchSize} document deletes`, {
        counts: { ...counts },
      });
      await batch.commit();
    }
    await this.setMessage(
      `Deleted ${Object.entries(counts)
        .map(([name, count]) => `${count} ${name}`)
        .join(", ")}`
    );
  }
}
