import { DbInternalDataJob } from "@nilo/firebase-schema";
import { sleepMs } from "../../utils";
import { DocumentReferenceT } from "../../typedFirebase";
import { Logger } from "../../logger";
import {
  C<PERSON>AllWorldsDataJob,
  CloneWorldsDataJob,
  CloneAllUsersDataJob,
} from "./cloning";
import { DataJob, JobType } from "./types";
import { MigrateDataJob } from "./migration";

class NoopDataJob extends DataJob<"noop"> {
  public override async run(): Promise<void> {
    this.logger.info("Running noop job", this.config);
    await sleepMs(this.config.durationMs ?? 0);
    this.logger.info("Noop job completed");
  }
}

type JobRegistry = {
  noop: NoopDataJob;
  clone_worlds: CloneWorldsDataJob;
  clone_all_worlds: CloneAllWorldsDataJob;
  clone_all_users: CloneAllUsersDataJob;
  migrate: MigrateDataJob;
};

type JobFactory<T extends JobType> = (
  logger: Logger,
  jobRef: DocumentReferenceT<DbInternalDataJob>,
  config: Extract<DbInternalDataJob["config"], { type: T }>
) => JobRegistry[T];

const jobFactories: {
  [K in JobType]: JobFactory<K>;
} = {
  noop: (logger, jobRef, config) => new NoopDataJob(logger, jobRef, config),
  clone_worlds: (logger, jobRef, config) =>
    new CloneWorldsDataJob(logger, jobRef, config),
  clone_all_worlds: (logger, jobRef, config) =>
    new CloneAllWorldsDataJob(logger, jobRef, config),
  clone_all_users: (logger, jobRef, config) =>
    new CloneAllUsersDataJob(logger, jobRef, config),
  migrate: (logger, jobRef, config) =>
    new MigrateDataJob(logger, jobRef, config),
};

export function createDataJob<T extends JobType>(
  logger: Logger,
  jobRef: DocumentReferenceT<DbInternalDataJob>,
  config: Extract<DbInternalDataJob["config"], { type: T }>
): JobRegistry[T] {
  const factory = jobFactories[config.type];
  if (!factory) {
    throw new Error(`Unknown data job: ${JSON.stringify(config)}`);
  }
  return factory(logger, jobRef, config);
}
