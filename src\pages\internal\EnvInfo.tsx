import {
  GitBranch,
  GitCommit,
  GitPullRequest,
  Zap,
  Server,
  Database,
  ExternalLink,
  Info,
  Biohazard,
  Code,
  Globe,
  Tag,
  Bot,
  FileText,
  HardDrive,
} from "lucide-react";
import { isolatedEnvironment } from "@/config/isolatedEnv";
import { Typography } from "@/components/ui-nilo/Typography";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { isLocalNetwork } from "@/debug/isLocalNetwork";
import { useEmulator } from "@/config/firebase";
import {
  FirebaseFunctionLogsUrl,
  FirebaseStorageUrl,
  FirestoreDocumentUrl,
  GameServerContainerLogsUrl,
  GoogleCloudStorageUrl,
} from "@/pages/internal/devUrls";

type Git = {
  commitRef?: string;
  commitSha?: string;
  pullRequestId?: string;
};

function isGitEmpty(git: Git) {
  return !git.commitRef && !git.commitSha && !git.pullRequestId;
}

export default function EnvInfo() {
  const git: Git = {
    commitRef: process.env.VERCEL_GIT_COMMIT_REF,
    commitSha: process.env.VERCEL_GIT_COMMIT_SHA,
    pullRequestId: process.env.VERCEL_GIT_PULL_REQUEST_ID,
  };

  const EnvironmentBadge = () => {
    if (useEmulator) {
      return (
        <Badge className="bg-nilo-primary-500">
          <Bot className="w-3 h-3 mr-1" />
          Emulator
        </Badge>
      );
    } else if (isolatedEnvironment.name === "prod") {
      return (
        <Badge className="bg-nilo-danger-500">
          <Biohazard className="w-3 h-3 mr-1" />
          prod
        </Badge>
      );
    } else if (isolatedEnvironment.name === "main") {
      return (
        <Badge className="bg-nilo-warning-500">
          <Globe className="w-3 h-3 mr-1" />
          main
        </Badge>
      );
    } else if (isolatedEnvironment.name.startsWith("release-")) {
      return (
        <Badge className="bg-nilo-accent-500">
          <Tag className="w-3 h-3 mr-1" />
          {git.commitRef ?? isolatedEnvironment.name}
        </Badge>
      );
    } else {
      return (
        <Badge className="bg-nilo-primary-500">
          <GitBranch className="w-3 h-3 mr-1" />
          {isolatedEnvironment.name}
        </Badge>
      );
    }
  };

  return (
    <div className="h-screen bg-black text-icon-primary overflow-y-auto scrollbar-thin-nilo">
      <div className="mx-auto space-y-12 pt-8 pb-24 max-w-6xl">
        {/* Header */}
        <header className="text-center">
          <Typography.Title level={1} color="light" className="mb-4">
            Environment Information
          </Typography.Title>
          <Typography.Body level={1} color="light" weight="medium">
            Environment details and deployment information for debugging
          </Typography.Body>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Environment Status Card */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="w-5 h-5" />
                Environment Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <Typography.Label level={2} color="light" weight="medium">
                    <Database className="w-4 h-4 inline mr-2" />
                    Environment
                  </Typography.Label>
                  <div className="flex items-center gap-2">
                    {isLocalNetwork() && (
                      <Badge variant="default" className="bg-nilo-primary-500">
                        <Code className="w-3 h-3 mr-1" />
                        Local Development
                      </Badge>
                    )}
                    <EnvironmentBadge />
                  </div>
                </div>

                <div className="space-y-2">
                  <Typography.Label level={2} color="light" weight="medium">
                    <Server className="w-4 h-4 inline mr-2" />
                    Isolated Environment
                  </Typography.Label>
                  <div className="space-y-1">
                    {Object.entries(isolatedEnvironment).map(([key, value]) => (
                      <div
                        className="flex justify-between items-center"
                        key={key}
                      >
                        <Typography.Body
                          level={3}
                          color="light"
                          className="text-xs"
                        >
                          {key}
                        </Typography.Body>
                        <Typography.Body
                          level={3}
                          color="light"
                          className="font-mono text-xs"
                        >
                          {key === "database" && value === undefined
                            ? "(default)"
                            : value}
                        </Typography.Body>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {!isGitEmpty(git) && <GitInfoCard git={git} />}
        </div>

        <QuickLinksCard git={git} />

        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="w-5 h-5" />
              Raw Environment Data
            </CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted text-muted-foreground p-4 rounded-lg overflow-x-auto text-sm font-mono leading-relaxed border">
              {JSON.stringify(
                {
                  git,
                  isolatedEnvironment,
                  useEmulator,
                },
                null,
                2
              )}
            </pre>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

const GitInfoCard = ({ git }: { git: Git }) => {
  return (
    <Card className="bg-card border-border">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <GitBranch className="w-5 h-5" />
          Git Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          {git.commitRef && (
            <div className="flex justify-between items-center">
              <Typography.Label level={2} color="light" weight="medium">
                <GitBranch className="w-4 h-4 inline mr-2" />
                Branch
              </Typography.Label>
              <Typography.Body
                level={3}
                color="light"
                className="font-mono text-sm"
              >
                {git.commitRef}
              </Typography.Body>
            </div>
          )}

          {git.commitSha && (
            <div className="flex justify-between items-center">
              <Typography.Label level={2} color="light" weight="medium">
                <GitCommit className="w-4 h-4 inline mr-2" />
                Commit SHA
              </Typography.Label>
              <Typography.Body
                level={3}
                color="light"
                className="font-mono text-sm"
              >
                {git.commitSha.substring(0, 8)}
              </Typography.Body>
            </div>
          )}

          {git.pullRequestId && (
            <div className="flex justify-between items-center">
              <Typography.Label level={2} color="light" weight="medium">
                <GitPullRequest className="w-4 h-4 inline mr-2" />
                Pull Request
              </Typography.Label>
              <Typography.Body
                level={3}
                color="light"
                className="font-mono text-sm"
              >
                #{git.pullRequestId}
              </Typography.Body>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const QuickLinksCard = ({ git }: { git: Git }) => {
  return (
    <Card className="bg-card border-border">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ExternalLink className="w-5 h-5" />
          Quick Links
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {git.commitRef && (
            <Button
              variant="outline"
              className="h-auto p-4 justify-start"
              asChild
            >
              <a
                href={`https://github.com/nilo-technologies/Nilo/tree/${git.commitRef}`}
                target="_blank"
                rel="noopener noreferrer"
                className="group"
              >
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center">
                    <GitBranch className="w-4 h-4" />
                  </div>
                  <div className="text-left">
                    <Typography.Label level={2} color="light" weight="medium">
                      Branch
                    </Typography.Label>
                    <Typography.Body
                      level={3}
                      color="light"
                      className="font-mono text-xs"
                    >
                      {git.commitRef}
                    </Typography.Body>
                  </div>
                </div>
              </a>
            </Button>
          )}

          {git.commitRef && (
            <Button
              variant="outline"
              className="h-auto p-4 justify-start"
              asChild
            >
              <a
                href={`https://github.com/nilo-technologies/Nilo/actions?query=branch%3A${encodeURIComponent(git.commitRef || "")}`}
                target="_blank"
                rel="noopener noreferrer"
                className="group"
              >
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center">
                    <Zap className="w-4 h-4" />
                  </div>
                  <div className="text-left">
                    <Typography.Label level={2} color="light" weight="medium">
                      GitHub Actions
                    </Typography.Label>
                    <Typography.Body
                      level={3}
                      color="light"
                      className="font-mono text-xs"
                    >
                      {git.commitRef}
                    </Typography.Body>
                  </div>
                </div>
              </a>
            </Button>
          )}

          {git.pullRequestId && (
            <Button
              variant="outline"
              className="h-auto p-4 justify-start"
              asChild
            >
              <a
                href={`https://github.com/nilo-technologies/Nilo/pull/${git.pullRequestId}`}
                target="_blank"
                rel="noopener noreferrer"
                className="group"
              >
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center">
                    <GitPullRequest className="w-4 h-4" />
                  </div>
                  <div className="text-left">
                    <Typography.Label level={2} color="light" weight="medium">
                      Pull Request
                    </Typography.Label>
                    <Typography.Body
                      level={3}
                      color="light"
                      className="font-mono text-xs"
                    >
                      #{git.pullRequestId}
                    </Typography.Body>
                  </div>
                </div>
              </a>
            </Button>
          )}

          {git.commitSha && (
            <Button
              variant="outline"
              className="h-auto p-4 justify-start"
              asChild
            >
              <a
                href={`https://github.com/nilo-technologies/Nilo/commit/${git.commitSha}`}
                target="_blank"
                rel="noopener noreferrer"
                className="group"
              >
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center">
                    <GitCommit className="w-4 h-4" />
                  </div>
                  <div className="text-left">
                    <Typography.Label level={2} color="light" weight="medium">
                      Commit
                    </Typography.Label>
                    <Typography.Body
                      level={3}
                      color="light"
                      className="font-mono text-xs"
                    >
                      {git.commitSha.substring(0, 8)}
                    </Typography.Body>
                  </div>
                </div>
              </a>
            </Button>
          )}

          <Button
            variant="outline"
            className="h-auto p-4 justify-start"
            asChild
          >
            <FirestoreDocumentUrl path={[]} className="group">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center">
                  <Database className="w-4 h-4" />
                </div>
                <div className="text-left">
                  <Typography.Label level={2} color="light" weight="medium">
                    Firestore Database
                  </Typography.Label>
                  <Typography.Body
                    level={3}
                    color="light"
                    className="font-mono text-xs"
                  >
                    {isolatedEnvironment.database ?? "(default)"}
                  </Typography.Body>
                </div>
              </div>
            </FirestoreDocumentUrl>
          </Button>

          <Button
            variant="outline"
            className="h-auto p-4 justify-start"
            asChild
          >
            <FirebaseFunctionLogsUrl className="group">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center">
                  <FileText className="w-4 h-4" />
                </div>
                <div className="text-left">
                  <Typography.Label level={2} color="light" weight="medium">
                    Functions Logs
                  </Typography.Label>
                  <Typography.Body
                    level={3}
                    color="light"
                    className="font-mono text-xs"
                  >
                    {isolatedEnvironment.functions}
                  </Typography.Body>
                </div>
              </div>
            </FirebaseFunctionLogsUrl>
          </Button>

          <Button
            variant="outline"
            className="h-auto p-4 justify-start"
            asChild
          >
            <FirebaseFunctionLogsUrl className="group" group="auth">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center">
                  <FileText className="w-4 h-4" />
                </div>
                <div className="text-left">
                  <Typography.Label level={2} color="light" weight="medium">
                    Auth Functions Logs
                  </Typography.Label>
                  <Typography.Body
                    level={3}
                    color="light"
                    className="font-mono text-xs"
                  >
                    auth
                  </Typography.Body>
                </div>
              </div>
            </FirebaseFunctionLogsUrl>
          </Button>

          <Button
            variant="outline"
            className="h-auto p-4 justify-start"
            asChild
          >
            <FirebaseStorageUrl className="group">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center">
                  <HardDrive className="w-4 h-4" />
                </div>
                <div className="text-left">
                  <Typography.Label level={2} color="light" weight="medium">
                    Firebase Storage
                  </Typography.Label>
                  <Typography.Body
                    level={3}
                    color="light"
                    className="font-mono text-xs"
                  >
                    {isolatedEnvironment.bucket}
                  </Typography.Body>
                </div>
              </div>
            </FirebaseStorageUrl>
          </Button>

          <Button
            variant="outline"
            className="h-auto p-4 justify-start"
            asChild
          >
            <GoogleCloudStorageUrl className="group">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center">
                  <HardDrive className="w-4 h-4" />
                </div>
                <div className="text-left">
                  <Typography.Label level={2} color="light" weight="medium">
                    Google Cloud Storage
                  </Typography.Label>
                  <Typography.Body
                    level={3}
                    color="light"
                    className="font-mono text-xs"
                  >
                    {isolatedEnvironment.bucket}
                  </Typography.Body>
                </div>
              </div>
            </GoogleCloudStorageUrl>
          </Button>

          {!useEmulator && (
            <Button
              variant="outline"
              className="h-auto p-4 justify-start"
              asChild
            >
              <GameServerContainerLogsUrl className="group">
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center">
                    <Server className="w-4 h-4" />
                  </div>
                  <div className="text-left">
                    <Typography.Label level={2} color="light" weight="medium">
                      Game Server Container Logs
                    </Typography.Label>
                    <Typography.Body
                      level={3}
                      color="light"
                      className="font-mono text-xs"
                    >
                      {isolatedEnvironment.fleet}
                    </Typography.Body>
                  </div>
                </div>
              </GameServerContainerLogsUrl>
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
