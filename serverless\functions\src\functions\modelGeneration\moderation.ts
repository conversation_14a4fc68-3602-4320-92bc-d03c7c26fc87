import {
  DbModelGenerationTask,
  ModelGenerationTaskType,
} from "@nilo/firebase-schema";
import { generateObject } from "ai";
import { z } from "zod";
import { Logger } from "../../logger";
import { getTracedLLMModel } from "../llmUtils";

const moderationVersion = "v2";

// "You are a content moderation assistant for a family-friendly 3D modeling platform. Be strict about content that could be inappropriate for children or in educational settings.",
//   user: `Please assess the following content for appropriateness in a 3D model generation context for a family-friendly creative platform. This platform is used by people of all ages, including children, to create
//   3D models and objects.

// Moderation instructions
const textToModelPrompt = {
  system:
    "You are a content moderation assistant for a family-friendly 3D modeling platform. Focus on flagging only extreme content that would be harmful or inappropriate.",
  user: `Please assess the following content for appropriateness in a 3D model generation context. This platform is used by people of all ages to create 3D models and objects.

Your assessment should ONLY flag content that is genuinely harmful or inappropriate, including:
- Explicit sexual content or nudity, including clinical/medical terms like "penis", "vagina", "genitalia", etc.
- Graphic violence, gore, or disturbing imagery
- Hate speech, harassment, or discriminatory content
- Illegal drug use or drug paraphernalia
- Content that promotes harm or dangerous activities

DO NOT flag content for:
- Fictional characters (zombies, clowns, monsters, etc.)
- Horror themes or scary content
- Normal professions (butcher, doctor, etc.)
- Fantasy or sci-fi elements
- Mild violence in entertainment contexts
- General adult themes that are not explicit
- Well-known names, characters, or proper nouns
- Historical figures or events

Context: This is for generating 3D models that will be visible to users of all ages in a creative/educational environment. Only flag content that would be genuinely harmful or inappropriate for a general audience.

Content to assess: "{{prompt}}"

Provide your assessment with a boolean flagged field and a reason field. If flagged is true, provide a detailed explanation. If flagged is false, provide a brief confirmation like "Prompt to generate 3D cat model is appropriate".`,
};

const imageToModelPrompt = {
  system:
    "You are a content moderation assistant for a family-friendly 3D modeling platform. Be strict about visual content that could be inappropriate for children or in educational settings.",
  user: `Please analyze this image for appropriateness in a 3D model generation context for a family-friendly creative platform. This platform is used by people of all ages, including children above 12, to create 3D models and objects.

Your assessment should ONLY flag content that is genuinely harmful or inappropriate, including:
- Explicit sexual content or nudity
- Graphic violence, gore, or disturbing imagery
- Hate symbols or discriminatory imagery
- Illegal drug use or drug paraphernalia
- Content that promotes harm or dangerous activities

DO NOT flag content for:
- People in images (assume consent is given)
- Alcohol in casual/social contexts
- Normal everyday objects or activities
- Mild violence in entertainment contexts (games, movies, etc.)
- Clothing or fashion choices
- General adult themes that are not explicit

Context: This image will be used to generate a 3D model. Only flag content that would be genuinely harmful or inappropriate for a general audience.

Provide your assessment with a boolean flagged field and a reason field. If flagged is true, provide a detailed explanation. If flagged is false, provide a brief confirmation like "Image of 3D cat model is appropriate".`,
};

/**
 * Moderate task content on the server side as a safety net
 * @param task The model generation task to moderate
 * @param logger Logger instance
 * @returns Promise<{ allowed: boolean; reason?: string }>
 */
export async function moderateTaskContent(
  task: DbModelGenerationTask,
  logger: Logger
): Promise<{ allowed: boolean; reason?: string }> {
  logger.debug(
    `🛡️ Server-side moderation check (${moderationVersion}), task type: ${task.taskBody.type}`
  );

  switch (task.taskBody.type) {
    case ModelGenerationTaskType.text_to_model: {
      const prompt = task.taskBody.prompt;
      if (!prompt || prompt.trim().length === 0) {
        return { allowed: false, reason: "Empty prompt" };
      }

      // Check with custom OpenAI moderation using GPT-5-mini
      const moderationResult = await checkCustomTextPromptModeration(
        prompt,
        logger
      );
      if (moderationResult.flagged) {
        return {
          allowed: false,
          reason:
            moderationResult.reason ||
            "Content flagged as inappropriate for 3D model generation",
        };
      }

      break;
    }
    case ModelGenerationTaskType.image_to_model: {
      const imageUrl = task.taskBody.imageUrl;
      if (!imageUrl) {
        return { allowed: false, reason: "No image URL provided" };
      }

      // Check image with custom moderation (analyze image description/context)
      const moderationResult = await checkCustomImagePromptModeration(
        imageUrl,
        logger
      );
      if (moderationResult.flagged) {
        return {
          allowed: false,
          reason:
            moderationResult.reason ||
            "Image flagged as inappropriate for 3D model generation",
        };
      }

      break;
    }
    case ModelGenerationTaskType.animate_rig: {
      // Animation/rigging tasks don't need content moderation
      // The original content was already moderated
      break;
    }
  }

  logger.debug("✅ Server-side moderation passed");
  return { allowed: true };
}

// Zod schema for moderation assessment
const ModerationAssessmentSchema = z.object({
  reason: z
    .string()
    .describe(
      "Explanation for why content was flagged (required if flagged is true)"
    ),
  flagged: z
    .boolean()
    .describe(
      "Whether the content should be blocked or meets the moderation criteria"
    ),
});

/**
 * Custom moderation using GPT-4o-mini for 3D model generation context
 */
async function checkCustomTextPromptModeration(
  prompt: string,
  logger: Logger
): Promise<{ flagged: boolean; reason?: string }> {
  const model = getTracedLLMModel({
    provider: "openai",
    model: "gpt-5-mini",
    settings: {},
    userId: "system-moderation", // Use a system user ID for moderation
  });

  const { object: assessment } = await generateObject({
    model,
    schema: ModerationAssessmentSchema,
    temperature: 1,
    system: textToModelPrompt.system,
    prompt: textToModelPrompt.user.replace("{{prompt}}", prompt),
  });

  if (assessment.flagged) {
    logger.warn("🚨 Content flagged by moderation", {
      prompt,
      reason: assessment.reason,
    });
  }

  return {
    flagged: assessment.flagged,
    reason: assessment.reason,
  };
}

/**
 * Custom image moderation using GPT-4V for 3D model generation context
 */
async function checkCustomImagePromptModeration(
  imageUrl: string,
  logger: Logger
): Promise<{ flagged: boolean; reason?: string }> {
  logger.debug("🖼️ Processing image for moderation", { imageUrl });

  // Convert image URL to base64 data URL if needed
  const imageData = await ensureBase64DataUrl(imageUrl, logger);

  const model = getTracedLLMModel({
    provider: "openai",
    model: "gpt-5-mini",
    settings: {},
    userId: "system-moderation", // Use a system user ID for moderation
  });

  const { object: assessment } = await generateObject({
    model,
    schema: ModerationAssessmentSchema,
    temperature: 1,
    system: imageToModelPrompt.system,
    messages: [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: imageToModelPrompt.user,
          },
          {
            type: "image",
            image: imageData,
          },
        ],
      },
    ],
  });

  if (assessment.flagged) {
    logger.warn("🚨 Image flagged by moderation", {
      imageUrl,
      reason: assessment.reason,
    });
  }

  return {
    flagged: assessment.flagged,
    reason: assessment.reason,
  };
}

/**
 * Utility function to convert an image URL to a base64 data URL
 * If the URL is already a base64 data URL, returns it as-is
 * Otherwise, fetches the image and converts it to base64
 */
async function ensureBase64DataUrl(
  imageUrl: string,
  logger: Logger
): Promise<string> {
  // Check if the URL is already a base64 data URL
  if (imageUrl.startsWith("data:")) {
    logger.debug("🔄 Image is already base64 data URL, using as-is");
    return imageUrl;
  }

  logger.debug("🌐 Fetching image from URL to convert to base64", { imageUrl });

  // Fetch the image and convert to base64 since OpenAI can't access localhost
  const imageResponse = await fetch(imageUrl);
  if (!imageResponse.ok) {
    throw new Error(`Failed to fetch image: ${imageResponse.status}`);
  }

  const imageBuffer = await imageResponse.arrayBuffer();
  const base64Image = Buffer.from(imageBuffer).toString("base64");

  // Determine the MIME type from the URL or response headers
  const contentType = imageResponse.headers.get("content-type") || "image/jpeg";

  return `data:${contentType};base64,${base64Image}`;
}
