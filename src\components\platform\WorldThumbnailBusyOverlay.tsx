interface WorldThumbnailBusyOverlayProps {
  isVisible: boolean;
  text: string;
}

export const WorldThumbnailBusyOverlay = ({
  isVisible,
  text,
}: WorldThumbnailBusyOverlayProps) => {
  if (!isVisible) return null;

  return (
    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
      <p className="text-lg font-medium bg-gradient-to-r from-gray-600 via-gray-100 to-gray-600 bg-clip-text text-transparent bg-[length:200%_100%] animate-gradient">
        {text}
      </p>
    </div>
  );
};
