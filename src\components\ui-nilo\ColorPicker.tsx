import type React from "react";

import { useState, useCallback, useEffect } from "react";
import { cn } from "@/lib/utils";

interface ColorPickerProps {
  id: string;
  placeholder: string;
  defaultColor: string;
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
}

export function ColorPicker({
  id,
  placeholder,
  defaultColor,
  value,
  onChange,
  className,
}: ColorPickerProps) {
  const [color, setColor] = useState(value || defaultColor);

  // Sync internal state with value prop changes
  useEffect(() => {
    if (value !== undefined) {
      setColor(value);
    }
  }, [value]);

  const handleColorChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newColor = event.target.value;
      setColor(newColor);
      onChange?.(newColor);
    },
    [onChange]
  );

  const handleInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;
      setColor(newValue);
      onChange?.(newValue);
    },
    [onChange]
  );

  return (
    <div
      className={cn(
        "relative flex cursor-pointer items-center gap-4 rounded-nilo-default p-1 text-sm font-normal font-nilo-primary leading-none outline-none select-none",
        "text-nilo-text-secondary",
        "hover:bg-nilo-fill-secondary",
        "active:bg-nilo-fill-tertiary",
        "focus-visible:bg-nilo-fill-secondary-hover",
        "ring-2 ring-inset ring-nilo-border-secondary",
        className
      )}
    >
      <div className="w-8 h-8 bg-nilo-fill-secondary-hover rounded flex items-center justify-center overflow-hidden">
        <input
          type="color"
          value={color}
          onChange={handleColorChange}
          className="w-full h-full border-0 rounded cursor-pointer appearance-none bg-transparent [&::-webkit-color-swatch-wrapper]:p-0 [&::-webkit-color-swatch]:border-0 [&::-webkit-color-swatch]:rounded"
          title="Click to pick color"
        />
      </div>

      <input
        type="text"
        id={id}
        placeholder={placeholder}
        value={color}
        onChange={handleInputChange}
        className="flex-1 bg-transparent border-0 text-nilo-text-secondary placeholder:text-nilo-text-tertiary outline-none cursor-pointer"
        title="Click to edit color value"
      />
    </div>
  );
}
