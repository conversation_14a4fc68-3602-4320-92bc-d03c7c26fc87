import {
  GameEntityComponent,
  TriggerComponent,
  TriggerAreaDamageComponent,
  TriggerAreaDamageStateComponent,
  PendingDamageBuffer,
} from "@/core/components";
import {
  OnTriggerEnter,
  OnTriggerExit,
  CollisionEvent,
} from "@/core/ecs/events";
import {
  System,
  SystemContext,
  World,
  EntityData,
  Query,
  EntityId,
} from "@nilo/ecs";
import { TriggerAreaDamageState } from "@/types/DealDamageData";

/**
 * DealDamageTriggerSystem manages damage-dealing trigger zones
 *
 * For entities with both DealDamageComponent and TriggerComponent:
 * - Sets up trigger enter/exit event handlers
 * - Tracks entities currently within each damage trigger
 * - Queues damage to PendingDamageComponent buffer for entities in triggers
 * - Handles instant damage on trigger enter if configured
 * - Damage is processed by DamageSystem which reduces health
 */
export class DealDamageTriggerSystem implements System {
  name = "DealDamageTriggerSystem";
  entityFilter = [
    GameEntityComponent,
    TriggerComponent,
    TriggerAreaDamageComponent,
    OnTriggerEnter,
    OnTriggerExit,
  ];

  // Query for iterating over damage trigger entities in run method
  private damageQuery = new Query({
    gameEntity: GameEntityComponent,
    trigger: TriggerComponent,
    damageConfig: TriggerAreaDamageComponent,
    damageState: TriggerAreaDamageStateComponent,
  });

  onEntityAdded(
    world: World,
    entity: EntityData,
    systemContext: SystemContext
  ): void {
    const triggerId = entity.id();
    systemContext.debug("Setting up damage trigger for entity", triggerId);

    const state = new TriggerAreaDamageState();
    world.addComponent(
      triggerId,
      TriggerAreaDamageStateComponent,
      new TriggerAreaDamageState()
    );

    // Create and store handler functions for cleanup
    state.enterHandler = (event: CollisionEvent) => {
      this.handleTriggerEnter(world, triggerId, event, systemContext);
    };
    state.exitHandler = (event: CollisionEvent) => {
      this.handleTriggerExit(world, triggerId, event, systemContext);
    };

    // Notify ECS of runtime component changes
    world.markDirty(triggerId, TriggerAreaDamageStateComponent);

    // Add damage handlers to existing trigger components
    const triggerEnterHandler = entity.getComponent(OnTriggerEnter)!;
    const triggerExitHandler = entity.getComponent(OnTriggerExit)!;
    triggerEnterHandler.on(state.enterHandler);
    triggerExitHandler.on(state.exitHandler);
  }

  onEntityRemoved(
    _world: World,
    entity: EntityData,
    systemContext: SystemContext
  ): void {
    const triggerId = entity.id();
    systemContext.debug("Removing damage trigger for entity", triggerId);

    // Unsubscribe from OnTriggerEnter and OnTriggerExit components
    const state = entity.getComponent(TriggerAreaDamageStateComponent);
    if (state?.enterHandler) {
      const triggerEnterHandler = entity.getComponent(OnTriggerEnter)!;
      triggerEnterHandler.off(state.enterHandler);
    }

    if (state?.exitHandler) {
      const triggerExitHandler = entity.getComponent(OnTriggerExit)!;
      triggerExitHandler.off(state.exitHandler);
    }

    _world.removeComponent(triggerId, TriggerAreaDamageStateComponent);
  }

  private handleTriggerEnter(
    world: World,
    triggerId: EntityId,
    event: CollisionEvent,
    systemContext: SystemContext
  ): void {
    if (!event.otherEntity) return;

    const enteredEntityId = event.otherEntity.id;

    systemContext.debug(
      `Entity ${enteredEntityId} entered damage trigger ${triggerId}`
    );

    // Get damage components for this trigger
    const triggerEntityData = world.entityData(triggerId)!;
    const config = triggerEntityData.getComponent(TriggerAreaDamageComponent)!;
    const state = triggerEntityData.getComponent(
      TriggerAreaDamageStateComponent
    )!;

    // Add to tracking using runtime component
    state.entitiesCurrentlyInTrigger.add(enteredEntityId);
    systemContext.debug(
      `Entity ${enteredEntityId} added to damage trigger tracking`
    );

    // Apply instant damage on enter if configured
    if (config.damageOnEnter && config.damageOnEnter > 0) {
      systemContext.debug(`queuing instant damage: ${config.damageOnEnter}`);
      this.queueDamage(
        world,
        enteredEntityId,
        triggerId,
        config.damageOnEnter,
        systemContext
      );
    }

    // Initialize damage timing
    state.timeAccumulatorPerEntity.set(enteredEntityId, 0);

    // Notify ECS of runtime component changes
    world.markDirty(triggerId, TriggerAreaDamageStateComponent);
  }

  private handleTriggerExit(
    world: World,
    triggerId: EntityId,
    event: CollisionEvent,
    systemContext: SystemContext
  ): void {
    if (!event.otherEntity) return;

    const exitedEntityId = event.otherEntity.id;
    systemContext.debug(
      `Entity ${exitedEntityId} exited damage trigger ${triggerId}`
    );

    // Get runtime component and remove from tracking
    const triggerEntityData = world.entityData(triggerId);
    const runtimeComponent = triggerEntityData?.getComponent(
      TriggerAreaDamageStateComponent
    );

    if (runtimeComponent) {
      // Remove from tracking using runtime component
      runtimeComponent.entitiesCurrentlyInTrigger.delete(exitedEntityId);
      runtimeComponent.timeAccumulatorPerEntity.delete(exitedEntityId);

      // Notify ECS of runtime component changes
      world.markDirty(triggerId, TriggerAreaDamageStateComponent);
    }
  }

  private queueDamage(
    world: World,
    targetEntityId: EntityId,
    attackerEntityId: EntityId,
    damageAmount: number,
    systemContext: SystemContext
  ): void {
    const entityData = world.entityData(targetEntityId);
    if (!entityData?.hasComponent(PendingDamageBuffer)) {
      systemContext.debug(
        `Entity ${targetEntityId} has no PendingDamageComponent, skipping damage`
      );
      return; // Entity has no pending damage buffer
    }

    const pendingDamage = entityData.getComponent(PendingDamageBuffer)!;
    pendingDamage.add({ damageAmount, damageSource: attackerEntityId });

    // Notify ECS of component changes
    world.markDirty(targetEntityId, PendingDamageBuffer);

    systemContext.debug(
      `Queued ${damageAmount} damage for entity ${targetEntityId}`
    );
  }

  // Run continuously to apply damage over time and process pending setups
  run(world: World, systemContext: SystemContext): void {
    // Process all damage trigger entities using query
    this.damageQuery.forEach(
      world,
      (entityData, { damageConfig: config, damageState: state }) => {
        const triggerId = entityData.id();
        const entitiesInTrigger = state.entitiesCurrentlyInTrigger;
        const timeAccumulatorForTrigger = state.timeAccumulatorPerEntity;

        if (entitiesInTrigger.size === 0) {
          return;
        }

        if (config.damagePerSecond <= 0) {
          return;
        }

        // Apply damage to each entity in this trigger
        for (const entityId of entitiesInTrigger) {
          let entityTimeAccumulator = timeAccumulatorForTrigger.get(entityId);
          if (entityTimeAccumulator === undefined) continue;

          entityTimeAccumulator += systemContext.deltaTime;

          if (entityTimeAccumulator >= 1 / config.ticksPerSecond) {
            this.queueDamage(
              world,
              entityId,
              triggerId,
              config.damagePerSecond * (1 / config.ticksPerSecond),
              systemContext
            );

            // Update damage timing
            timeAccumulatorForTrigger.set(entityId, 0);
          } else {
            timeAccumulatorForTrigger.set(entityId, entityTimeAccumulator);
          }
          // Notify ECS of runtime component changes
          world.markDirty(triggerId, TriggerAreaDamageStateComponent);
        }
      }
    );
  }
}
