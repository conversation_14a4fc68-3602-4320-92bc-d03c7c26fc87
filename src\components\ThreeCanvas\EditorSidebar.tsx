import {
  Cog6ToothIcon,
  GlobeAmericasIcon,
  ListBulletIcon,
} from "@heroicons/react/24/solid";

import { But<PERSON> } from "@/components/ui/button";
import { LeftPanelType } from "@/contexts/EditorUIContext";
import { useFeatureFlag } from "@/hooks/useFeatureFlags";

const ENABLE_HOVER_OPEN = true;
const DISABLE_HOVER_OPEN_WHEN_PINNED = true;
const ENABLE_PINNING_ON_CLICK = true;
const ENABLE_CLOSE_ON_CLICK_PINNED = true;

//// TODO: hover out and panel pinning

export function EditorSidebar({
  currentPanel,
  isCurrentPanelPinned,
  toggleLeftPanel,
  setCurrentPanelPinned,
}: {
  currentPanel: LeftPanelType | null;
  isCurrentPanelPinned: boolean;
  toggleLeftPanel: (panel: LeftPanelType, open?: boolean) => void;
  setCurrentPanelPinned: (pinned: boolean) => void;
}) {
  // Feature flag for entities panel
  const entitiesPanelEnabled = useFeatureFlag("builderUiEntitiesPanel");

  const panelButtons = [
    {
      id: LeftPanelType.Entities,
      icon: ListBulletIcon,
      label: "Entities",
      isActive: currentPanel === LeftPanelType.Entities,
      hidden: !entitiesPanelEnabled,
    },
    {
      id: LeftPanelType.EnvironmentSettings,
      icon: GlobeAmericasIcon,
      label: "Environment Settings",
      isActive: currentPanel === LeftPanelType.EnvironmentSettings,
      hidden: false,
    },
    {
      id: LeftPanelType.DevSettings,
      icon: Cog6ToothIcon,
      label: "Dev Settings",
      isActive: currentPanel === LeftPanelType.DevSettings,
      hidden: false,
    },
  ];

  const handleHover = (panel: LeftPanelType) => {
    if (!ENABLE_HOVER_OPEN) {
      return;
    }
    if (DISABLE_HOVER_OPEN_WHEN_PINNED && isCurrentPanelPinned) {
      return;
    }
    toggleLeftPanel(panel, true);
  };

  const handleClick = (panel: LeftPanelType) => {
    if (currentPanel !== panel) {
      toggleLeftPanel(panel, true);
    }

    if (ENABLE_PINNING_ON_CLICK && !isCurrentPanelPinned) {
      setCurrentPanelPinned(true);
    }

    if (ENABLE_CLOSE_ON_CLICK_PINNED && isCurrentPanelPinned) {
      toggleLeftPanel(panel, false);
    }
  };

  return (
    <div className="fixed left-[1rem] top-[4.5rem] h-full pointer-events-auto z-50">
      <div className="bg-gray-800/25 backdrop-blur-sm rounded-full flex flex-col items-center space-y-1">
        {panelButtons
          .filter(({ hidden }) => !hidden)
          .map(({ id, icon: Icon, label, isActive }) => (
            <Button
              key={id}
              onClick={() => handleClick(id)}
              onMouseEnter={() => handleHover(id)}
              variant="ghost"
              size="icon"
              className={`rounded-full focus-visible:ring-0 hover:ring-transparent ${isActive ? "bg-nilo-fill-secondary/60" : "bg-transparent"}`}
              title={label}
            >
              <Icon />
            </Button>
          ))}
      </div>
    </div>
  );
}
