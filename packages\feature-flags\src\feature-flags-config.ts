/**
 * Helper type to validate feature flag structure at compile time
 * @typedef {Object} FeatureFlagConfigItem
 * @property {string} name - The feature flag name
 * @property {string} description - Description of the feature flag
 * @property {boolean} enabled - Whether the flag is enabled by default (for local development)
 * @property {T} on - The value served when the flag is enabled
 * @property {T} off - The value served when the flag is disabled (default)
 */
type FeatureFlagConfigItem<T = unknown> = {
  name: string;
  description: string;
  enabled: boolean;
  on: T;
  off: T;
};

/**
 * Feature flags configuration array
 * Each flag must have enabled, on, and off values
 * The 'as const satisfies' ensures type safety while maintaining const assertion
 */
export const FEATURE_FLAGS_CONFIG = [
  {
    name: "addPlayerControls",
    description: "Enable player controls option in the toolbar",
    enabled: true,
    on: true,
    off: false,
  },
  {
    name: "intuitiveMultiSelection",
    description:
      "Change multiselection - select if any piece of entity is inside of the frame",
    enabled: true,
    on: true,
    off: false,
  },
  {
    name: "rigCharacterModel",
    description:
      "Enable character rigging functionality in toolbar and radial menu",
    enabled: true,
    on: true,
    off: false,
  },
  {
    name: "builderUiSidebar",
    description: "Enable builder UI sidebar",
    enabled: false,
    on: true,
    off: false,
  },
  {
    name: "builderUiInspector",
    description: "Enable builder UI inspector",
    enabled: false,
    on: true,
    off: false,
  },
  {
    name: "builderUiEntitiesPanel",
    description: "Enable entities panel in the main menu",
    enabled: false,
    on: true,
    off: false,
  },
  {
    name: "testObjectFlag",
    description: "Test object flag functionality",
    enabled: false,
    on: { tier: "premium", limit: 100 },
    off: { tier: "free", limit: 10 },
  },
  {
    name: "testStringFlag",
    description: "Test string flag functionality",
    enabled: true,
    on: "this flag is on",
    off: "this flag is off",
  },
  {
    name: "testBooleanFlag",
    description: "Test boolean flag functionality",
    enabled: false,
    on: true,
    off: false,
  },
  {
    name: "useServerNetworking",
    description: "Enables next-gen networking to replace Liveblocks",
    enabled:
      process.env.USE_SERVER_URL !== undefined &&
      process.env.USE_SERVER_URL !== "",
    on: true,
    off: false,
  },
] as const satisfies readonly FeatureFlagConfigItem[];
