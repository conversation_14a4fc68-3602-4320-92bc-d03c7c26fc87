import {
  Object3D,
  BoxGeometry,
  Mesh,
  Color,
  SphereGeometry,
  CylinderGeometry,
  ConeGeometry,
  Vector3,
  EdgesGeometry,
  LineDashedMaterial,
  LineSegments,
  MeshStandardMaterial,
  BufferGeometry,
  BufferAttribute,
  InterleavedBufferAttribute,
  Material,
  ExtrudeGeometry,
  Path,
  Shape,
  Box3,
  TorusGeometry,
  TorusKnotGeometry,
  CapsuleGeometry,
  OctahedronGeometry,
  TetrahedronGeometry,
  CircleGeometry,
  MeshBasicMaterial,
  DoubleSide,
} from "three";
import isEqual from "fast-deep-equal/es6";
import { EntityHierarchyController } from "../util/hierarchy/EntityHierarchyController";
import { assertNotNull, assertTrue, assertUnreachable } from "../util/Assert";
import {
  LAYERS,
  setObjectLayer,
  setObjectRaycastable,
} from "../util/RaycastUtils";
import { mergeObjectsDeep, simpleDeepClone } from "../util/JsObjectUtils";
import { MeshComponent } from "../ecs/behaviors/mesh";
import { PhysicsUserDataComponent } from "../ecs/behaviors/physics";
import {
  TriggerComponent,
  HealthComponent,
  TriggerAreaDamageComponent,
  RequestRespawnComponent,
  RespawnPointTag,
} from "../components";
import { EntityOriginTypes } from "./Entity";
import { EntityMaterialController } from "./common/EntityMaterialController";
import { physicsSetDragging } from "./common/physicsSetDragging";
import { NetworkEntity } from "./NetworkEntity";
import { getOriginOffset } from "./common/getEntityOriginOffset";
import { EmissiveFresnelShader } from "@/core/shader";
import {
  EntityType,
  PrimitiveEntityData,
  PrimitiveType,
  TransformationData,
  TransformationType,
} from "@/liveblocks.config";
import { Client } from "@/core/client";

import { globalDelegates } from "@/core/client/globalDelegates";
import { PhysicsUserData } from "@/types";
import { EntityId, makeUniqueKey } from "@nilo/ecs";
import {
  PrimitiveData,
  primitiveDataSchema,
  TransformationDataSchema,
} from "@nilo/network";
import { calculatePhysicsBodyOffsetMatrix } from "@/physics/helpers/calculatePhysicsBodyOffsetMatrix";
import { FriendlySchema } from "@nilo/ecs-networking";

export const dashedMaterial = new LineDashedMaterial({
  color: 0xffffff,
  dashSize: 0.1,
  gapSize: 0.1,
  linewidth: 1,
});

export const vDown = new Vector3(0, -1, 0);
export const vLeft = new Vector3(1, 0, 0);
const pos = new Vector3();

export class PrimitiveEntity extends NetworkEntity<PrimitiveData> {
  override rootNode: Object3D = null!;
  // three.js local scenegraph
  private originType = EntityOriginTypes.NativeOrigin;
  // original primitive mesh
  private originalPrimitive: Mesh | null = null;
  // primitive preview material
  private primitivePreviewMaterial: EmissiveFresnelShader;
  // primitive set material
  private primitiveSetMaterial: MeshStandardMaterial;
  // primitiveType for other user to set entity
  // Since the member variable _primitiveType is constrained to the type "PrimitiveType",
  // we can run onPrimitiveTypeChange when we create a "Cube",
  // since the type will not change, and that has the side effect of not generating the geometry.
  // We therefore need _primitiveObject3DCreated to check that the generation has occurred or not.
  private _primitiveType: PrimitiveType = "None";
  private _primitiveObject3DCreated: boolean = false;
  // entity type
  public override type:
    | EntityType.PrimitiveEntity
    | EntityType.PreviewPrimitive = EntityType.PrimitiveEntity;
  //user of the id who created this primitive entity
  public _userId: EntityId | null = null;
  private dragMarker: LineSegments<BufferGeometry, LineDashedMaterial> | null =
    null;
  // primitive color
  public _color: number = 0xffffff;

  private originalColor: Color | null = null;

  private _transformations: TransformationData[] = [];

  // children
  public readonly hierarchyController: EntityHierarchyController;

  public readonly materialController = new EntityMaterialController(this);

  public physicsUserData: PhysicsUserData = {};

  // constructor
  constructor(id: EntityId | null = null, userId: EntityId | null = null) {
    super(true, id, true);
    this.setRootNode(new Object3D());
    this.hierarchyController = new EntityHierarchyController(this);

    this.primitivePreviewMaterial = new EmissiveFresnelShader({
      color: new Color(0x757373),
      minOpacity: 0.05,
      maxOpacity: 0.25,
      power: 1.5,
    });
    this._userId = userId;

    this.primitiveSetMaterial = new MeshStandardMaterial({
      color: new Color(this._color),
    });

    this._alwaysLocal = true;
  }

  public getMesh() {
    return this.originalPrimitive;
  }

  public get primitiveType(): PrimitiveType {
    return this._primitiveType;
  }

  setSelected(value: boolean) {
    const mesh = this.getMesh();
    if (mesh) setObjectLayer(mesh, LAYERS.SELECTION, value);
  }

  setHovered(value: boolean) {
    const mesh = this.getMesh();
    if (mesh) setObjectLayer(mesh, LAYERS.HOVER, value);
  }

  override getOffset(): Vector3 {
    return (this.getMesh() || new Mesh()).position
      .clone()
      .multiply(this._scale)
      .applyQuaternion(this._orientation);
  }

  setOriginType(type: EntityOriginTypes, _dirty = true) {
    const prevOffset = this.getOffset();
    const prevPosition = new Vector3();
    this.getSimulatedPosition(prevPosition);
    this.updateOriginType(type);
    if (this.originType === type) return;

    this.originType = type;
    const offsetDifference = prevOffset.sub(this.getOffset());
    if (_dirty) {
      this.setPosition(prevPosition.add(offsetDifference), false);
      Client.markEntityAsDirty(this, true);
    }

    const bodyControl = this.getPhysicsBodyControl();
    bodyControl?.updatePhysicsBodyOffsetMatrix(
      calculatePhysicsBodyOffsetMatrix(this).toArray()
    );
  }

  public getGeometryBox() {
    const mesh = this.getMesh();
    if (!mesh) return new Box3();
    if (!mesh.geometry.boundingBox) mesh.geometry.computeBoundingBox();

    return mesh.geometry.boundingBox || new Box3();
  }

  updateOriginType(type: EntityOriginTypes) {
    const mesh = this.getMesh();
    if (!mesh) return;
    mesh.position.copy(getOriginOffset(mesh, type));
    mesh.updateMatrix();
    mesh.updateMatrixWorld();
  }

  getOriginType() {
    return this.originType;
  }

  public override addDragMarker(): void {
    this.dragMarker =
      this.dragMarker || new LineSegments(new BufferGeometry(), dashedMaterial);
    const circle = new Mesh(
      new CircleGeometry(0.2).rotateY(Math.PI / 2),
      new MeshBasicMaterial({
        color: 0xffffff,
        depthTest: false,
        side: DoubleSide,
        transparent: true,
        opacity: 0.15,
      })
    );
    circle.position.set(0, 2, 0);
    this.dragMarker.add(circle);
    this.dragMarker.visible = false;

    Client.scene.add(this.dragMarker);
    this.updateDragMarker();
  }

  public override updateDragMarker(): void {
    if (this.dragMarker) {
      this.getSimulatedPosition(pos);

      const [point, _angle, normal] = Client.raycastGround(
        [pos, vDown],
        [this]
      );
      if (point) {
        this.dragMarker.visible = true;
        this.dragMarker.geometry.setFromPoints([pos, point]);
        this.dragMarker.computeLineDistances();
        this.dragMarker.children[0].position.copy(point);
        this.dragMarker.children[0].quaternion.setFromUnitVectors(
          vLeft,
          normal || vLeft
        );
      } else {
        this.dragMarker.visible = false;
      }
    }
  }
  public override removeDragMarker(): void {
    if (this.dragMarker) {
      Client.scene.remove(this.dragMarker);
      this.dragMarker.geometry.dispose();
      this.dragMarker.material.dispose();
      this.dragMarker = null;
    }
  }

  public override setDragging(value: boolean, _dirty = true) {
    if (this._dragging === value) {
      return;
    }

    super.setDragging(value, _dirty);

    const physicsData = this.getPhysicsUserData();
    assertNotNull(physicsData, "Physics user data should not be null");

    //While the entity is being dragged, we want to temporarly make it static
    physicsSetDragging(this, physicsData, value);
  }

  public setPrimitiveSetMaterial(value: MeshStandardMaterial) {
    this.primitiveSetMaterial = value;
  }

  public previewColor(color: number) {
    if (!this.originalColor) {
      this.originalColor = this.primitiveSetMaterial.color.clone();
    }
    this.primitiveSetMaterial.color.setHex(color);
    Client.markEntityAsDirty(this, false);
  }

  public revertColorPreview() {
    if (this.originalColor) {
      this.primitiveSetMaterial.color.copy(this.originalColor);
      this.originalColor = null;
      Client.markEntityAsDirty(this, false);
      this.onColorChange();
    }
  }

  public setColor(value: number, _dirty = true) {
    if (this._color !== value) {
      this._color = value;
      this.onColorChange();
      if (_dirty) {
        Client.markEntityAsDirty(this, false);
      }
      this.originalColor = null;
    }
  }

  // getter of the color
  public getColor() {
    return this._color;
  }

  // Transformation methods
  public addTransformation(transformation: TransformationData, _dirty = true) {
    this._transformations.push(transformation);
    this.applyTransformations();
    if (_dirty) {
      Client.markEntityAsDirty(this, false);
    }
  }

  public removeTransformation(index: number, _dirty = true) {
    if (index >= 0 && index < this._transformations.length) {
      this._transformations.splice(index, 1);
      this.applyTransformations();
      if (_dirty) {
        Client.markEntityAsDirty(this, false);
      }
    }
  }

  public getTransformations(): TransformationData[] {
    return [...this._transformations];
  }

  // setter of the primitiveType
  public setPrimitiveType(value: PrimitiveType, _dirty = true) {
    if (
      this._primitiveType != value ||
      this._primitiveObject3DCreated == false
    ) {
      this._primitiveType = value;
      if (value) {
        try {
          this.createMeshForPrimitiveType(value, _dirty);
        } catch (error) {
          console.error(
            `PrimitiveEntity: Error creating mesh for primitive type ${value}\n\n`,
            error
          );
        }
      }
      if (_dirty) {
        Client.markEntityAsDirty(this, false);
      }
    }
  }

  // getter of the primitiveType
  public getPrimitiveType() {
    return this._primitiveType;
  }

  // Function to change the color of the entity
  //@param {}
  //@return {} no return
  private onColorChange() {
    if (this.primitivePreviewMaterial)
      this.primitivePreviewMaterial.setColor(new Color(this._color));

    if (this.primitiveSetMaterial)
      this.primitiveSetMaterial.color = new Color(this._color);
  }

  private updateFrame(primitiveEntity: Mesh | null) {
    if (!primitiveEntity) return;
    const firstChild = primitiveEntity.children[0] as LineSegments | null;
    if (!firstChild) return;
    const lineDistances = firstChild.geometry.attributes.lineDistance.array;
    for (let i = 0; i < lineDistances.length; i++) {
      lineDistances[i] += 0.01;
    }

    firstChild.geometry.attributes.lineDistance.needsUpdate = true;
  }

  public getHeight(object: Mesh): number {
    const box = new Box3().setFromObject(object);
    const size = box.getSize(new Vector3());
    return size.y;
  }

  public getSize() {
    const mesh = this.getMesh();
    if (!mesh) {
      return new Vector3();
    }
    const box = new Box3().setFromObject(mesh.clone());
    const size = box.getSize(new Vector3());

    return size.multiply(this._scale);
  }

  public getGlobalSize() {
    const box = new Box3().setFromObject(this.rootNode.clone());
    const size = box.getSize(new Vector3());

    size.x = Math.abs(size.x);
    size.y = Math.abs(size.y);
    size.z = Math.abs(size.z);
    return size;
  }

  // Function to check type of entity to generate
  //@param {type of the entity in string}
  //@return { primitive entity }
  private createMeshForPrimitiveType(type: PrimitiveType, _dirty = true) {
    if (!this.primitiveSetMaterial) return;

    const primitiveMesh = _primitiveObjectCreate(
      type,
      this.primitiveSetMaterial
    );

    if (!primitiveMesh) return;

    setObjectRaycastable(primitiveMesh);
    primitiveMesh.renderOrder = 1;
    primitiveMesh.position.y = 0;
    primitiveMesh.name = this.id;

    primitiveMesh.castShadow = true;
    primitiveMesh.receiveShadow = true;

    this.originalPrimitive = primitiveMesh;
    this.rootNode.add(primitiveMesh);
    this.rootNode.updateMatrixWorld();

    if (_dirty) {
      Client.markEntityAsDirty(this, false);
    }
    this._primitiveObject3DCreated = true;

    this.materialController.onChange(); // ensure the material is updated once the primitive is created

    if (this.isInWorld) {
      const physicsUserData: PhysicsUserData = {
        isSceneObject: true,
        isPrimitive: true,
      };

      this.setPhysicsUserData(physicsUserData, _dirty);
    }
    return primitiveMesh;
  }

  getPhysicsUserData() {
    return this.id
      ? Client.world.getComponent(this.id, PhysicsUserDataComponent)
      : null;
  }

  setPhysicsUserData(physicsUserData: PhysicsUserData, _dirty = true) {
    if (!physicsUserData) return;
    const oldPhysicsUserData = this.getPhysicsUserData() || {};

    const newPhysicsUserData = mergeObjectsDeep(
      oldPhysicsUserData,
      physicsUserData
    );
    const clonedData = simpleDeepClone(newPhysicsUserData);
    if (this.originalPrimitive) {
      this.originalPrimitive.userData = newPhysicsUserData;
    }

    this.data().addComponent(PhysicsUserDataComponent, clonedData);

    if (clonedData.physics?.isSensor) {
      Client.world.addComponent(this.id, TriggerComponent, {});
    } else {
      Client.world.removeComponent(this.id, TriggerComponent);
    }

    // TODO:this is not enough, if a physics property changes we need to update the live entity
    if (this.isInWorld && _dirty) {
      Client.markEntityAsDirty(this, false);
    }
  }

  public triggerSetup(active: boolean) {
    const physicsUserData = this.getPhysicsUserData() || {};
    const updatedPhysicsUserData: PhysicsUserData = {
      ...physicsUserData,
      physics: {
        ...physicsUserData.physics,
        isSensor: active,
        motionType: active ? "kinematic" : "dynamic",
        gravityFactor: active ? 0 : 1,
      },
    };
    this.setPhysicsUserData(updatedPhysicsUserData);

    this.getPhysicsBodyControl()?.updatePhysicsOptions({
      ...physicsUserData.physics,
      ...updatedPhysicsUserData.physics,
    });
  }

  public onPreview(primitiveType: PrimitiveType, previewPos: Vector3) {
    let tempPrimitive: Object3D | undefined;

    if (this.isEntityPresent(primitiveType)) {
      // Fetch the primitive by its name
      tempPrimitive = Client.scene.getObjectByName(primitiveType);
      //  the primitive from the scene
      const globalSize = this.getGlobalSize();
      if (tempPrimitive) {
        tempPrimitive.visible = true;
        tempPrimitive.position.set(
          previewPos.x,
          previewPos.y + globalSize.y * 0.5,
          previewPos.z
        );
        tempPrimitive.updateMatrixWorld();
      }
    }
  }

  // Function to set type of entity to generate
  //@param {type of the entity in string, the position of radial menu in vector3}
  //@return {} no return
  public setPreview(type: PrimitiveType) {
    if (!this.primitivePreviewMaterial) return;

    const previewGeometry = _primitiveGeometryCreate(type);

    if (!previewGeometry) return;

    const primitiveMesh = new Mesh(
      previewGeometry,
      this.primitivePreviewMaterial
    );

    this.type = EntityType.PreviewPrimitive;
    primitiveMesh.add(generateLines(previewGeometry));
    primitiveMesh.renderOrder = 1;
    primitiveMesh.name = type;

    this.rootNode.add(primitiveMesh);

    primitiveMesh.onBeforeRender = () => {
      this.updateFrame(primitiveMesh);
    };
  }

  // Function to revert the preview of the entity
  //@param {type of the entity to revert in string}
  //@return {} no return
  public onRevert(primitiveType: string) {
    let tempPrimitive: Object3D | undefined;

    if (this.isEntityPresent(primitiveType)) {
      // Fetch the primitive by its name
      tempPrimitive = Client.scene.getObjectByName(primitiveType);
      // Remove the primitive from the scene
      if (tempPrimitive) {
        Client.removeEntity(this);
      }
    }
  }

  // Function to check if an entity is present in the scene
  //@param { name of the entity}
  //@return {} no return
  public isEntityPresent(entityName: string) {
    let entityFound = false;

    Client.scene.traverse(function (object: Object3D) {
      if (object.name === entityName) {
        entityFound = true;
      }
    });

    return entityFound;
  }

  public setRootParent(parent: Object3D) {
    parent.add(this.rootNode);
  }
  //#endregion

  //#region JSON

  public override onRoomJoin(): void {
    super.onRoomJoin();

    // Tell other systems that this entity has a mesh
    this.data().addComponent(MeshComponent, this.getMesh());

    if (this.originalPrimitive) {
      // This assert may indicate either a prefab issue, or onRoomJoin being called in a deferred context
      assertTrue(
        this.data().hasComponent(PhysicsUserDataComponent),
        "Expected physicsUserData to be present before calling `onRoomJoin`"
      );

      // not set for preview primitives as they should not have physics

      // Notify physics system entity spawned and mesh is ready
      globalDelegates.addEntity.invoke(this);
    }
  }

  public override toJSON(): PrimitiveEntityData {
    const health =
      Client.world.getComponent(this.id, HealthComponent) ?? undefined;
    const dealDamage =
      Client.world.getComponent(this.id, TriggerAreaDamageComponent) ??
      undefined;
    const respawn =
      Client.world.getComponent(this.id, RequestRespawnComponent) ?? undefined;
    const respawnPoint =
      Client.world.getComponent(this.id, RespawnPointTag) ?? undefined;

    return {
      ...super.toJSON(),
      type: this.type as EntityType.PrimitiveEntity,
      originType: this.getOriginType(),
      primitiveType: this._primitiveType,
      userId: this._userId || null,
      color: this._color,
      physicsUserData: this.data().getComponent(PhysicsUserDataComponent)!,
      transformations:
        this._transformations.length > 0 ? this._transformations : undefined,
      ...this.hierarchyController.toJSON(),
      ...this.materialController.toJSON(),
      health: health,
      triggerAreaDamage: dealDamage,
      respawn: respawn,
      respawnPoint: respawnPoint,
    };
  }

  public static override readonly SCHEMA = new FriendlySchema<PrimitiveData>(
    primitiveDataSchema
  );

  public static override readonly UNIQUE_ID = makeUniqueKey("PrimitiveEntity");

  public override serialize(): PrimitiveData {
    const transforms = this.getTransformations().map((t) => {
      return {
        type: t.type,
        params: JSON.stringify(t.params), // Serialize params as JSON string
      } as TransformationDataSchema;
    });

    return {
      transform: this.serializeTransform(),
      color: this._color,
      primitiveType: this._primitiveType,
      originType: this.originType,
      userId: this._userId,
      transforms: transforms,
      material: this.materialController.toNetworkData(),
      dragging: this._dragging,
    };
  }

  public override deserialize(msg: PrimitiveData): void {
    // Update position
    this.deserializeTransform(msg.transform);

    this._userId = msg.userId;
    this.rootNode.name = `${EntityType.PrimitiveEntity}-${msg.primitiveType}`;
    this.setOriginType(msg.originType as EntityOriginTypes, false);
    this.setPrimitiveType(msg.primitiveType as PrimitiveType, false);
    this.setColor(msg.color, false);

    // Apply dragging state
    this.setDragging(msg.dragging, false);

    // Apply material data
    if (msg.material) {
      this.materialController.fromNetworkData(msg.material);
    }

    // Handle transformations
    if (msg.transforms) {
      this._transformations = msg.transforms.map((t) => ({
        type: t.type as TransformationType,
        params: JSON.parse(t.params), // Deserialize params from JSON string
      }));
      this.applyTransformations();
    }

    // We do not need to set physicsUserData, as that is an externally synchronized component
  }

  public override fromJSON(json: PrimitiveEntityData) {
    const data = this.data();

    // Add physicsUserData if not added earlier (such as constructing from Prefab)
    //
    // Ensure this is added before `super.fromJSON` as it may end up calling `setDragging` and trying to modify physicsData
    if (!data.hasComponent(PhysicsUserDataComponent)) {
      data.addComponent(PhysicsUserDataComponent, json.physicsUserData || {});
    }

    super.fromJSON(json);
    this.rootNode.name = `${json.type}-${json.primitiveType}`;
    if (json.originType) this.setOriginType(json.originType, false);

    // currently when duplicating a primitive entity,
    // settings all parameters of the new entity happens via .fromJSON().
    // We previously called .setPrimitiveType() before .setPhysicsUserData().
    // But since .setPrimitiveType() triggers the creation of the physics objects,
    // that creation would be done with incorrect physics properties,
    // as those would be overriden shortly after by .setPhysicsUserData().
    // So for now, we call .setPhysicsUserData() first.
    // But we should probably find more robust fixes/architecture.
    // A possibility that comes to mind is that no side effect is possible while we are in .fromJSON(),
    // and any side effect that would have been triggered would only be stored until this function is completed,
    // and actually run after that.
    // NOTE: the problem is most likely similar for MeshEntity.
    // NOTE 2: currently physicsUserData is reset in .onSet(), so maybe this order of operations still does not work.
    //

    // For existing entities, push new physics data explicitely since the physics body is already created
    if (this.originalPrimitive && this.isInWorld) {
      // TODO: NILO-905 replace by system listening for component changes instead. Extract to completely separate component rather than component and setter in PrimitiveEntity

      const oldPhysicsUserData = this.getPhysicsUserData() || {};
      if (!isEqual(oldPhysicsUserData.physics, json.physicsUserData.physics)) {
        this.setPhysicsUserData(json.physicsUserData, false);
      }
    }

    this.setPrimitiveType(json.primitiveType, false);

    this._userId = json.userId;
    this.setColor(json.color, false);

    // Load transformations
    this._transformations = json.transformations || [];

    this.hierarchyController.fromJSON(json);
    this.materialController.fromJSON(json);
    const isAlive = Client.world.isAlive(this.id);
    if (!isAlive) {
      console.error("Primitive despawned before onRoomJoin could run");
      return;
    }

    // Handle health component
    setupHealth(this.id);

    function setupHealth(id: EntityId) {
      if (json.health !== undefined) {
        Client.world.addComponent(id, HealthComponent, json.health);
      } else {
        Client.world.removeComponent(id, HealthComponent);
      }

      // Handle deal damage component
      if (json.triggerAreaDamage !== undefined) {
        Client.world.addComponent(
          id,
          TriggerAreaDamageComponent,
          json.triggerAreaDamage
        );
      } else {
        Client.world.removeComponent(id, TriggerAreaDamageComponent);
      }

      // Handle respawn component
      if (json.respawn !== undefined) {
        Client.world.addComponent(id, RequestRespawnComponent, json.respawn);
      } else {
        Client.world.removeComponent(id, RequestRespawnComponent);
      }

      // Handle respawn point tag
      if (json.respawnPoint !== undefined) {
        Client.world.addComponent(id, RespawnPointTag, json.respawnPoint);
      } else {
        Client.world.removeComponent(id, RespawnPointTag);
      }
    }
  }

  // Apply transformations to geometry
  private applyTransformations() {
    if (!this.originalPrimitive?.geometry) return;

    // Reset to original geometry first
    this.resetGeometry();

    // Apply each transformation in sequence
    for (const transformation of this._transformations) {
      this.applyTransformation(transformation);
    }
  }

  // Apply a single transformation
  private applyTransformation(transformation: TransformationData) {
    if (!this.originalPrimitive?.geometry) return;

    const geometry = this.originalPrimitive.geometry;
    const positions = geometry.attributes.position;

    switch (transformation.type) {
      case "Twist":
        this.applyTwistTransformation(positions, transformation.params);
        break;
      case "Taper":
        this.applyTaperTransformation(positions, transformation.params);
        break;
      case "Bend":
        this.applyBendTransformation(positions, transformation.params);
        break;
      case "Array":
        this.applyArrayTransformation(transformation.params);
        return; // Array doesn't modify geometry directly
      case "Wave":
        this.applyWaveTransformation(positions, transformation.params);
        break;
      case "Stretch":
        this.applyStretchTransformation(positions, transformation.params);
        break;
      case "Shear":
        this.applyShearTransformation(positions, transformation.params);
        break;
      case "Noise":
        this.applyNoiseTransformation(positions, transformation.params);
        break;
    }

    positions.needsUpdate = true;
    geometry.computeVertexNormals();
  }

  // Reset geometry to original state
  private resetGeometry() {
    if (!this.originalPrimitive) return;

    // Recreate original geometry
    const newGeometry = _primitiveGeometryCreate(this._primitiveType);
    if (newGeometry) {
      this.originalPrimitive.geometry.dispose();
      this.originalPrimitive.geometry = newGeometry;
    }
  }

  // Twist transformation
  private applyTwistTransformation(
    positions: BufferAttribute | InterleavedBufferAttribute,
    params: Record<string, number>
  ) {
    const angle = params.angle || Math.PI;
    const height = params.height || 1;

    for (let i = 0; i < positions.count; i++) {
      const x = positions.getX(i);
      const y = positions.getY(i);
      const z = positions.getZ(i);

      const normalizedY = y / height;
      const twist = angle * normalizedY;

      const newX = x * Math.cos(twist) - z * Math.sin(twist);
      const newZ = x * Math.sin(twist) + z * Math.cos(twist);

      positions.setX(i, newX);
      positions.setZ(i, newZ);
    }
  }

  // Taper transformation
  private applyTaperTransformation(
    positions: BufferAttribute | InterleavedBufferAttribute,
    params: Record<string, number>
  ) {
    const topScale = params.topScale || 0.1;
    const height = params.height || 1;

    for (let i = 0; i < positions.count; i++) {
      const x = positions.getX(i);
      const y = positions.getY(i);
      const z = positions.getZ(i);

      const normalizedY = (y + height / 2) / height;
      const scale = 1 + (topScale - 1) * normalizedY;

      positions.setX(i, x * scale);
      positions.setZ(i, z * scale);
    }
  }

  // Bend transformation
  private applyBendTransformation(
    positions: BufferAttribute | InterleavedBufferAttribute,
    params: Record<string, number>
  ) {
    const bendAngle = params.bendAngle || Math.PI / 4;
    const bendAxis = params.bendAxis || 1; // 0=X, 1=Y, 2=Z

    for (let i = 0; i < positions.count; i++) {
      const x = positions.getX(i);
      const y = positions.getY(i);
      const z = positions.getZ(i);

      const radius = 1 / bendAngle;

      if (bendAxis === 0) {
        // Bend around X axis (bend in YZ plane)
        const angle = x * bendAngle;
        const newX = radius * Math.sin(angle);
        const newY = y;
        const newZ = z + radius * (1 - Math.cos(angle));

        positions.setX(i, newX);
        positions.setY(i, newY);
        positions.setZ(i, newZ);
      } else if (bendAxis === 1) {
        // Bend around Y axis (bend in XZ plane)
        const angle = y * bendAngle;
        const newX = x;
        const newY = radius * Math.sin(angle);
        const newZ = z + radius * (1 - Math.cos(angle));

        positions.setX(i, newX);
        positions.setY(i, newY);
        positions.setZ(i, newZ);
      } else if (bendAxis === 2) {
        // Bend around Z axis (bend in XY plane)
        const angle = z * bendAngle;
        const newX = x + radius * (1 - Math.cos(angle));
        const newY = y;
        const newZ = radius * Math.sin(angle);

        positions.setX(i, newX);
        positions.setY(i, newY);
        positions.setZ(i, newZ);
      }
    }
  }

  // Array transformation
  private applyArrayTransformation(params: Record<string, number>) {
    const count = Math.floor(params.count || 4);
    const radius = params.radius || 2;

    if (count <= 1) return;

    // Get current transform values
    const currentPos = new Vector3();
    const currentScale = new Vector3();

    this.getPosition(currentPos);
    this.getScale(currentScale);

    // Create duplicates
    for (let i = 1; i < count; i++) {
      const angle = (i / count) * Math.PI * 2;
      const offsetX = Math.cos(angle) * radius;
      const offsetZ = Math.sin(angle) * radius;

      const duplicate = new PrimitiveEntity(null, null);
      duplicate.setPrimitiveType(this._primitiveType);
      duplicate.setColor(this._color);

      const newPos = new Vector3(
        currentPos.x + offsetX,
        currentPos.y,
        currentPos.z + offsetZ
      );
      duplicate.setPosition(newPos);
      duplicate.setScale(currentScale);

      Client.addEntityToWorld(duplicate);
    }
  }

  // Wave transformation
  private applyWaveTransformation(
    positions: BufferAttribute | InterleavedBufferAttribute,
    params: Record<string, number>
  ) {
    const amplitude = params.amplitude || 0.1;
    const frequency = params.frequency || 2;
    const direction = params.direction || 0;

    for (let i = 0; i < positions.count; i++) {
      const x = positions.getX(i);
      const y = positions.getY(i);
      const z = positions.getZ(i);

      if (direction === 0) {
        const wave = Math.sin(x * frequency) * amplitude;
        positions.setY(i, y + wave);
      } else if (direction === 1) {
        const wave = Math.sin(y * frequency) * amplitude;
        positions.setX(i, x + wave);
      } else {
        const wave = Math.sin(z * frequency) * amplitude;
        positions.setY(i, y + wave);
      }
    }
  }

  // Stretch transformation
  private applyStretchTransformation(
    positions: BufferAttribute | InterleavedBufferAttribute,
    params: Record<string, number>
  ) {
    const stretchX = params.stretchX || 1;
    const stretchY = params.stretchY || 1;
    const stretchZ = params.stretchZ || 1;

    for (let i = 0; i < positions.count; i++) {
      const x = positions.getX(i);
      const y = positions.getY(i);
      const z = positions.getZ(i);

      positions.setX(i, x * stretchX);
      positions.setY(i, y * stretchY);
      positions.setZ(i, z * stretchZ);
    }
  }

  // Shear transformation
  private applyShearTransformation(
    positions: BufferAttribute | InterleavedBufferAttribute,
    params: Record<string, number>
  ) {
    const shearX = params.shearX || 0;
    const shearY = params.shearY || 0;
    const shearZ = params.shearZ || 0;

    for (let i = 0; i < positions.count; i++) {
      const x = positions.getX(i);
      const y = positions.getY(i);
      const z = positions.getZ(i);

      positions.setX(i, x + y * shearX + z * shearZ);
      positions.setY(i, y + x * shearY);
    }
  }

  // Noise transformation
  private applyNoiseTransformation(
    positions: BufferAttribute | InterleavedBufferAttribute,
    params: Record<string, number>
  ) {
    const amplitude = params.amplitude || 0.1;
    const seed = params.seed || 0;

    const noise = (x: number, y: number, z: number) => {
      const n =
        Math.sin(x * 12.9898 + y * 78.233 + z * 37.719 + seed) * 43758.5453;
      return n - Math.floor(n);
    };

    for (let i = 0; i < positions.count; i++) {
      const x = positions.getX(i);
      const y = positions.getY(i);
      const z = positions.getZ(i);

      const noiseX = (noise(x, y, z) - 0.5) * amplitude;
      const noiseY = (noise(x + 1, y, z) - 0.5) * amplitude;
      const noiseZ = (noise(x, y + 1, z) - 0.5) * amplitude;

      positions.setX(i, x + noiseX);
      positions.setY(i, y + noiseY);
      positions.setZ(i, z + noiseZ);
    }
  }

  //#endregion
}

function updateBounds<T extends Mesh>(mesh: T): T {
  if ("computeBoundsTree" in mesh.geometry) {
    mesh.geometry.computeBoundsTree();
  }

  return mesh;
}

//#region geometry/mesh creation

function generateLines(entityGeometry: BufferGeometry) {
  // define edges of primitive entity
  const edges = new EdgesGeometry(entityGeometry);

  const lineSegments = new LineSegments(edges, dashedMaterial);
  lineSegments.computeLineDistances();
  return lineSegments;
}

function _primitiveObjectCreate(
  primitiveType: PrimitiveType,
  material: Material
) {
  const geometry = _primitiveGeometryCreate(primitiveType);
  if (!geometry) return;

  const mesh = new Mesh(geometry, material);
  return updateBounds(mesh);
}

function _primitiveGeometryCreate(primitiveType: PrimitiveType) {
  // we're using switch/case here, followed by assertUnreachable,
  // to ensure that we have covered all cases.
  switch (primitiveType) {
    case "Cube":
      return _boxGeometry(1);
    case "Sphere":
      return _sphereGeometry(16);
    case "Cylinder":
      return _cylinderGeometry(12);
    case "Cone":
      return _coneGeometry(12);
    case "Pipe":
      return _pipeGeometry({ radialSegments: 64 });
    case "Plane":
      return _planeGeometry(4);
    case "Torus":
      return _torusGeometry();
    case "TorusKnot":
      return _torusKnotGeometry();
    case "Ring":
      return _ringGeometry();
    case "Capsule":
      return _capsuleGeometry();
    case "Wedge":
      return _wedgeGeometry();
    case "Octahedron":
      return _octahedronGeometry();
    case "Tetrahedron":
      return _tetrahedronGeometry();
    case "None":
      return;
  }
  assertUnreachable(primitiveType);
}

function _boxGeometry(segments: number = 1) {
  return new BoxGeometry(0.95, 0.95, 0.95, segments, segments, segments);
}

function _sphereGeometry(segments: number = 32) {
  return new SphereGeometry(0.5, segments, segments / 2); // Radius, width segments, height segments
}

function _cylinderGeometry(segments: number = 32) {
  return new CylinderGeometry(0.5, 0.5, 1, segments); // Radius top, radius bottom, height, radial segments
}

function _coneGeometry(segments: number = 32) {
  return new ConeGeometry(0.5, 1, segments); // Radius, height, radial segments
  // return new ConeGeometry(0.5, 1, 12);
}

function _planeGeometry(segments: number = 4) {
  // Creating a BoxGeometry isntead of a PlaneGeometry, because of z-fighting issues
  return new BoxGeometry(4, 0.02, 4, segments, segments, segments);
}

function _pipeGeometry(cfg: {
  radius?: number; // Outer radius of the pipe
  height?: number; // Height of the pipe
  radialSegments?: number; // Number of segmented faces around the circumference
  thickness?: number; // Wall thickness of the pipe
  thetaStart?: number; // Start angle for first segment (in radians)
  thetaLength?: number; // The central angle (in radians)
}) {
  const {
    radius = 0.5,
    height = 1,
    radialSegments = 12,
    thickness = 0.1,
    thetaStart = 0,
    thetaLength = Math.PI * 2,
  } = cfg;

  const outerRadius = radius;
  const innerRadius = radius - thickness;

  // Create the outer shape (ring)
  const shape = new Shape();
  shape.absarc(0, 0, outerRadius, thetaStart, thetaStart + thetaLength, false);

  // Create the hole (inner ring)
  const holePath = new Path();
  holePath.absarc(
    0,
    0,
    innerRadius,
    thetaStart + thetaLength,
    thetaStart,
    true
  );
  shape.holes.push(holePath);

  // Extrusion settings
  const extrudeSettings = {
    depth: height,
    bevelEnabled: false,
    steps: 1,
    curveSegments: radialSegments / 2,
  };

  // Create geometry by extruding the shape
  const geometry = new ExtrudeGeometry(
    shape,
    extrudeSettings
  ) as BufferGeometry;

  geometry.translate(0, 0, -height * 0.5);
  geometry.name = "pipe";
  return geometry;
}

function _torusGeometry() {
  return new TorusGeometry(0.4, 0.15, 12, 32);
}

function _torusKnotGeometry() {
  return new TorusKnotGeometry(0.4, 0.1, 64, 8);
}

function _ringGeometry() {
  // Create ring shape with hole
  const shape = new Shape();
  shape.absarc(0, 0, 0.5, 0, Math.PI * 2, false);

  // Create hole
  const hole = new Path();
  hole.absarc(0, 0, 0.2, 0, Math.PI * 2, true);
  shape.holes.push(hole);

  // Extrude to give thickness
  const extrudeSettings = {
    depth: 0.05,
    bevelEnabled: false,
    steps: 1,
    curveSegments: 16,
  };

  const geometry = new ExtrudeGeometry(shape, extrudeSettings);

  // Center the ring
  geometry.translate(0, 0, -0.025);

  return geometry;
}

function _capsuleGeometry() {
  return new CapsuleGeometry(0.25, 0.5, 4, 8);
}

function _wedgeGeometry() {
  const shape = new Shape();
  shape.moveTo(0, 0);
  shape.lineTo(1, 0);
  shape.lineTo(1, 1);
  shape.lineTo(0, 0);
  return new ExtrudeGeometry(shape, { depth: 1, bevelEnabled: false });
}

function _octahedronGeometry() {
  return new OctahedronGeometry(0.5);
}

function _tetrahedronGeometry() {
  return new TetrahedronGeometry(0.5);
}
//#endregion
