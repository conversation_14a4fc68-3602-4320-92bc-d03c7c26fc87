import Jolt from "jolt-physics";
import { QuaternionLike, Vector3<PERSON>ike } from "three";

export function joltRVector3ToThreeVector3(vector: Jolt.RVec3): Vector3Like {
  return {
    x: vector.GetX(),
    y: vector.GetY(),
    z: vector.GetZ(),
  };
}

export function joltVector3ToThreeVector3(vector: Jolt.Vec3): Vector3Like {
  return {
    x: vector.GetX(),
    y: vector.GetY(),
    z: vector.GetZ(),
  };
}

export function joltQuaternionToThreeQuaternion(
  quaternion: Jolt.Quat
): QuaternionLike {
  return {
    x: quaternion.GetX(),
    y: quaternion.GetY(),
    z: quaternion.GetZ(),
    w: quaternion.GetW(),
  };
}
