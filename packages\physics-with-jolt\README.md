# Physics with Jolt - Architecture Documentation

## Overview

The `@nilo/physics-with-jolt` package provides a comprehensive physics simulation system built on top of Jolt Physics, featuring a webworker-based implementation. This package enables 3D physics simulation with support for rigid bodies, character controllers and ragdolls

## Key Features

- **Physics Loop**: Dedicated physics simulation independent of render loop
- **Jolt Physics Integration**: WASM-based physics engine
- **Character Controllers**
- **Ragdoll Physics**
- **TypeScript Support**

### High-Level Architecture

![Physics with Jolt Architecture](images/physics-with-jolt.drawio.png)

## Core Components

### 1. Main Thread Components

#### JoltPhysicsServiceWithWebworker

The main interface for physics operations in the main thread. Acts as a proxy to the JoltPhysicsService.

**Key Responsibilities:**

- Handles physics loop control
- Manages body control, ragdoll control and character control wrappers
- Provides event delegation for contacts and physics state
- Coordinates communication with webworker: Retrive physics state, and create bodies

**Key Methods:**

```typescript
startPhysicsLoop(): void
stopPhysicsLoop(): void
registerObjectForPhysics(physicsBody: PhysicsBody, geometry: BodyGeometry): Promise<BodyControlWrapper>
unregisterObjectFromPhysics(id: string): void
registerRagdollForPhysics(ragdollSettings: RagdollSettings): Promise<RagdollControlWrapper>
unregisterRagdollFromPhysics(ragdollControl: RagdollControlWrapper): void
createCharacterPhysics(characterSettings: Partial<CharacterPhysicsSettings>): Promise<CharacterControlWrapper>
unregisterCharacterFromPhysics(characterControl: CharacterControlWrapper): void
getLastStepInfo(): Promise<PhysicsStepInfo>
```

#### Wrapper Classes

Wrapper classes provide a clean interface to manipulate physics objects from the webworker:

- **BodyControlWrapper**: Wraps rigid body physics controls
- **CharacterControlWrapper**: Wraps character controller physics
- **RagdollControlWrapper**: Wraps ragdoll physics systems

**Benefits:**

- Caches frequently accessed properties locally and provides synchronous access to them
- Reduces webworker communication overhead
- Maintains type safety across thread boundaries

##### Dynamic Properties System

Wrappers support dynamic properties. This properties can be set to active for each body so that every physics update retrieves it, alongside all the necessary data.
For example, in BodyControl (rigid bodies), position and orientation are always retrieved during physicsSync since they are commonly accessed every frame. However, properties like linearVelocity are not needed every frame for every body, so serializing them all would be wasteful. If the application needs linearVelocity occasionally, retrieving it in separate messages outside of syncPhysics can create overhead if done every frame and can be out of sync with the current state in the Main Thread. The solution is to mark linearVelocity as a dynamic property - it will then be efficiently included in the syncPhysics call (in PhysicsStepInfo) only when needed, and always in sync.

Any wrapper has this methods available:

```typescript
export type DynamicPropertyKey =
  | "linearVelocity"
  | "angularVelocity"
  | "contactCount";

export type DynamicProperties = {
  [key in DynamicPropertyKey]?: DynamicProperty;
};
```

```typescript
  //To mark propeties as active, otherwise they won't get cached in the wrapper
  addActiveDynamicProperties(dynamicProperties: DynamicPropertyKey[]) : void
  removeActiveDynamicProperties(dynamicProperties: DynamicPropertyKey[]) : void

  updateDynamicProperties(dynamicProperties: DynamicProperties) : void
  getVector3DynamicProperty(dynamicProperty: DynamicPropertyKey): Vector3Like
  getQuaternionDynamicProperty(dynamicProperty: DynamicPropertyKey): QuaternionLike
  getNumberDynamicProperty(dynamicProperty: DynamicPropertyKey): number
  getDynamicProperty(dynamicProperty: DynamicPropertyKey): DynamicProperty
```

This properties are then updated every frame. They are retrieved through the PhysicsStep.
To add more properties to the Dynamic Properties, it's needed to add them to the type _DynamicPropertyKey_ and do the appropriate change in the JoltPhysicsService.updatePhysics

**Benefits:**

- Only transfers necessary data each frame
- Reduces webworker communication overhead
- Enables synchronous property access in main thread

### 2. Web Worker Components

#### JoltWebworker

The core webworker implementation that runs the physics simulation.

#### PhysicsStepController

Manages the physics simulation timing with a fixed FPS loop.

#### JoltPhysicsService

The core physics service that interfaces directly with Jolt Physics WASM. Creates Jolt instances and Controls to manipulate them.

**Key Components:**

- Physics world initialization
- Body management and lifecycle
- Collision detection and response
- Memory management and optimization

#### Jolt Physics WASM

The package integrates with Jolt Physics through WebAssembly, providing:

- **High Performance**: Native C++ performance in the browser
- **Advanced Features**: Complex collision shapes, constraints, and materials
- **Memory Efficiency**: Optimized memory management
- **Cross-Platform**: Consistent behavior across different browsers

## Data Flow

### 1. Physics Step Flow

```mermaid
sequenceDiagram
    participant PSC as PhysicsStepController
    participant JPS as JoltPhysicsService
    participant WASM as Jolt Physics WASM
    participant WB as JoltWebworker
    participant MT as Main Thread

    loop Every 16.67ms ( for 60 FPS)
        PSC->>JPS: physicsStep(deltaTime, steps)
        JPS->>WASM: Step Simulation
        WASM->>JPS: Collect Body Updates
        MT-)WB: getLastStepInfo()
        WB->>JPS: Get PhysicsStepInfo
        JPS-->>WB: Get PhysicsStepInfo
        WB--)MT: getLastStepInfo()
        MT->>MT: Update Wrapper States
    end
```

### 2. Body Registration Flow

```mermaid
sequenceDiagram
    participant MT as Main Thread
    participant WW as Web Worker
    participant BCF as BodyControlFactory
    participant WASM as Jolt Physics WASM

    MT-)WW: registerObjectForPhysics(PhysicsBody)
    WW->>BCF: create(body, geometry, options)
    BCF->>WASM: Create Physics Body
    WASM-->>BCF: Body Created
    BCF-->>WW: BodyControl Instance
    WW->>WW: Create Comlink Proxy
    WW--)MT: BodyControl Proxy
    MT->>MT: Create BodyControlWrapper
```

### Basic Physics Setup

```typescript
import { createJoltPhysicsServiceWithWebworker } from "@nilo/physics-with-jolt";

// Initialize physics service
const physicsService = await createJoltPhysicsServiceWithWebworker();

// Start physics simulation
physicsService.startPhysicsLoop();

// Register a physics body
const bodyControl = await physicsService.registerObjectForPhysics(
  physicsBody,
  geometry
);

// Get physics updates
const stepInfo = await physicsService.getLastStepInfo();
console.log("Updated bodies:", stepInfo.bodies);
```

### Character Controller

```typescript
// Create character physics
const characterControl = await physicsService.createCharacterPhysics({
  position: { x: 0, y: 0, z: 0 },
  radius: 0.5,
  height: 1.8,
});

// Control character movement
characterControl.setLinearVelocity({ x: 5, y: 0, z: 0 });
```

### Ragdoll Physics

```typescript
// Create ragdoll
const ragdollControl = await physicsService.registerRagdollForPhysics({
  bones: boneData,
  constraints: constraintData,
});

// Control ragdoll state
ragdollControl.setDynamic(); // Enable physics
ragdollControl.setKinematic(); // Disable physics
```
