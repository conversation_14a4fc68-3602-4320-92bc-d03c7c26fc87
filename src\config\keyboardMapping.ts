////
//// # Keyboard shortcuts configuration
////
//// Format: action_name: "modifier+key" (e.g., "Ctrl+KeyQ", "Ctrl+Shift+KeyQ")
//// Multiple shortcuts for the same action: "Ctrl+KeyY, Meta+Shift+KeyZ"
////
//// Note 1: Modifiers and keys are case-insensitive
//// Note 2: Make sure to only use one non-modifier key (e.g. no "Ctrl+A+B")
//// Note 3: Use comma to separate multiple shortcuts for the same action
////
//// ## CHEAT SHEET
////
//// Modifiers: Ctrl, Shift, Alt, Meta (case-insensitive)
//// Keys: https://keycode.info (read "event.code")
////

export const keyboardMappingDict = {
  // #### Panels
  open_environment_settings: "Ctrl + KeyQ",
  open_dev_settings: "Ctrl + Backquote",
  open_legacy_world_settings: "Ctrl + Shift + KeyQ",

  // #### Camera and view actions
  save_camera_settings: "Ctrl + KeyK",
  load_camera_settings: "Ctrl + KeyL",
  toggle_entity_lock: "KeyL",

  // #### Undo/Redo actions
  scene_undo: "Ctrl + KeyZ",
  scene_redo: "Ctrl + KeyY, Ctrl + Shift + KeyZ, Meta + Shift + KeyZ",

  // #### Room actions
  save_room_preview: "Ctrl + KeyP",
  toggle_physics: "KeyP",

  // #### Entity actions
  delete_selected_entities: "Delete, Backspace",
  commit_current_action: "Enter",
  duplicate_entities: "Ctrl + KeyD",
  focus_entities: "KeyF",

  // #### Gizmo and view actions
  toggle_particle_gizmos: "Ctrl + KeyI",
  reset_camera: "KeyO",
  toggle_transform_space: "KeyG",
  cycle_selection: "Tab",
  toggle_radial_menu: "Space",

  // #### Quick rotations
  rotate_clockwise: "KeyR",
  rotate_counterclockwise: "Shift + KeyR",

  // #### Debug and dev actions
  toggle_network_stats: "Ctrl + KeyM",
  send_emoji_broadcast: "Ctrl + BracketLeft",
  toggle_feature_flags_debug: "Ctrl + Shift + KeyF",
  set_preferred_deployment: "Ctrl + Shift + Backquote",

  // #### AI and prompt actions
  open_ai_prompt_popover: "Ctrl + KeyB",

  // #### Global
  global_dismiss: "Escape",
  open_keyboard_shortcuts: "Ctrl + Shift + Alt + KeyK",
} as const;

//// //// //// //// //// //// //// //// //// //// //// //// //// //// ////
//// //// //// //// //// //// //// //// //// //// //// //// //// //// ////
//// //// //// //// //// //// //// //// //// //// //// //// //// //// ////
//// You don't need to edit anything below to customize.
//// //// //// //// //// //// //// //// //// //// //// //// //// //// ////
//// //// //// //// //// //// //// //// //// //// //// //// //// //// ////
//// //// //// //// //// //// //// //// //// //// //// //// //// //// ////

export type KeyboardMappingID = keyof typeof keyboardMappingDict;

export const KeyboardMappingIDs = Object.fromEntries(
  Object.keys(keyboardMappingDict).map((key) => [key.toUpperCase(), key])
) as { readonly [K in KeyboardMappingID as Uppercase<K>]: K };
