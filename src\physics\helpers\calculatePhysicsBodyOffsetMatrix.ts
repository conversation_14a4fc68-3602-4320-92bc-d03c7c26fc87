import { Matrix4, Vector3 } from "three";
import { MeshEntity, PrimitiveEntity } from "@/core/entity";
import { getOriginOffset } from "@/core/entity/common/getEntityOriginOffset";

export function calculatePhysicsBodyOffsetMatrix(
  entity: MeshEntity | PrimitiveEntity
): Matrix4 {
  //Get offsetMatrix
  const mesh = entity.getMesh();
  if (!mesh) {
    throw new Error(`Mesh not found for entity: ${entity.id}`);
  }
  const offsetMatrix = new Matrix4();
  const originOffset = getOriginOffset(mesh, entity.getOriginType());

  const quaternion = mesh.quaternion;
  offsetMatrix.compose(
    new Vector3(originOffset.x, originOffset.y, originOffset.z),
    quaternion,
    new Vector3(1, 1, 1)
  );
  return offsetMatrix;
}
