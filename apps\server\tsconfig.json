{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ES2020",
    "lib": ["ES2020", "DOM"],
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "node10",
    "emitDeclarationOnly": false,

    "allowSyntheticDefaultImports": true,

    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,

    "importHelpers": true,

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,
    "forceConsistentCasingInFileNames": true,

    "types": ["node"]
  },
  "include": ["src"]
}
