import React from "react";

type Props = {
  children: React.ReactNode;
  separator: React.ReactNode;
};

export function Join({ children, separator }: Props): React.JSX.Element | null {
  const items = React.Children.toArray(children);
  if (items.length === 0) return null;
  if (items.length === 1) return <>{items[0]}</>;

  return (
    <>
      {items.map((child, i) =>
        i === 0 ? (
          <React.Fragment key={i}>
            {child}
            {/* Add the separator to the last item */}
          </React.Fragment>
        ) : (
          <React.Fragment key={i}>
            {separator}
            {child}
          </React.Fragment>
        )
      )}
    </>
  );
}
