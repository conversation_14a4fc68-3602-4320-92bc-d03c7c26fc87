// TODO: We need to figure out how to properly load API keys from environment variables
// in Firebase Functions instead of hard-coding fallback values. This is a security risk and
// needs to be addressed before production deployment.
//
// <PERSON> says: "Consider using Firebase Functions config or Google Secret Manager for secure key management"

export const secretKeys = {
  googleApiKey:
    process.env.GOOGLE_API_KEY || "AIzaSyAzaU79-JDw_AGL0OgjrmcMvxNtxtxg40c",
  openaiApiKey:
    process.env.OPENAI_API_KEY ||
    "******************************************************************************************************************************************************************",
  anthropicApiKey:
    process.env.ANTHROPIC_API_KEY ||
    "************************************************************************************************************",
  groqApiKey:
    process.env.GROQ_API_KEY ||
    "********************************************************",
  cerebrasApiKey:
    process.env.CEREBRAS_API_KEY || "PLACEHOLDER_CEREBRAS_API_KEY", // TODO: Add actual API key
  togetheraiApiKey:
    process.env.TOGETHERAI_API_KEY || "PLACEHOLDER_TOGETHERAI_API_KEY", // TODO: Add actual API key
};
