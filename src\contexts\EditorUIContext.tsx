import {
  create<PERSON>ontext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { urlParams } from "@/utils/urlParams";
import { KeyboardMappingIDs } from "@/config/keyboardMapping";
import { Client } from "@/core/client";

/**
 * EditorUIContext provides access to UI panel states and controls throughout the component tree.
 *
 * Usage:
 *
 * 1. Wrap your component tree with EditorUIProvider:
 *    ```tsx
 *    <EditorUIProvider>
 *      <YourComponents />
 *    </EditorUIProvider>
 *    ```
 *
 * 2. Access UI state in any child component:
 *    ```tsx
 *    const { isEnvironmentSettingsOpen, isDevSettingsOpen, isLegacyWorldSettingsOpen } = useEditorUIState();
 *    ```
 *
 * 3. Access UI actions in any child component:
 *    ```tsx
 *    const { toggleEnvironmentSettingsPanel, toggleKeyboardShortcuts, toggleFeedback } = useEditorUIActions();
 *    // Toggle: toggleEnvironmentSettingsPanel()
 *    // Open: toggleEnvironmentSettingsPanel(true)
 *    // Close: toggleEnvironmentSettingsPanel(false)
 *    ```
 */

export enum LeftPanelType {
  EnvironmentSettings = "env",
  DevSettings = "dev",
  LegacyWorldSettings = "legacy",
  SceneHierarchy = "scene",
  Entities = "entities",
}

//// Only useful for debugging
const DEFAULT_LEFT_PANEL = urlParams.openPanel as LeftPanelType | null;

interface EditorUIState {
  currentLeftPanel: LeftPanelType | null;
  isEnvironmentSettingsOpen: boolean;
  isDevSettingsOpen: boolean;
  isLegacyWorldSettingsOpen: boolean;
  isSceneHierarchyOpen: boolean;
  isEntitiesOpen: boolean;
  isInspectorOpen: boolean;
  isInspectorHidden: boolean;
  isKeyboardShortcutsOpen: boolean;
  isFeedbackOpen: boolean;
  isLeftPanelPinned: boolean;
}

interface EditorUIActions {
  toggleEnvironmentSettingsPanel: (open?: boolean) => void;
  toggleDevSettingsPanel: (open?: boolean) => void;
  toggleLegacyWorldSettingsPanel: (open?: boolean) => void;
  toggleSceneHierarchyPanel: (open?: boolean) => void;
  toggleEntitiesPanel: (open?: boolean) => void;
  toggleInspectorPanel: (open?: boolean) => void;
  toggleInspectorHidden: (hidden?: boolean) => void;
  setCurrentLeftPanel: (panel: LeftPanelType | null) => void;
  toggleLeftPanel: (panel: LeftPanelType, open?: boolean) => void;
  toggleLeftPanelPinned: (pinned?: boolean) => void;
  toggleKeyboardShortcuts: (open?: boolean) => void;
  toggleFeedback: (open?: boolean) => void;
}

interface EditorUIContextValue {
  state: EditorUIState;
  actions: EditorUIActions;
}

const EditorUIContext = createContext<EditorUIContextValue | null>(null);

export function EditorUIProvider({ children }: { children: ReactNode }) {
  const { currentLeftPanel, setCurrentLeftPanel, toggleCurrentLeftPanel } =
    useCurrentPanelState();
  const [isKeyboardShortcutsOpen, setIsKeyboardShortcutsOpen] = useState(false);
  const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);
  const [isInspectorOpen, setIsInspectorOpen] = useState(false);
  const [isInspectorHidden, setIsInspectorHidden] = useState(false);
  const [isLeftPanelPinned, setIsLeftPanelPinned] = useState(false);

  const state: EditorUIState = useMemo(
    () => ({
      currentLeftPanel,
      isEnvironmentSettingsOpen:
        currentLeftPanel === LeftPanelType.EnvironmentSettings,
      isDevSettingsOpen: currentLeftPanel === LeftPanelType.DevSettings,
      isLegacyWorldSettingsOpen:
        currentLeftPanel === LeftPanelType.LegacyWorldSettings,
      isSceneHierarchyOpen: currentLeftPanel === LeftPanelType.SceneHierarchy,
      isEntitiesOpen: currentLeftPanel === LeftPanelType.Entities,
      isInspectorOpen,
      isInspectorHidden,
      isKeyboardShortcutsOpen,
      isFeedbackOpen,
      isLeftPanelPinned,
    }),
    [
      currentLeftPanel,
      isInspectorOpen,
      isInspectorHidden,
      isKeyboardShortcutsOpen,
      isFeedbackOpen,
      isLeftPanelPinned,
    ]
  );

  const toggleLeftPanel = useCallback(
    (panel: LeftPanelType, open?: boolean) => {
      const isCurrentlyOpen = currentLeftPanel === panel;

      // If open parameter is provided and matches current state, do nothing
      if (open !== undefined && open === isCurrentlyOpen) {
        return;
      }

      if (open === true) {
        setCurrentLeftPanel(panel);
      } else if (open === false) {
        setCurrentLeftPanel(null);
      } else {
        // No parameter provided, toggle
        toggleCurrentLeftPanel(panel);
      }
    },
    [currentLeftPanel, setCurrentLeftPanel, toggleCurrentLeftPanel]
  );

  const toggleEnvironmentSettingsPanel = useCallback(
    (open?: boolean) => {
      toggleLeftPanel(LeftPanelType.EnvironmentSettings, open);
    },
    [toggleLeftPanel]
  );

  const toggleDevSettingsPanel = useCallback(
    (open?: boolean) => {
      toggleLeftPanel(LeftPanelType.DevSettings, open);
    },
    [toggleLeftPanel]
  );

  const toggleLegacyWorldSettingsPanel = useCallback(
    (open?: boolean) => {
      toggleLeftPanel(LeftPanelType.LegacyWorldSettings, open);
    },
    [toggleLeftPanel]
  );

  const toggleSceneHierarchyPanel = useCallback(
    (open?: boolean) => {
      toggleLeftPanel(LeftPanelType.SceneHierarchy, open);
    },
    [toggleLeftPanel]
  );

  const toggleEntitiesPanel = useCallback(
    (open?: boolean) => {
      toggleLeftPanel(LeftPanelType.Entities, open);
    },
    [toggleLeftPanel]
  );

  const toggleInspectorPanel = useCallback(
    (open?: boolean) => {
      // If open parameter is provided and matches current state, do nothing
      if (open !== undefined && open === isInspectorOpen) {
        return;
      }

      if (open === true) {
        setIsInspectorOpen(true);
      } else if (open === false) {
        setIsInspectorOpen(false);
      } else {
        // No parameter provided, toggle
        setIsInspectorOpen((prev) => !prev);
      }
    },
    [isInspectorOpen]
  );

  const toggleInspectorHidden = useCallback(
    (hidden?: boolean) => {
      // If hidden parameter is provided and matches current state, do nothing
      if (hidden !== undefined && hidden === isInspectorHidden) {
        return;
      }

      if (hidden === true) {
        setIsInspectorHidden(true);
      } else if (hidden === false) {
        setIsInspectorHidden(false);
      } else {
        // No parameter provided, toggle
        setIsInspectorHidden((prev) => !prev);
      }
    },
    [isInspectorHidden]
  );

  const toggleKeyboardShortcuts = useCallback(
    (open?: boolean) => {
      // If open parameter is provided and matches current state, do nothing
      if (open !== undefined && open === isKeyboardShortcutsOpen) {
        return;
      }

      if (open === true) {
        setIsKeyboardShortcutsOpen(true);
      } else if (open === false) {
        setIsKeyboardShortcutsOpen(false);
      } else {
        // No parameter provided, toggle
        setIsKeyboardShortcutsOpen((prev) => !prev);
      }
    },
    [isKeyboardShortcutsOpen]
  );

  const toggleFeedback = useCallback(
    (open?: boolean) => {
      // If open parameter is provided and matches current state, do nothing
      if (open !== undefined && open === isFeedbackOpen) {
        return;
      }

      if (open === true) {
        setIsFeedbackOpen(true);
      } else if (open === false) {
        setIsFeedbackOpen(false);
      } else {
        // No parameter provided, toggle
        setIsFeedbackOpen((prev) => !prev);
      }
    },
    [isFeedbackOpen]
  );

  const toggleLeftPanelPinned = useCallback(
    (pinned?: boolean) => {
      // If pinned parameter is provided and matches current state, do nothing
      if (pinned !== undefined && pinned === isLeftPanelPinned) {
        return;
      }

      if (pinned === true) {
        setIsLeftPanelPinned(true);
      } else if (pinned === false) {
        setIsLeftPanelPinned(false);
      } else {
        // No parameter provided, toggle
        setIsLeftPanelPinned((prev) => !prev);
      }
    },
    [isLeftPanelPinned]
  );

  // Reset pin flag when panel changes
  useEffect(() => {
    setIsLeftPanelPinned(false);
  }, [currentLeftPanel]);

  // Keyboard shortcuts
  useKeyboardShortcuts({
    toggleEnvironmentSettingsPanel,
    toggleDevSettingsPanel,
    toggleLegacyWorldSettingsPanel,
    toggleSceneHierarchyPanel,
    toggleKeyboardShortcuts,
    closeLeftPanel: () => setCurrentLeftPanel(null),
  });

  const actions: EditorUIActions = useMemo(
    () => ({
      toggleEnvironmentSettingsPanel,
      toggleDevSettingsPanel,
      toggleLegacyWorldSettingsPanel,
      toggleSceneHierarchyPanel,
      toggleEntitiesPanel,
      toggleInspectorPanel,
      toggleInspectorHidden,
      setCurrentLeftPanel: setCurrentLeftPanel,
      toggleLeftPanel,
      toggleLeftPanelPinned,
      toggleKeyboardShortcuts,
      toggleFeedback,
    }),
    [
      toggleEnvironmentSettingsPanel,
      toggleDevSettingsPanel,
      toggleLegacyWorldSettingsPanel,
      toggleSceneHierarchyPanel,
      toggleEntitiesPanel,
      toggleInspectorPanel,
      toggleInspectorHidden,
      setCurrentLeftPanel,
      toggleLeftPanel,
      toggleLeftPanelPinned,
      toggleKeyboardShortcuts,
      toggleFeedback,
    ]
  );

  const value: EditorUIContextValue = useMemo(
    () => ({ state, actions }),
    [state, actions]
  );

  return (
    <EditorUIContext.Provider value={value}>
      {children}
    </EditorUIContext.Provider>
  );
}

function useCurrentPanelState() {
  const [currentLeftPanel, setCurrentLeftPanel] =
    useState<LeftPanelType | null>(DEFAULT_LEFT_PANEL);

  return {
    currentLeftPanel,

    setCurrentLeftPanel: useCallback((key: LeftPanelType | null) => {
      setCurrentLeftPanel(key);
    }, []),

    toggleCurrentLeftPanel: useCallback((key: LeftPanelType) => {
      setCurrentLeftPanel((prev) => (prev === key ? null : key));
    }, []),
  };
}

function useKeyboardShortcuts({
  toggleEnvironmentSettingsPanel,
  toggleDevSettingsPanel,
  toggleLegacyWorldSettingsPanel,
  toggleSceneHierarchyPanel,
  closeLeftPanel,
  toggleKeyboardShortcuts,
}: {
  toggleEnvironmentSettingsPanel: () => void;
  toggleDevSettingsPanel: () => void;
  toggleLegacyWorldSettingsPanel: () => void;
  toggleSceneHierarchyPanel: () => void;
  closeLeftPanel: () => void;
  toggleKeyboardShortcuts: () => void;
}) {
  useEffect(() => {
    // Register keyboard shortcuts declaratively
    const cleanupFunctions = [
      Client.keyboard.registerAction(
        KeyboardMappingIDs.GLOBAL_DISMISS,
        closeLeftPanel
      ),
      Client.keyboard.registerAction(
        KeyboardMappingIDs.OPEN_ENVIRONMENT_SETTINGS,
        toggleEnvironmentSettingsPanel
      ),
      Client.keyboard.registerAction(
        KeyboardMappingIDs.OPEN_DEV_SETTINGS,
        toggleDevSettingsPanel
      ),
      Client.keyboard.registerAction(
        KeyboardMappingIDs.OPEN_LEGACY_WORLD_SETTINGS,
        toggleLegacyWorldSettingsPanel
      ),
      Client.keyboard.registerAction(
        KeyboardMappingIDs.OPEN_KEYBOARD_SHORTCUTS,
        toggleKeyboardShortcuts
      ),
    ];

    // Cleanup all registrations when component unmounts
    return () => {
      cleanupFunctions.forEach((cleanup) => cleanup());
    };
  }, [
    toggleEnvironmentSettingsPanel,
    toggleDevSettingsPanel,
    toggleLegacyWorldSettingsPanel,
    toggleSceneHierarchyPanel,
    closeLeftPanel,
    toggleKeyboardShortcuts,
  ]);
}

export function useEditorUIState(): EditorUIState {
  const context = useContext(EditorUIContext);
  if (!context) {
    throw new Error("useEditorUIState must be used within a EditorUIProvider");
  }
  return context.state;
}

export function useEditorUIActions(): EditorUIActions {
  const context = useContext(EditorUIContext);
  if (!context) {
    throw new Error(
      "useEditorUIActions must be used within a EditorUIProvider"
    );
  }
  return context.actions;
}
