import Emittery from "emittery";
import { Subject } from "rxjs";
import { logger } from "@sentry/react";
import {
  Channel,
  Channels,
  ChannelType,
  CleanupCallback,
  Client,
  ClientEvents,
  ClientOptions,
  ConnectionCloseInfo,
  MessageCallback,
  ProviderType,
} from "../types";
import { ChannelStats, NetworkStats } from "../core/stats";

const WSHeaderSize = 2;
const WSMaskSize = 4;
const WSTypicalTCPOverhead = 20; // Typical TCP header size
const WSMinPacketSize = WSHeaderSize + WSMaskSize + WSTypicalTCPOverhead;

/**
 * Calculate the size of the WebSocket packet that will be sent over the network
 *
 * Based on:
 * https://datatracker.ietf.org/doc/html/rfc6455#section-5.2
 * https://datatracker.ietf.org/doc/html/rfc6455#section-5.3
 *
 * @param byteLength - The length of the byte array to send
 * @returns The size of the packet that will be sent over the WebSocket including the WebSocket headers
 */
const getWSPacketSize = (byteLength: number): number => {
  if (byteLength < 126) {
    return byteLength + WSMinPacketSize;
  }

  if (byteLength < 65536) {
    return byteLength + WSMinPacketSize + 2;
  }

  return byteLength + WSMinPacketSize + 8;
};

class WebSocketDefaultChannel implements Channel {
  private _socket: WebSocket;
  private _subject: Subject<Uint8Array>;

  private _stats: ChannelStats;

  constructor(
    socket: WebSocket,
    subject: Subject<Uint8Array>,
    stats: ChannelStats
  ) {
    this._stats = stats;
    this._socket = socket;
    this._subject = subject;
  }

  public async send(data: Uint8Array): Promise<void> {
    this._socket.send(data);
    this._stats._onSend(getWSPacketSize(data.byteLength), data.byteLength);
  }

  public onReceive(cb: MessageCallback): CleanupCallback {
    const sub = this._subject.subscribe(cb);

    return () => {
      sub.unsubscribe();
    };
  }

  public get type(): ChannelType {
    return ChannelType.RELIABLE_ORDERED;
  }

  public get stats(): ChannelStats {
    return this._stats;
  }
}

class WebSocketUnreliableUnorderedChannel extends WebSocketDefaultChannel {
  // constructor(socket: WebSocket, subject: Subject<Uint8Array>) {
  //   super(socket, subject, ChannelType.UNRELIABLE_UNORDERED);
  // }

  public override onReceive(cb: MessageCallback): CleanupCallback {
    logger.warn(
      "WebSocket does not support unreliable unordered channel, using reliable ordered instead"
    );
    return super.onReceive(cb);
  }

  public override get type(): ChannelType {
    return ChannelType.UNRELIABLE_UNORDERED;
  }
}

interface WSClientOptions {
  secure?: boolean;
}

export class WebSocketClient extends Emittery<ClientEvents> implements Client {
  private _ws: WebSocket;

  private _ready: Promise<void>;
  private _closed: Promise<ConnectionCloseInfo>;

  private _channels: Channels;

  private _stats: NetworkStats;

  // public readonly channels: Readonly<Channels>;
  // public readonly stats: ClientStats;

  static isSupported(): boolean {
    return typeof WebSocket !== "undefined";
  }

  constructor(options: ClientOptions & WSClientOptions) {
    super();

    const protocol = options.secure ? "wss" : "ws";
    const url = `${protocol}://${options.host + (options.port ? `:${options.port}` : "")}${options.path ?? ""}`;

    this._ws = new WebSocket(url);
    this._ws.binaryType = "arraybuffer";

    const subject = new Subject<Uint8Array>();

    this._stats = new NetworkStats();

    const reliableOrderedStats = new ChannelStats(
      ChannelType.RELIABLE_ORDERED,
      this._stats
    );

    const unreliableUnorderedStats = new ChannelStats(
      ChannelType.UNRELIABLE_UNORDERED,
      this._stats
    );

    const onMessage = (event: MessageEvent<ArrayBuffer>) => {
      const data = new Uint8Array(event.data);
      reliableOrderedStats._onReceive(
        getWSPacketSize(data.byteLength),
        data.byteLength
      );
      subject.next(data);
    };

    this._channels = {
      reliableOrdered: new WebSocketDefaultChannel(
        this._ws,
        subject,
        reliableOrderedStats
      ),
      unreliableUnordered: new WebSocketUnreliableUnorderedChannel(
        this._ws,
        subject,
        unreliableUnorderedStats
      ),
    };

    let rafId: number = -1;
    const onFrame = () => {
      this.updateStats();
      rafId = requestAnimationFrame(onFrame);
    };

    this._ready = new Promise<void>((resolve) => {
      this._ws.addEventListener(
        "open",
        () => {
          this._ws.addEventListener("message", onMessage);
          rafId = requestAnimationFrame(onFrame);

          resolve();
          this.emit("ready");
        },
        { once: true }
      );
    });

    this._closed = new Promise<ConnectionCloseInfo>((resolve) => {
      this._ws.addEventListener(
        "close",
        (event) => {
          this._ws.removeEventListener("message", onMessage);
          cancelAnimationFrame(rafId);

          resolve({
            closeCode: event.code,
            reason: event.reason,
          });
        },
        { once: true }
      );

      this._ws.addEventListener("error", (event) => {
        logger.error("WebSocket error", {
          type: event.type,
          target: event.target?.constructor?.name || "unknown",
        });

        resolve({
          closeCode: 1006,
          reason: "",
        });
      });
    });

    this._closed.then((info) => {
      this.emit("close", info);
    });
  }

  private _lastTimestamp = 0;
  private updateStats() {
    const now = performance.now();
    const delta = now - this._lastTimestamp;

    if (delta > NetworkStats.STATS_INTERVAL) {
      this._lastTimestamp = now;

      this.channels.reliableOrdered.stats._onCycle(delta);
      this.channels.unreliableUnordered.stats._onCycle(delta);
      this._stats._onCycle(delta);

      this._lastTimestamp = now;
    }
  }

  // public get stats(): ClientStats {
  //   return this._stats;
  // }

  public get MAX_DATAGRAM_SIZE(): number {
    return Infinity;
  }

  public get ready(): Promise<void> {
    return this._ready;
  }

  public get closed(): Promise<ConnectionCloseInfo> {
    return this._closed;
  }

  public get provider(): ProviderType {
    return ProviderType.WebSocket;
  }

  public get channels(): Readonly<Channels> {
    return this._channels;
  }

  public get stats(): NetworkStats {
    return this._stats;
  }

  close(info?: ConnectionCloseInfo): void {
    this._ws.close(info?.closeCode, info?.reason);
  }

  public get socket(): WebSocket {
    return this._ws;
  }
}
