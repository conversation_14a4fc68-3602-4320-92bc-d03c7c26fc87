# This is run for:
# - pull requests
# - branches that don't rely on a PR to have their environment ("main", "prod")
# It builds the server container image and deploys the fleet if the server has changed

name: Deploy Server

on:
  workflow_dispatch: {}
  push:
    branches: [main, prod]
    paths:
      - .github/workflows/server-deploy.yml
      - cloud/helm/nilo-server-fleet/**
      - apps/server/**
      # FIXME: consider adding all packages just to be safe
      - packages/logger/**
      - packages/network/**
  pull_request:
    types: [opened, reopened, synchronize]
    paths:
      - .github/workflows/server-deploy.yml
      - cloud/helm/nilo-server-fleet/**
      - apps/server/**
      # FIXME: consider adding all packages just to be safe
      - packages/logger/**
      - packages/network/**

env:
  REGISTRY: europe-west4-docker.pkg.dev
  REGISTRY_PATH: nilo-technologies/nilo

jobs:
  build-container-image:
    runs-on: ubuntu-latest
    concurrency:
      group: server-build-${{ github.sha }}
      cancel-in-progress: true
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - id: auth
        uses: ./.github/actions/setup-gcp-auth
        with:
          token_format: access_token
      - uses: docker/setup-buildx-action@v3
      - uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: oauth2accesstoken
          password: ${{ steps.auth.outputs.access_token }}
      - id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.REGISTRY_PATH }}/nilo-server
          tags: |
            type=schedule
            type=sha
            type=sha,format=long
      - uses: docker/build-push-action@v5
        with:
          file: apps/server/Dockerfile
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-fleet:
    needs: build-container-image
    runs-on: ubuntu-latest
    concurrency:
      group: server-fleet-deploy-${{ github.head_ref || github.ref_name }}
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-gcp-auth
        with:
          token_format: access_token
      - uses: google-github-actions/get-gke-credentials@v1
        with:
          cluster_name: nilo-eu-west4-cluster
          location: europe-west4
      - uses: ./.github/actions/setup-node
      - uses: ./.github/actions/get-isolated-environment
        id: isolated-env
        with:
          branch: ${{ github.head_ref || github.ref_name }}
      - if: github.event_name == 'push' && ((github.head_ref || github.ref_name) == 'main' || (github.head_ref || github.ref_name) == 'prod')
        name: Update ${{ github.head_ref || github.ref_name }} agones fleet
        run: |
          helm --debug upgrade --install ${{ steps.isolated-env.outputs.fleet }} cloud/helm/nilo-server-fleet \
            --set imageTag=sha-${{ github.sha }} \
            --set-json 'isolatedEnvironment=${{ steps.isolated-env.outputs.json }}' \
            --set autoscaler.buffer=10 \
            --set autoscaler.maxReplicas=300

      - if: github.event_name == 'pull_request' && (github.head_ref || github.ref_name) != 'main' && (github.head_ref || github.ref_name) != 'prod'
        name: Update agones fleet for isolated staging environment
        run: |
          helm --debug upgrade --install ${{ steps.isolated-env.outputs.fleet }} cloud/helm/nilo-server-fleet \
            --set imageTag=sha-${{ github.sha }} \
            --set-json 'isolatedEnvironment=${{ steps.isolated-env.outputs.json }}'
