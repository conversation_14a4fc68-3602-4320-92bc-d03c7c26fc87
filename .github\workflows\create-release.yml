name: "Create Release"

on:
  workflow_dispatch:
    inputs:
      release_name:
        description: "Release name (e.g., v1.2.0, feature-release)"
        required: true
        type: string

permissions:
  contents: write
  pull-requests: write

jobs:
  create-release:
    name: "Create Release Branch and PR"
    runs-on: ubuntu-latest
    env:
      GIT_EDITOR: "true" # Prevent interactive editor sessions
      GIT_MERGE_AUTOEDIT: "no" # Prevent merge commit message editing
      GIT_PAGER: "cat" # Disable pagers
      GIT_TERMINAL_PROMPT: "0" # Disable credential prompts
    steps:
      - name: "Checkout repository"
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch full history
          fetch-tags: true
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: "Configure Git"
        run: |
          git config --local user.name "fabiopolimeni"
          git config --local user.email "<EMAIL>"
          git config --local core.editor "true"  # Avoid interactive editor sessions
          git config --local merge.tool "true"   # Avoid merge tool prompts
          git config --local core.pager "cat" # Disable pagers

      - name: "Fetch latest from main and prod"
        run: |
          echo "📥 Fetching latest changes..."
          git fetch origin main
          git fetch origin prod
          echo "✅ Fetched latest changes from main and prod"

      - name: "Switch to prod branch"
        run: |
          echo "🔄 Switching to prod branch..."
          git checkout -B prod origin/prod
          echo "✅ Now on prod branch"

      - name: "Find divergence point between prod and main"
        id: find-divergence
        run: |
          echo "🔍 Finding where prod diverged from main..."

          # Find the merge-base (common ancestor) between prod and main
          MERGE_BASE=$(git merge-base origin/prod origin/main)
          echo "Merge base (common ancestor): $MERGE_BASE"

          # Find the first commit on prod that's not on main
          # This gives us the point where prod started diverging
          DIVERGENCE_COMMIT=$(git rev-list --reverse origin/prod ^origin/main | head -n 1)

          if [ -z "$DIVERGENCE_COMMIT" ]; then
            echo "⚠️  No divergence found - prod and main are in sync"
            DIVERGENCE_COMMIT=$MERGE_BASE
          else
            echo "First diverging commit on prod: $DIVERGENCE_COMMIT"
          fi

          # Get the parent of the divergence commit (the last common commit)
          REVERT_TO_COMMIT=$(git rev-parse ${DIVERGENCE_COMMIT}^ 2>/dev/null || echo $MERGE_BASE)
          echo "Will revert back to: $REVERT_TO_COMMIT"

          # Verify commits exist
          git rev-parse --verify $MERGE_BASE >/dev/null
          git rev-parse --verify $DIVERGENCE_COMMIT >/dev/null
          git rev-parse --verify $REVERT_TO_COMMIT >/dev/null

          echo "merge_base=$MERGE_BASE" >> $GITHUB_OUTPUT
          echo "divergence_commit=$DIVERGENCE_COMMIT" >> $GITHUB_OUTPUT
          echo "revert_to_commit=$REVERT_TO_COMMIT" >> $GITHUB_OUTPUT

          echo "✅ Divergence analysis complete"

      - name: "Create release branch"
        id: create-branch
        run: |
          RELEASE_NAME="${{ github.event.inputs.release_name }}"
          BRANCH_NAME="release/$RELEASE_NAME"

          echo "🌿 Creating release branch: $BRANCH_NAME"

          # Check if branch already exists locally
          if git show-ref --verify --quiet refs/heads/$BRANCH_NAME; then
            echo "Branch $BRANCH_NAME already exists locally, deleting it"
            git branch -D $BRANCH_NAME
          fi

          # Check if branch exists on remote
          if git ls-remote --exit-code --heads origin $BRANCH_NAME >/dev/null 2>&1; then
            echo "❌ Branch $BRANCH_NAME already exists on remote"
            echo "Please choose a different release name or delete the existing branch"
            exit 1
          fi

          # Create new branch from current prod
          git checkout -b $BRANCH_NAME
          echo "branch_name=$BRANCH_NAME" >> $GITHUB_OUTPUT
          echo "✅ Created branch: $BRANCH_NAME"

      - name: "Reset release branch to divergence point (non-interactive)"
        run: |
          REVERT_TO_COMMIT="${{ steps.find-divergence.outputs.revert_to_commit }}"

          echo "🔄 Hard resetting branch to divergence point $REVERT_TO_COMMIT"
          git reset --hard
          git reset --hard "$REVERT_TO_COMMIT"
          echo "✅ Reset to divergence point"

      - name: "Merge main into release branch (prefer main, non-interactive)"
        run: |
          echo "🔀 Merging main into release branch..."

          # Prefer main's changes on conflict; commits will be GPG-signed via imported key
          if git merge --no-edit --no-ff -X theirs origin/main -m "Merge main into release branch (prefer main)

          Automatically merged main branch into release branch after resetting to divergence point."; then
            echo "✅ Successfully merged main into release branch"
          else
            echo "⚠️  Merge reported conflicts, attempting auto-resolve by preferring main"
            git add -A
            if git -c core.editor=true merge --continue 2>/dev/null || git commit -m "Resolve conflicts preferring main changes"; then
              echo "✅ Conflicts resolved automatically by preferring main"
            else
              echo "❌ Failed to auto-resolve conflicts"
              git --no-pager status
              exit 1
            fi
          fi

      - name: "Push release branch"
        run: |
          BRANCH_NAME="${{ steps.create-branch.outputs.branch_name }}"

          echo "📤 Pushing release branch: $BRANCH_NAME"
          # Guardrail: only allow pushing release/* branches and ensure we're on that branch
          current_branch="$(git rev-parse --abbrev-ref HEAD)"
          if [[ "$current_branch" != "$BRANCH_NAME" ]]; then
            echo "❌ Refusing to push: current branch ($current_branch) != expected ($BRANCH_NAME)"
            exit 1
          fi
          if [[ "$BRANCH_NAME" != release/* ]]; then
            echo "❌ Refusing to push non-release branch: $BRANCH_NAME"
            exit 1
          fi
          git push origin $BRANCH_NAME
          echo "✅ Pushed release branch to remote"

      - name: "Create Pull Request"
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GH_RELEASE_PAT }}
          script: |
            const branchName = '${{ steps.create-branch.outputs.branch_name }}';
            const releaseName = '${{ github.event.inputs.release_name }}';
            const divergenceCommit = '${{ steps.find-divergence.outputs.divergence_commit }}';
            const mergeBase = '${{ steps.find-divergence.outputs.merge_base }}';

            const title = `Release: ${releaseName}`;
            const body = `## 🚀 Release: ${releaseName}

            This release branch was automatically created and contains:

            ### 📋 Process Summary
            1. ✅ Created \`${branchName}\` from \`prod\`
            2. ✅ Reverted commits back to divergence point (\`${divergenceCommit.substring(0, 8)}\`)
            3. ✅ Merged \`main\` into release branch

            ### 📊 Branch Information
            - **Source Branch:** \`${branchName}\`
            - **Target Branch:** \`prod\`
            - **Divergence Point:** \`${divergenceCommit.substring(0, 8)}\`
            - **Merge Base:** \`${mergeBase.substring(0, 8)}\`

            ### 🔍 What's Included
            This release includes all changes from \`main\` that were not previously in \`prod\`.

            ### ⚠️ Important Notes
            - This PR should be reviewed carefully before merging
            - All tests should pass before merging to \`prod\`
            - Once merged, a new version tag will be created automatically

            ---
            *This PR was created automatically by the Create Release workflow.*`;

            try {
              const { data: pr } = await github.rest.pulls.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: title,
                head: branchName,
                base: 'prod',
                body: body,
                draft: false
              });
              
              core.notice(`Created PR #${pr.number}: ${title}`);
              core.notice(`PR URL: ${pr.html_url}`);
              
              // Set output for potential future steps
              core.setOutput('pr_number', pr.number);
              core.setOutput('pr_url', pr.html_url);
              
            } catch (error) {
              core.setFailed(`Failed to create PR: ${error.message}`);
            }

      - name: "Summary"
        run: |
          BRANCH_NAME="${{ steps.create-branch.outputs.branch_name }}"
          RELEASE_NAME="${{ github.event.inputs.release_name }}"

          echo "🎉 Release process completed successfully!"
          echo ""
          echo "📋 Summary:"
          echo "  • Release Name: $RELEASE_NAME"
          echo "  • Branch Created: $BRANCH_NAME"
          echo "  • Divergence Point: ${{ steps.find-divergence.outputs.divergence_commit }}"
          echo "  • Merge Base: ${{ steps.find-divergence.outputs.merge_base }}"
          echo ""
          echo "🔗 Next Steps:"
          echo "  1. Review the created Pull Request"
          echo "  2. Run tests and ensure everything works"
          echo "  3. Merge the PR to deploy to production"
          echo "  4. A version tag will be created automatically upon merge"
