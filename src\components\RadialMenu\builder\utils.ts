enum OS {
  MacOS = "macos",
  Windows = "windows",
  Linux = "linux",
  Android = "android",
  iOS = "ios",
  Undefined = "undefined",
}

function getOS(): OS {
  const userAgent = window.navigator.userAgent.toLowerCase(),
    macosPlatforms = /(macintosh|macintel|macppc|mac68k|macos)/i,
    windowsPlatforms = /(win32|win64|windows|wince)/i,
    iosPlatforms = /(iphone|ipad|ipod)/i;

  if (macosPlatforms.test(userAgent)) {
    return OS.MacOS;
  } else if (iosPlatforms.test(userAgent)) {
    return OS.iOS;
  } else if (windowsPlatforms.test(userAgent)) {
    return OS.Windows;
  } else if (/android/.test(userAgent)) {
    return OS.Android;
  } else if (/linux/.test(userAgent)) {
    return OS.Linux;
  }

  return OS.Undefined;
}

const isMacOS = () => [OS.MacOS, OS.iOS].includes(getOS());

// const CMD = "⌘";
export const CMD = "cmd"; // Use 'cmd' for consistency with other parts of the code

export const buildShortcut = (parts: string[]) => {
  const isMac = isMacOS();
  if (isMac) {
    // Return in a style like "⌘N"
    return parts
      .map((part) => {
        if (part === CMD) {
          return "⌘";
        }

        if (part === "shift") {
          return "⇧";
        }

        return part.toUpperCase();
      })
      .join("");
  } else {
    // Return in a style like "Ctrl+N"
    return parts
      .map((part) => {
        if (part === CMD) {
          return "Ctrl";
        }

        if (part === "shift") {
          return "⇧";
        }

        return part.toUpperCase();
      })
      .join("+");
  }
};
