import React from "react";

import { PlatformHeader } from "../platform/PlatformHeader";

export const DisabledTabPage: React.FC<{
  reactivateThisTab?: () => void;
}> = ({ reactivateThisTab }) => {
  return (
    <>
      <div className="fixed top-0 left-0 w-full h-full bg-black/90 flex justify-center items-center">
        <div className="text-white text-center max-w-[80%] animate-fade-in">
          <h2 className="text-[2.5rem] mb-2 font-bold tracking-[1px]">
            Session Already Active
          </h2>
          <svg
            className="h-5 mx-auto my-4 block"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 25 15"
          >
            <path
              d="M0,7.5 Q6.25,0 12.5,7.5 T25,7.5"
              fill="none"
              stroke="#ffffff"
              strokeWidth="2"
            />
          </svg>
          <p className="text-[1.2rem] mb-6 leading-[1.6]">
            This application is already open in another tab or window.
            <br />
            To ensure a consistent experience, please use the existing session.
          </p>
          {reactivateThisTab && (
            <>
              <p className="text-[1.1rem] mb-6 font-semibold">
                You can either close this tab manually and return to the active
                session, or reactivate this tab by clicking the button below.
              </p>
              <button
                className="mt-5 px-6 py-3 bg-white text-black border-none rounded-full cursor-pointer text-base font-semibold transition-all duration-300 hover:bg-[#f0f0f0] hover:-translate-y-0.5 hover:shadow-[0_4px_8px_rgba(255,255,255,0.2)]"
                onClick={reactivateThisTab}
              >
                Reload Page
              </button>
            </>
          )}
        </div>
      </div>
      <PlatformHeader showLogo={true} showUser={true} />
    </>
  );
};
