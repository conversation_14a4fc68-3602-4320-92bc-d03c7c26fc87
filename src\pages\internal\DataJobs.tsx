import {
  collection,
  CollectionReference,
  limit,
  QueryDocumentSnapshot,
} from "firebase/firestore";
import { useNavigate } from "react-router-dom";
import { useAuthState } from "react-firebase-hooks/auth";
import { db, auth } from "@/config/firebase";
import { DbCollections, DbInternalDataJob } from "@nilo/firebase-schema";
import { orderByT, queryT, useFirebaseQuery } from "@/hooks/firebaseHooks";
import { Typography } from "@/components/ui-nilo/Typography";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

const LIMIT = 100;

const formatDate = (timestamp: DbInternalDataJob["createdAt"]) => {
  if (!timestamp) return "N/A";
  return new Date(timestamp.toDate()).toLocaleString();
};

const getStatusBackgroundColorClassName = (
  status: DbInternalDataJob["status"]
) => {
  switch (status) {
    case "completed":
      return "bg-green-500";
    case "failed":
      return "bg-red-500";
    case "processing":
      return "bg-yellow-500";
    case "pending":
      return "bg-blue-500";
    default:
      return "bg-gray-500";
  }
};

const DataJobRow = ({
  job,
  currentUserId,
}: {
  job: QueryDocumentSnapshot<DbInternalDataJob>;
  currentUserId: string | undefined;
}) => {
  const navigate = useNavigate();
  const data = job.data();
  const isCurrentUser = data.userId === currentUserId;

  return (
    <TableRow
      className={`${isCurrentUser ? "bg-blue-50/10" : ""} cursor-pointer hover:bg-gray-50/5`}
      id={job.id}
      onClick={() => {
        navigate(`/internal/dataJobs/${job.id}`);
      }}
    >
      <TableCell className="font-medium w-32">
        <div className="flex items-center gap-2 min-w-0">
          <span className="text-brand truncate">{data.config.type}</span>
          {isCurrentUser && (
            <Badge variant="secondary" className="text-xs flex-shrink-0">
              Yours
            </Badge>
          )}
        </div>
      </TableCell>
      <TableCell className="w-24">
        <Badge className={getStatusBackgroundColorClassName(data.status)}>
          {data.status}
        </Badge>
      </TableCell>
      <TableCell className="text-sm text-muted-foreground w-32">
        <span className="truncate block" title={job.id}>
          {job.id}
        </span>
      </TableCell>
      <TableCell className="text-sm text-muted-foreground min-w-0 flex-1">
        <div className="break-words max-w-xs">{data.message ?? "-"}</div>
      </TableCell>
      <TableCell className="text-sm w-40">
        <span className="text-xs">{formatDate(data.createdAt)}</span>
      </TableCell>
      <TableCell className="text-sm w-24">
        {data.subJobs !== undefined ? (
          <>
            <Typography.Body level={2} color="light">
              <span className="text-green-400">
                {data.completedSubJobs ?? 0}
              </span>
              {(data.failedSubJobs ?? 0) > 0 && (
                <>
                  {"+"}
                  <span className="text-red-400">
                    {data.failedSubJobs ?? 0}
                  </span>
                </>
              )}
              {" / "}
              {data.subJobs}
            </Typography.Body>
          </>
        ) : (
          "-"
        )}
      </TableCell>
    </TableRow>
  );
};

export default function DataJobs() {
  const navigate = useNavigate();
  const [user] = useAuthState(auth);

  const jobsCollection = collection(
    db,
    DbCollections.internalDataJobs
  ) as CollectionReference<DbInternalDataJob, DbInternalDataJob>;
  const q = queryT(jobsCollection, orderByT("createdAt", "desc"), limit(LIMIT));
  const jobs = useFirebaseQuery(q);

  const handleNewJobClick = () => {
    navigate("/internal/dataJobs/new");
  };

  return (
    <div className="h-screen bg-black text-icon-primary overflow-y-auto">
      <div className="mx-auto space-y-6 pt-8 pb-24 max-w-7xl px-4">
        {/* Header */}
        <header className="flex justify-between items-center">
          <div>
            <Typography.Title level={1} color="light" className="mb-2">
              Internal Data Jobs
            </Typography.Title>
            <Typography.Body level={1} color="light" weight="medium">
              Last {LIMIT} jobs
            </Typography.Body>
          </div>
          <Button
            onClick={handleNewJobClick}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Schedule New Job
          </Button>
        </header>

        {/* Jobs Table */}
        <div className="bg-surface-primary rounded-lg border border-accessories-divider overflow-x-auto">
          <Table className="min-w-full">
            <TableHeader>
              <TableRow>
                <TableHead className="w-32">Type</TableHead>
                <TableHead className="w-24">Status</TableHead>
                <TableHead className="w-32">Job ID</TableHead>
                <TableHead className="min-w-0 flex-1">Message</TableHead>
                <TableHead className="w-40">Created</TableHead>
                <TableHead className="w-24">Sub Jobs</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {jobs.map((job) => (
                <DataJobRow key={job.id} job={job} currentUserId={user?.uid} />
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
