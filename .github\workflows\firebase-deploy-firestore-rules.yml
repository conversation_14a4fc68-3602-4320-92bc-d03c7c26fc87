# This is run for all branches that have their environment set up (determined by the existence of the firestore database)
# It deploys the firestore rules and indexes if they have changed

name: Deploy Firestore Database Rules
on:
  workflow_dispatch: {}
  push:
    paths:
      - .github/workflows/firebase-deploy-firestore-rules.yml
      - firestore.rules
      - firestore.indexes.json

concurrency:
  group: firestore-rules-${{ github.head_ref || github.ref_name }}
  cancel-in-progress: true

jobs:
  deploy_firestore_rules:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-gcp-auth
      - uses: ./.github/actions/setup-node
      - uses: ./.github/actions/get-isolated-environment
        id: isolated-env
        with:
          branch: ${{ github.head_ref || github.ref_name }}
          updateFirebaseJson: true
      - id: check-database
        run: |
          if pnpm exec firebase firestore:databases:get '${{ steps.isolated-env.outputs.database }}' &> /dev/null; then
            echo "Database exists"
            echo "exists=true" >> $GITHUB_OUTPUT
          else
            echo "Database does not exist"
            echo "exists=false" >> $GITHUB_OUTPUT
          fi
      - if: steps.check-database.outputs.exists == 'true'
        name: Deploy rules and indexes
        uses: ./.github/actions/run-single-command-with-retry
        with:
          command: pnpm exec firebase deploy --non-interactive --force --only firestore
