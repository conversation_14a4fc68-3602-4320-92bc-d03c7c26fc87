# This is run for all branches that have their environment set up (determined by the existence of the storage bucket)
# It deploys the storage rules if they have changed

name: Deploy Firebase Storage Rules
on:
  workflow_dispatch: {}
  push:
    paths:
      - .github/workflows/firebase-deploy-storage-rules.yml
      - storage.rules

concurrency:
  group: firebase-storage-rules-${{ github.head_ref || github.ref_name }}
  cancel-in-progress: true

jobs:
  deploy_storage_rules:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - id: auth
        uses: ./.github/actions/setup-gcp-auth
        with:
          token_format: access_token
      - uses: ./.github/actions/setup-node
      - uses: ./.github/actions/get-isolated-environment
        id: isolated-env
        with:
          branch: ${{ github.head_ref || github.ref_name }}
          updateFirebaseJson: true
      - id: check-bucket
        run: |
          if gcloud storage buckets describe gs://${{ steps.isolated-env.outputs.bucket }} &> /dev/null; then
            echo "Bucket already exists"
            echo "exists=true" >> $GITHUB_OUTPUT
          else
            echo "Bucket does not exist"
            echo "exists=false" >> $GITHUB_OUTPUT
          fi
      - if: steps.check-bucket.outputs.exists == 'true'
        run: pnpm exec firebase target:apply storage ${{ steps.isolated-env.outputs.name }} ${{ steps.isolated-env.outputs.bucket }}
      - if: steps.check-bucket.outputs.exists == 'true'
        name: Deploy storage rules
        uses: ./.github/actions/run-single-command-with-retry
        with:
          command: pnpm exec firebase deploy --non-interactive --force --only storage:${{ steps.isolated-env.outputs.name }}
