import { PhysicsOptions } from "../types/PhysicsOptions";

export abstract class PhysicsValidationError {
  constructor(
    public readonly message: string,
    public readonly code: string
  ) {}

  abstract hasError(_options: PhysicsOptions): boolean;

  fixError(options: PhysicsOptions): PhysicsOptions {
    return options;
  }
}

// Specific validation error classes
export class AsisMustBeStaticError extends PhysicsValidationError {
  constructor() {
    super("Asis objects must be static", "ASIS_MUST_BE_STATIC");
  }

  override hasError(options: PhysicsOptions): boolean {
    return options.shapeType === "asis" && !options.isStatic;
  }

  override fixError(options: PhysicsOptions): PhysicsOptions {
    return { ...options, isStatic: true };
  }
}

export class SensorMustBeStaticError extends PhysicsValidationError {
  constructor() {
    super("Sensor objects must be static", "SENSOR_MUST_BE_STATIC");
  }

  override hasError(options: PhysicsOptions): boolean {
    return (options.isSensor ?? false) && !options.isStatic;
  }

  override fixError(options: PhysicsOptions): PhysicsOptions {
    return { ...options, isStatic: true };
  }
}

export type PhysicsValidationResult =
  | {
      isValid: true;
      options: PhysicsOptions;
    }
  | {
      isValid: false;
      errors: PhysicsValidationError[];
      formatErrors: () => string;
      options: PhysicsOptions;
    };

const allErrors: PhysicsValidationError[] = [
  new AsisMustBeStaticError(),
  new SensorMustBeStaticError(),
];

export function validatePhysicsOptions(
  options: PhysicsOptions,
  fixErrors: boolean = false
): PhysicsValidationResult {
  const invalidErrors: PhysicsValidationError[] = [];
  for (const error of allErrors) {
    if (error.hasError(options)) {
      invalidErrors.push(error);
      if (fixErrors) {
        options = error.fixError(options);
      }
    }
  }

  if (invalidErrors.length > 0) {
    return {
      isValid: false,
      errors: invalidErrors,
      formatErrors: () =>
        invalidErrors.map((error) => error.message).join(", "),
      options,
    };
  }
  return { isValid: true, options };
}
