import {
  EyeIcon,
  GlobeAltIcon,
  UserGroupIcon,
  StarIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import {
  DocumentDuplicateIcon as DocumentDuplicateIconSolid,
  StarIcon as StarIconSolid,
} from "@heroicons/react/24/solid";
import { useState } from "react";

import { ConfirmationModal } from "../ui-nilo/ConfirmationModal";

import {
  remixWorld,
  setWorldFeatured,
  setWorldPublished,
} from "@/platform/actions/world-actions";
import { useDeleteWorld } from "@/hooks/useDeleteWorld";
import { useNiloPlatformNavigate } from "@/platform/hook/useNiloPlatformNavigate";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useIsAdmin } from "@/hooks/useIsAdmin";

export const WorldThumbnailMoreMenu = ({
  userId,
  worldId,
  isOwner,
  worldData,
  ownerProfile,
}: {
  userId: string;
  worldId: string;
  isOwner: boolean;
  worldData?: {
    isPublic?: boolean;
    isFeatured?: boolean;
    ownerId?: string;
    displayName?: string;
  };
  ownerProfile?: {
    avatarUrl?: string;
    displayName?: string;
    username?: string;
  };
}) => {
  const isAdmin = useIsAdmin();
  const [isCloning, setIsCloning] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [isTogglingFeatured, setIsTogglingFeatured] = useState(false);

  const {
    isDeleting,
    showDeleteConfirm,
    handleDeleteWorld,
    openDeleteConfirm,
    closeDeleteConfirm,
  } = useDeleteWorld();

  const { navigateToWorld, navigateToUserProfile } = useNiloPlatformNavigate();

  const isWorldPublished = Boolean(worldData?.isPublic);

  const handleCloneWorld = async () => {
    if (!userId || !worldId) {
      console.error("🚫 User or World ID is missing for clone action.");
      return;
    }

    try {
      const newWorldDisplayName = worldData?.displayName ?? "New World";
      const newWorldId = await remixWorld(worldId, newWorldDisplayName);
      navigateToWorld(newWorldId, { inNewTab: true });
    } catch (error) {
      console.error("💥 Error cloning world:", error);
      throw error;
    }
  };

  const handleVisit = () => {
    // Always visit directly, don't use onNavigate which might trigger remix behavior
    navigateToWorld(worldId);
  };

  const handlePublishToggle = async () => {
    if (!worldId || !worldData) return;

    setIsPublishing(true);
    try {
      const newPublishState = !worldData.isPublic;
      await setWorldPublished(worldId, newPublishState);
    } catch (error) {
      console.error("💣 Error toggling publish state:", error);
    } finally {
      setIsPublishing(false);
    }
  };

  const handleFeaturedToggle = async () => {
    if (!worldId || !worldData) return;

    setIsTogglingFeatured(true);
    try {
      const newFeaturedState = !worldData.isFeatured;
      await setWorldFeatured(worldId, newFeaturedState);
    } catch (error) {
      console.error("💥 Error toggling featured state:", error);
    } finally {
      setIsTogglingFeatured(false);
    }
  };

  const handleMoreByBuilder = () => {
    if (worldData?.ownerId) {
      navigateToUserProfile(worldData.ownerId);
    }
  };

  const onDeleteWorld = () => {
    if (worldId && userId) {
      handleDeleteWorld(worldId, userId);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
          className="p-1 rounded-full hover:opacity-80 transition-opacity"
        >
          <img
            src={"/icons/vertical-ellipsis-circle.svg"}
            alt="More options"
            className="w-5 h-5"
          />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="bg-black/90 border-white/20 text-white"
      >
        <DropdownMenuItem
          onClick={async (e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsCloning(true);
            try {
              await handleCloneWorld();
            } catch (error) {
              console.error("💥 Error cloning world:", error);
            } finally {
              setIsCloning(false);
            }
          }}
          className="flex items-center space-x-2 focus:bg-white/10"
          disabled={isCloning}
        >
          <DocumentDuplicateIconSolid className="w-4 h-4" />
          <span>{isCloning ? "Cloning..." : "Remix World"}</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={handleVisit}
          className="flex items-center space-x-2 focus:bg-white/10"
        >
          <EyeIcon className="w-4 h-4" />
          <span>Visit</span>
        </DropdownMenuItem>

        {(isAdmin || isOwner) && (
          <DropdownMenuSeparator className="bg-white/20" />
        )}

        {isAdmin && (
          <DropdownMenuItem
            onClick={handleFeaturedToggle}
            className="flex items-center space-x-2 focus:bg-white/10"
            disabled={isTogglingFeatured}
          >
            {worldData?.isFeatured ? (
              <StarIconSolid className="w-4 h-4 text-yellow-400" />
            ) : (
              <StarIcon className="w-4 h-4" />
            )}
            <span>
              {isTogglingFeatured
                ? worldData?.isFeatured
                  ? "Unfeaturing..."
                  : "Featuring..."
                : worldData?.isFeatured
                  ? "Unfeature"
                  : "Feature"}
            </span>
          </DropdownMenuItem>
        )}

        {isOwner && (
          <>
            {!isWorldPublished && (
              <DropdownMenuItem
                onClick={openDeleteConfirm}
                className="flex items-center space-x-2 focus:bg-white/10"
              >
                <TrashIcon className="w-4 h-4" />
                <span>Delete</span>
              </DropdownMenuItem>
            )}
            <DropdownMenuItem
              onClick={handlePublishToggle}
              className="flex items-center space-x-2 focus:bg-white/10"
              disabled={isPublishing}
            >
              <GlobeAltIcon className="w-4 h-4" />
              <span>
                {isPublishing
                  ? worldData?.isPublic
                    ? "Unpublishing..."
                    : "Publishing..."
                  : worldData?.isPublic
                    ? "Unpublish"
                    : "Publish"}
              </span>
            </DropdownMenuItem>
          </>
        )}

        {!isOwner && worldData?.ownerId && (
          <>
            <DropdownMenuItem
              onClick={handleMoreByBuilder}
              className="flex items-center space-x-2 focus:bg-white/10"
            >
              {ownerProfile?.avatarUrl ? (
                <img
                  src={ownerProfile.avatarUrl}
                  alt={
                    ownerProfile.displayName ||
                    ownerProfile.username ||
                    "Builder"
                  }
                  className="w-4 h-4 rounded-full object-cover"
                />
              ) : (
                <UserGroupIcon className="w-4 h-4" />
              )}
              <span>More by this builder</span>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
      <ConfirmationModal
        open={showDeleteConfirm}
        onOpenChange={closeDeleteConfirm}
        title="Delete World"
        description="Are you sure you want to delete this world?"
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={onDeleteWorld}
        loading={isDeleting}
        variant="destructive"
      />
    </DropdownMenu>
  );
};
