import * as React from "react";

const useMatchMedia = (query: string) => {
  const [matches, setMatches] = React.useState(
    window.matchMedia(query).matches
  );

  React.useEffect(() => {
    const media = window.matchMedia(query);

    if (media.matches !== matches) {
      setMatches(media.matches);
    }

    const listener = () => setMatches(media.matches);
    media.addEventListener("change", listener);

    return () => media.removeEventListener("change", listener);
  }, [matches, query]);

  return matches;
};

export const isMobile = () => !window.matchMedia("(pointer: fine)").matches;

export function useIsMobile() {
  const matches = useMatchMedia("(pointer: fine)"); // Check if the device has a fine pointer, e.g., a mouse

  return !matches;
}
