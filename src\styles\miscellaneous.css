/* Custom scrollbar styling for textarea */
@layer utilities {
  .scrollbar-thin-nilo {
    scrollbar-width: thin;
    scrollbar-color: var(--color-nilo-fill-tertiary-hover) transparent;
    -ms-overflow-style: none;
  }
  .scrollbar-thin-nilo::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  .scrollbar-thin-nilo::-webkit-scrollbar-track {
    background: transparent;
  }
  .scrollbar-thin-nilo::-webkit-scrollbar-thumb {
    background-color: var(--color-nilo-fill-tertiary-hover);
    border-radius: 3px;
  }
  .scrollbar-thin-nilo::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-nilo-fill-tertiary);
  }
}

/* Monaco Editor styling */
.monaco-editor {
  --vscode-editor-background: transparent !important;
  --vscode-editorGutter-background: transparent !important;
}
