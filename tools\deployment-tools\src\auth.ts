import * as os from "os";
import * as fs from "fs";
import * as path from "path";
import { google, Auth } from "googleapis";

export interface FirebaseToolsConfig {
  activeProjects: {
    [directory: string]: string;
  };
  tokens?: {
    refresh_token?: string;
    access_token?: string;
    expires_at?: number;
  };
  user?: {
    email?: string;
  };
}

/**
 * Authenticates using the same methods as firebase-tools:
 * 1. FIREBASE_TOKEN environment variable (CI token)
 * 2. Local Firebase CLI login tokens
 * 3. Service account key file
 * 4. Application Default Credentials
 */
export async function authenticate(): Promise<
  Auth.GoogleAuth | Auth.OAuth2Client
> {
  // 1. Check for FIREBASE_TOKEN (CI token from firebase login:ci)
  const firebaseToken = process.env.FIREBASE_TOKEN;
  if (firebaseToken) {
    console.debug("Using FIREBASE_TOKEN for authentication");
    const auth = new google.auth.OAuth2();
    auth.setCredentials({ access_token: firebaseToken });
    return auth;
  }

  // 2. Check for local Firebase CLI login tokens
  const homeDir = os.homedir();
  const configPath = path.join(
    homeDir,
    ".config",
    "configstore",
    "firebase-tools.json"
  );

  if (fs.existsSync(configPath)) {
    try {
      const configContent = fs.readFileSync(configPath, "utf8");
      const config: FirebaseToolsConfig = JSON.parse(configContent);

      if (config.tokens?.refresh_token) {
        console.debug("Using Firebase CLI local login tokens");
        const auth = new google.auth.OAuth2();
        auth.setCredentials({
          refresh_token: config.tokens.refresh_token,
          access_token: config.tokens.access_token,
        });
        return auth;
      }
    } catch (error) {
      console.warn("Warning: Could not parse Firebase CLI configuration file", {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  // 3. Check for service account key file
  const serviceAccountPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
  if (serviceAccountPath && fs.existsSync(serviceAccountPath)) {
    console.debug("Using service account key file");
    return new google.auth.GoogleAuth({
      keyFile: serviceAccountPath,
      scopes: ["https://www.googleapis.com/auth/cloud-platform"],
    });
  }

  // 4. Fall back to Application Default Credentials
  console.debug("Using Application Default Credentials");
  return new google.auth.GoogleAuth({
    scopes: ["https://www.googleapis.com/auth/cloud-platform"],
  });
}

/**
 * Gets the current project ID from the authenticated client
 */
export async function getProjectId(
  auth: Auth.GoogleAuth | Auth.OAuth2Client
): Promise<string | null> {
  try {
    if (auth instanceof Auth.GoogleAuth) {
      return await auth.getProjectId();
    }
    return null;
  } catch (error) {
    console.warn("Could not determine project ID from authentication", {
      error: error instanceof Error ? error.message : String(error),
    });
    return null;
  }
}
