import EventEmitter from "eventemitter3";
import { AnimatePresence, motion } from "framer-motion";
import React, {
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { MathUtils, Vector2, Vector2Like, Vector2Tuple } from "three";
import { RadialUtils, Segment } from "./utils";
import { RadialItem, renderRadialItem } from "./modules";
import {
  RadialItemContext,
  RadialItemEvents,
  RadialMenuProvider,
} from "./context";
import { useIsMobile } from "./useIsMobile";
import { usePanelActions } from "@/hooks/usePanelActions";
import { usePanelId } from "@/hooks/usePanel";
import UIManager from "@/core/util/UIManager";
import { cn } from "@/lib/utils";

const tmp2 = new Vector2();

export interface RadialSection {
  /** Icon displayed in the section */
  icon: ReactNode;

  /** Title displayed in the section */
  title: string;

  /** Keyboard shortcut for the section */
  shortcut?: string;

  /** Whether the section is disabled */
  disabled?: boolean;

  /** Is moving cursor out of the radial should reduce opacity for the menu if current section is opened */
  reduceMenuOpacity?: boolean;

  /** The opacity level to set when the cursor is outside the menu */
  menuOpacityOutside?: number;

  /** Item in the section */
  item?: RadialItem;

  /** Click handler for the section */
  onClick?: () => void;
}

const RadialMenuModule = ({
  visible,
  item,
}: {
  segment: Segment;
  visible: boolean;
  item: RadialItem;
}) => {
  return <AnimatePresence>{visible && renderRadialItem(item)}</AnimatePresence>;
};

const INNER_RADIUS = 64; // Inner radius of the main menu
const OUTER_RADIUS = 145; // Outer radius of the main menu

const RADIAL_PADDING = 96; // Padding around the radial menu to prevent drag/creation

const preventOutside = (pos: Vector2Like, isMobile: boolean): Vector2Like => {
  const multiplier = isMobile ? 0.75 : 1.0;
  const padding = RADIAL_PADDING * multiplier;
  const radius = OUTER_RADIUS * multiplier;

  const minX = padding + radius;
  const maxX = window.innerWidth - padding - radius;
  const minY = padding + radius;
  const maxY = window.innerHeight - padding - radius;

  let x: number;
  let y: number;

  if (maxX <= minX) {
    x = (minX + maxX) / 2;
  } else {
    x = MathUtils.clamp(pos.x, minX, maxX);
  }

  if (maxY <= minY) {
    y = (minY + maxY) / 2;
  } else {
    y = MathUtils.clamp(pos.y, minY, maxY);
  }

  return { x, y };
};

const desktopDragSetup = (
  target: HTMLElement,
  onClose: () => void,
  panelId: string,
  lock: (value: boolean) => void
) => {
  const offset = new Vector2();
  const position = new Vector2();
  const origin = new Vector2();
  let isDragging = false;

  const panel = UIManager.instance.getPanel(panelId)!;

  const startDrag = (x: number, y: number) => {
    offset.set(x, y);
    // position.set(target.offsetLeft, target.offsetTop);
    position.set(panel.position.x, panel.position.y);
    isDragging = true;
    lock(true);

    const rect = target.getBoundingClientRect();

    origin.set(
      offset.x - rect.width / 2 - rect.left,
      offset.y - rect.height / 2 - rect.top
    );

    target.style.transitionDuration = "0.2s";
    target.style.transitionTimingFunction = "cubic-bezier(0.33, 1, 0.68, 1)";

    target.style.transformOrigin = `${origin.x}px ${origin.y}px`;
    target.style.transform = "scale(0.75)";
  };

  const onPointerDown = (event: PointerEvent) => {
    if (event.button !== 1) return; // Only allow middle mouse button drag
    startDrag(event.clientX, event.clientY);
  };

  const onPointerMove = (event: PointerEvent) => {
    if (!isDragging) return;
    const dx = event.clientX - offset.x;
    const dy = event.clientY - offset.y;

    panel.options.screenPosition = preventOutside(
      {
        x: position.x + dx,
        y: position.y + dy,
      },
      false
    );
  };

  const onPointerUp = () => {
    isDragging = false;

    target.style.transitionDuration = "0.75s";
    target.style.transitionTimingFunction =
      "linear(0, 0.1261955826176666, 0.4132640382972399, 0.7368104127813003, 1.007846824961594, 1.1812001810496797, 1.250642887206604, 1.236586395369334, 1.1718614896285013, 1.0896059298903558, 1.015362797385151, 0.9637589063293496, 0.9389249316766018, 0.9372353901997679, 0.9509038116896882, 0.971285220062452, 0.9912127641275379, 1.0061605072819542, 1.0143739861288894, 1.0163028280829531, 1.0137156509523786, 1.0088177404578724, 1.003578021904284, 0.9993495983340094, 0.9967692901076747, 1, 0.9962474680037349, 0.9973796812271148, 0.998727775527958, 0.9998983805041842, 1.0006789611034836, 1, 1)";

    target.style.transform = "scale(1.0)"; // Reset scale
    lock(false);
  };

  const wp = new Vector2();
  const we = new Vector2();
  const onWindowDown = (event: PointerEvent) => {
    if (event.button !== 0 && event.button !== 2) return;

    wp.set(event.clientX, event.clientY);
  };

  const onWindowUp = (event: MouseEvent) => {
    if (event.button !== 0 && event.button !== 2) return;
    we.set(event.clientX, event.clientY);

    const eventTarget = event.target as HTMLElement;
    if (target.contains(eventTarget)) {
      const menuPos = panel.options.screenPosition!;
      const dist = new Vector2(event.clientX, event.clientY).distanceTo(
        menuPos
      );

      if (dist < INNER_RADIUS && event.button === 0) {
        onClose(); // Close if we click inside the inner radius, e.g. empty space. Also the mouse button must be the left button.
      }

      return; // Prevent closing if clicking on an item
    }

    if (wp.distanceTo(we) >= 1) {
      return;
    }

    // If the click is within 1 pixel, consider it a click
    onClose();
  };

  target.addEventListener("pointerdown", onPointerDown);
  window.addEventListener("pointermove", onPointerMove);
  window.addEventListener("pointerup", onPointerUp);
  window.addEventListener("pointerdown", onWindowDown);
  window.addEventListener("pointerup", onWindowUp);

  return () => {
    target.removeEventListener("pointerdown", onPointerDown);
    window.removeEventListener("pointermove", onPointerMove);
    window.removeEventListener("pointerup", onPointerUp);
    window.removeEventListener("pointerdown", onWindowDown);
    window.removeEventListener("pointerup", onWindowUp);
  };
};

const mobileDragSetup = (
  target: HTMLElement,
  handle: SVGCircleElement,
  onClose: () => void,
  panelId: string
) => {
  const offset = new Vector2();
  const position = new Vector2();
  const origin = new Vector2();

  let isDragging = false;

  const panel = UIManager.instance.getPanel(panelId)!;

  const startDrag = (x: number, y: number) => {
    offset.set(x, y);
    position.set(panel.position.x, panel.position.y);

    const rect = target.getBoundingClientRect();

    origin.set(
      offset.x - rect.width / 2 - rect.left,
      offset.y - rect.height / 2 - rect.top
    );

    if (origin.length() >= INNER_RADIUS) return;

    isDragging = true;

    target.style.transitionDuration = "0.2s";
    target.style.transitionTimingFunction = "cubic-bezier(0.33, 1, 0.68, 1)";

    target.style.transformOrigin = `${origin.x}px ${origin.y}px`;
    target.style.transform = "scale(0.6)";
  };

  const stopDrag = () => {
    isDragging = false;

    target.style.transitionDuration = "0.75s";
    target.style.transitionTimingFunction =
      "linear(0, 0.1261955826176666, 0.4132640382972399, 0.7368104127813003, 1.007846824961594, 1.1812001810496797, 1.250642887206604, 1.236586395369334, 1.1718614896285013, 1.0896059298903558, 1.015362797385151, 0.9637589063293496, 0.9389249316766018, 0.9372353901997679, 0.9509038116896882, 0.971285220062452, 0.9912127641275379, 1.0061605072819542, 1.0143739861288894, 1.0163028280829531, 1.0137156509523786, 1.0088177404578724, 1.003578021904284, 0.9993495983340094, 0.9967692901076747, 1, 0.9962474680037349, 0.9973796812271148, 0.998727775527958, 0.9998983805041842, 1.0006789611034836, 1, 1)";

    target.style.transform = "scale(0.75)"; // Reset scale
  };

  const onTouchStart = (event: TouchEvent) => {
    event.stopPropagation(); // Stop propagation to avoid conflicts
  };

  const onPointerDown = (event: PointerEvent) => {
    if (!event.isPrimary) {
      stopDrag();
      return;
    }

    event.stopPropagation(); // Stop propagation to avoid conflicts
    startDrag(event.clientX, event.clientY);
  };

  const onPointerMove = (event: PointerEvent) => {
    if (!event.isPrimary) return;

    if (!isDragging) return;
    const dx = event.clientX - offset.x;
    const dy = event.clientY - offset.y;

    panel.options.screenPosition = preventOutside(
      {
        x: position.x + dx,
        y: position.y + dy,
      },
      true
    );
  };

  const wp = new Vector2();
  const we = new Vector2();
  const onWindowDown = (event: PointerEvent) => {
    wp.set(event.clientX, event.clientY);
  };

  const onWindowUp = (event: PointerEvent) => {
    if (!event.isPrimary) return;
    stopDrag();

    const eventTarget = event.target as HTMLElement;
    if (target.contains(eventTarget)) {
      return; // Prevent closing if clicking on an item
    }

    we.set(event.clientX, event.clientY);
    if (wp.distanceTo(we) >= 1) {
      return;
    }

    // If the click is within 1 pixel, consider it a click
    onClose();
  };

  handle.addEventListener("touchstart", onTouchStart);
  handle.addEventListener("pointerdown", onPointerDown);
  window.addEventListener("pointermove", onPointerMove);
  window.addEventListener("pointerdown", onWindowDown);
  window.addEventListener("pointerup", onWindowUp);

  return () => {
    handle.removeEventListener("touchstart", onTouchStart);
    handle.removeEventListener("pointerdown", onPointerDown);
    window.removeEventListener("pointermove", onPointerMove);
    window.removeEventListener("pointerdown", onWindowDown);
    window.removeEventListener("pointerup", onWindowUp);
  };
};

export const RadialMenu = ({
  sections,
  startAngle = 270,
  isCentered = true,
}: {
  sections: RadialSection[];
  startAngle?: number;
  isCentered?: boolean;
}) => {
  const { close } = usePanelActions();
  const panelId = usePanelId();
  const ref = useRef<HTMLDivElement>(null);
  const handleRef = useRef<SVGCircleElement>(null);

  const [emitters] = useState<EventEmitter<RadialItemEvents>[]>(() =>
    sections.map(() => new EventEmitter())
  );

  const [opacity, setOpacity] = useState(1);
  const [isLocked, setIsLocked] = useState(false);
  const [nextActiveButton, setNextActiveButton] = useState<number | null>(null);
  const [activeButton, setActiveButton] = useState<number | null>(null);

  const isMobile = useIsMobile();

  const { size: segmentsArea, segments } = RadialUtils.generateSegments({
    total: sections.length,
    gap: 8,
    startAngle: startAngle * MathUtils.DEG2RAD, // Start at -135 degrees (or 225 degrees)
    endAngle: (startAngle + 360) * MathUtils.DEG2RAD, // Full circle (360 degrees + 270 degrees)
    innerRadius: 64,
    outerRadius: OUTER_RADIUS,
    borderRadius: 12,
    isCentered,
  });

  const size = {
    x: segmentsArea.x + 2, // Add 2 to account for the hover line width
    y: segmentsArea.y + 2, // Add 2 to account for the hover line width
  };

  const onButtonClick = useCallback(
    (index: number) => {
      emitters[index].emit("click");
    },
    [emitters]
  );

  const onClose = useCallback(() => {
    setNextActiveButton(null);
  }, []);

  useEffect(() => {
    if (isLocked) {
      return;
    }

    setActiveButton(nextActiveButton);
  }, [nextActiveButton, isLocked]);

  useEffect(() => {
    const target = ref.current;
    const handle = handleRef.current;
    if (!target || !handle) return;

    if (isMobile) {
      return mobileDragSetup(target, handle, close, panelId);
    }

    return desktopDragSetup(target, close, panelId, setIsLocked);
  }, [isMobile, close, panelId]);

  useEffect(() => {
    const onKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        event.preventDefault();
        event.stopPropagation();
        close();
      }
    };

    window.addEventListener("keydown", onKeyDown, { capture: true });

    return () => {
      window.removeEventListener("keydown", onKeyDown, { capture: true });
    };
  }, [close]);

  const isPointerOutside = useCallback(
    (pointer: Vector2Tuple) => {
      const target = ref.current;
      if (!target) return false;

      const { x, y } = target.getBoundingClientRect();

      const distance = tmp2.set(pointer[0] - x, pointer[1] - y).length();
      const scale = isMobile ? 0.75 : 1.0;

      return distance > OUTER_RADIUS * scale;
    },
    [isMobile]
  );

  const getScreenPosition = useCallback(() => {
    const target = ref.current;
    if (!target) return new Vector2(0, 0);

    const { x, y } = target.getBoundingClientRect();
    return new Vector2(x, y);
  }, []);

  useEffect(() => {
    if (activeButton === null) {
      setOpacity(1);
      return;
    }

    const section = sections[activeButton];
    if (!section.reduceMenuOpacity) {
      return;
    }

    const desktopChecker = () => {
      const onPointerMove = (event: PointerEvent) => {
        if (isPointerOutside([event.clientX, event.clientY])) {
          setOpacity(section.menuOpacityOutside || 0);
        } else {
          setOpacity(1);
        }
      };

      window.addEventListener("pointermove", onPointerMove);

      return () => {
        window.removeEventListener("pointermove", onPointerMove);
        setOpacity(1); // Reset opacity when component unmounts
      };
    };

    const mobileChecker = () => {
      const onPointerDown = (event: PointerEvent) => {
        if (!event.isPrimary) return;

        if (isPointerOutside([event.clientX, event.clientY])) {
          setOpacity(section.menuOpacityOutside || 0);
        } else {
          setOpacity(1);
        }
      };

      const onPointerUp = (event: PointerEvent) => {
        if (!event.isPrimary) return;
        setOpacity(1);
      };

      window.addEventListener("pointerdown", onPointerDown);
      window.addEventListener("pointerup", onPointerUp);

      return () => {
        window.removeEventListener("pointerdown", onPointerDown);
        window.removeEventListener("pointerup", onPointerUp);
        setOpacity(1);
      };
    };

    if (isMobile) {
      return mobileChecker();
    }

    return desktopChecker();
  }, [setOpacity, isPointerOutside, activeButton, sections, isMobile]);

  useEffect(() => {
    const panel = UIManager.instance.getPanel(panelId)!;

    const next = preventOutside(panel.options.screenPosition!, isMobile);

    panel.options.screenPosition!.x = next.x;
    panel.options.screenPosition!.y = next.y;
  }, [panelId, isMobile]);

  const radialContextData = useMemo(() => {
    return {
      setOpacity,
      close,
      closeSubmenu: onClose,
      lock: setIsLocked,
      isPointerOutside,
      getScreenPosition,
    };
  }, [
    close,
    onClose,
    setIsLocked,
    setOpacity,
    isPointerOutside,
    getScreenPosition,
  ]);

  const ANIM_DURATION = 0.2;
  const ANIM_STAGGER = 0.02;
  const ANIM_SCALE = 1.0;

  return (
    <RadialMenuProvider value={radialContextData}>
      <motion.div
        ref={ref}
        className="relative select-none transition-transform touch-none"
        onContextMenu={(e) => e.preventDefault()}
        style={{
          transform: isMobile ? "scale(0.75)" : "scale(1.0)",
        }}
      >
        {segments.map((segment, index) =>
          sections[index].item ? (
            <RadialItemContext.Provider
              key={index}
              value={{
                events: emitters[index],
                angle: segment.middleAngle,
                radius: OUTER_RADIUS,
                isTriggerHovered: activeButton === index,
                close: onClose,
                lock: setIsLocked,
              }}
            >
              <RadialMenuModule
                key={index}
                segment={segment}
                visible={activeButton === index}
                item={sections[index].item}
              />
            </RadialItemContext.Provider>
          ) : null
        )}

        <motion.div
          style={{ opacity }}
          className="transition-opacity duration-500"
        >
          <motion.svg
            key="radial-menu-svg"
            className="absolute pointer-events-none"
            width={size.x}
            height={size.y}
            viewBox={`${-size.x / 2} ${-size.y / 2} ${size.x} ${size.y}`}
            style={{
              left: -size.x / 2,
              top: -size.y / 2,
            }}
            initial="initial"
            animate="animate"
            // exit="exit"
            transition={{
              staggerChildren: ANIM_STAGGER * ANIM_SCALE,
            }}
          >
            <circle
              ref={handleRef}
              cx={0}
              cy={0}
              r={OUTER_RADIUS}
              fill="none"
              stroke="none"
              style={{
                pointerEvents: "fill",
              }}
            />
            {segments.map((segment, index) => (
              <motion.g
                key={index}
                variants={{
                  initial: {
                    opacity: 0,
                    x: -segment.center.x * 0.4,
                    y: -segment.center.y * 0.4,
                  },
                  animate: { opacity: 1, x: 0, y: 0 },
                }}
                transition={{
                  ease: "circOut",
                  duration: ANIM_DURATION * ANIM_SCALE,
                }}
                // tabIndex={0}
              >
                <motion.path
                  d={segment.path}
                  className={
                    "fill-nilo-fill-tertiary hover:fill-nilo-fill-tertiary-hover active:fill-nilo-fill-tertiary-pressed"
                  }
                  style={{
                    pointerEvents: "fill",
                  }}
                  fillRule="evenodd"
                  onPointerEnter={() => {
                    if (!isMobile) setNextActiveButton(index);
                  }}
                  onPointerDown={(e) => e.stopPropagation()}
                  onClick={() => {
                    if (isMobile) setNextActiveButton(index);
                    onButtonClick(index);
                    if (sections[index].onClick) {
                      sections[index].onClick();
                      close();
                    }
                  }}
                />
                <motion.path
                  d={segment.hoverLinePath}
                  className={
                    activeButton === index
                      ? "stroke-nilo-fill-primary"
                      : "stroke-transparent"
                  }
                  strokeWidth={2}
                  fill="none"
                  style={{ zIndex: 1 }}
                />
              </motion.g>
            ))}
          </motion.svg>

          <motion.div
            key="radial-menu-content"
            className="pointer-events-none select-none"
            initial="initial"
            animate="animate"
            // exit="exit"
            transition={{
              staggerChildren: ANIM_STAGGER * ANIM_SCALE,
            }}
          >
            {segments.map((segment, index) => (
              <motion.div
                key={index}
                variants={{
                  initial: {
                    opacity: 0,
                    x: -segment.center.x * 0.4,
                    y: -segment.center.y * 0.4,
                  },
                  animate: {
                    opacity: 1,
                    x: 0,
                    y: 0,
                  },
                  // exit: { opacity: 0, scale: 0.95 },
                }}
                transition={{
                  ease: "circOut",
                  duration: ANIM_DURATION * ANIM_SCALE,
                }}
              >
                <span
                  style={{
                    position: "absolute",
                    left: segment.center.x,
                    top: segment.center.y,
                    width: 0,
                    height: 0,
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  {/* {segment.middleAngle.toFixed(2)} */}
                  <span
                    style={{
                      width: 32,
                      height: 32,
                      fontSize: 32, // Resize any icon
                      display: "flex",
                      justifyContent: "center",
                    }}
                    className={cn(
                      index === activeButton
                        ? "text-nilo-icon-quaternary"
                        : "text-nilo-icon-secondary"
                    )}
                  >
                    {sections[index].icon}
                  </span>

                  <span
                    style={{
                      fontSize: 10,
                      paddingTop: 4,
                      whiteSpace: "nowrap",
                    }}
                    className={cn(
                      "font-semibold",
                      index === activeButton
                        ? "text-nilo-text-quaternary"
                        : "text-nilo-text-secondary"
                    )}
                  >
                    {sections[index].title}
                  </span>

                  <span
                    style={{
                      fontSize: 10,
                      whiteSpace: "nowrap",
                    }}
                    className="font-semibold text-nilo-text-tertiary"
                  >
                    {sections[index].shortcut}
                  </span>
                </span>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </motion.div>
    </RadialMenuProvider>
  );
};
