import { runCommands } from "@/core/command";
import RemoveEntityCommand from "@/core/command/commands/removeEntity";
import SelectEntitiesCommand from "@/core/command/commands/selectEntities";
import { beginTransaction, Transaction } from "@/core/command/transaction";
import { Client } from "@/core/client";

export function deleteEntities(entityIds: string[]) {
  const transaction: Transaction = beginTransaction("entities-delete");

  // Get currently selected entities
  const currentlySelected = Client.userEntity.getSelectedEntities();
  if (currentlySelected.length > 0) {
    const currentlySelectedIds = currentlySelected.map((entity) => entity.id);

    // Check for intersection and filter out entities that are about to be deleted
    const entitiesToKeepSelected = currentlySelectedIds.filter(
      (selectedId) => !entityIds.includes(selectedId)
    );

    if (entitiesToKeepSelected.length < currentlySelected.length) {
      // Update selection to only include entities that aren't being deleted
      runCommands([new SelectEntitiesCommand(entitiesToKeepSelected)]);
    }
  }

  // Delete the specified entities
  for (const entityId of entityIds) {
    runCommands([new RemoveEntityCommand(entityId)]);
  }

  transaction.end();
}
