import { KeyboardMappingID } from "@/config/keyboardMapping";
import { getKeyboardAction } from "@/utils/keyboardShortcuts";

type KeyboardEventType = "keydown" | "keyup" | "keypress";
type ActionOptions = {
  allowDuringTextInputFocus: boolean;
  eventType: KeyboardEventType;
  preventDefault: boolean;
};
const defaultActionOptions: ActionOptions = {
  allowDuringTextInputFocus: false,
  eventType: "keydown",
  preventDefault: true,
};

interface ActionBinding {
  callback: () => unknown;
  options: ActionOptions;
}

/**
 * Keyboard shortcut observer with automatic cleanup and text input awareness.
 *
 * Benefits over document.addEventListener():
 * - Action-based (semantic names vs raw keys)
 * - Ignore during text input on focus (unless explicitly allowed)
 * - Error handling for callbacks
 * - Performance optimized (as opposed to many document.addEventListener calls)
 *
 * Usage:
 * ```ts
 * const observer = new KeyboardEventObserver();
 * const cleanup = observer.registerAction("open_settings", () => {
 *   console.log("Settings opened!");
 * });
 *
 * cleanup(); // Remove listener
 *
 * // Allow browser default behavior
 * observer.registerAction("refresh", () => {
 *   console.log("Refresh triggered!");
 * }, { preventDefault: false });
 * ```
 */
export class KeyboardEventObserver {
  private actionCallbacks = new ActionCallbacksDict();
  private isListening = false;
  private eventHandlers = new Map<
    KeyboardEventType,
    (event: KeyboardEvent) => void
  >();

  // TODO: Consider making this last-one-wins or priority/category based
  // For now, all registered callbacks for an action will be called

  constructor() {
    this.startListening();
  }

  /**
   * Register a callback for a specific keyboard action
   * @param action - The keyboard action ID to listen for
   * @param callback - Function to call when the action is triggered
   * @param options - Options for the listener including text input behavior, event type, and preventDefault behavior
   * @returns Cleanup function to unregister the callback
   */
  registerAction(
    action: KeyboardMappingID,
    callback: () => void,
    options: Partial<ActionOptions> = {}
  ): () => void {
    const resolvedOptions = fillInActionOptionsDefaults(options);
    const eventType = resolvedOptions.eventType;

    const actionCallback = {
      callback,
      options: resolvedOptions,
    };

    this.actionCallbacks.add(action, eventType, actionCallback);

    // Ensure we're listening to the required event type
    this.ensureEventTypeListener(eventType);

    return () => {
      this.actionCallbacks.remove(action, eventType, actionCallback);
      this.cleanupUnusedEventListeners();
    };
  }

  private startListening(): void {
    if (this.isListening) return;
    this.isListening = true;
  }

  private stopListening(): void {
    if (!this.isListening) return;
    this.isListening = false;

    // Remove all event listeners
    this.eventHandlers.forEach((handler, eventType) => {
      document.removeEventListener(eventType, handler);
    });
    this.eventHandlers.clear();
  }

  private ensureEventTypeListener(eventType: KeyboardEventType): void {
    // Only add listener if we don't already have one
    if (this.eventHandlers.has(eventType)) return;

    const handler = (event: KeyboardEvent) => {
      this.handleKeyboardEvent(event, eventType);
    };

    document.addEventListener(eventType, handler);
    this.eventHandlers.set(eventType, handler);
  }

  private cleanupUnusedEventListeners(): void {
    // Remove listeners for unused event types
    for (const [eventType, handler] of this.eventHandlers.entries()) {
      if (!this.actionCallbacks.hasCallbacksForEventType(eventType)) {
        document.removeEventListener(eventType, handler);
        this.eventHandlers.delete(eventType);
      }
    }
  }

  private handleKeyboardEvent = (
    event: KeyboardEvent,
    eventType: KeyboardEventType
  ): void => {
    const action = getKeyboardAction(event);
    if (!action) return;

    const callbacks = this.actionCallbacks.get(action, eventType);
    if (callbacks.length === 0) return;

    const activeElement = document.activeElement;
    const textInputFocused =
      !!activeElement && isElementTextInput(activeElement);

    callbacks.forEach(({ callback, options }) => {
      if (textInputFocused && !options.allowDuringTextInputFocus) {
        return;
      }

      // Prevent default behavior only if explicitly requested
      if (options.preventDefault) {
        event.preventDefault();
        event.stopPropagation();
      }

      executeCallbackSafely(callback, action);
    });
  };

  /**
   * Clean up the observer (mainly for testing)
   */
  destroy(): void {
    this.stopListening();
    this.actionCallbacks.clear();
  }
}

/**
 * Key for the action callbacks dictionary
 */
type ActionCallbacksDictKey = `${KeyboardMappingID}+${KeyboardEventType}`;

/**
 * Small utility class to manage action callbacks with composite keys
 */
class ActionCallbacksDict {
  private readonly callbacksMap = new Map<
    ActionCallbacksDictKey,
    ActionBinding[]
  >();

  add(
    action: KeyboardMappingID,
    eventType: KeyboardEventType,
    callback: ActionBinding
  ): void {
    const key = this.createKey(action, eventType);
    if (!this.callbacksMap.has(key)) {
      this.callbacksMap.set(key, []);
    }
    this.callbacksMap.get(key)!.push(callback);
  }

  remove(
    action: KeyboardMappingID,
    eventType: KeyboardEventType,
    callback: ActionBinding
  ): boolean {
    const key = this.createKey(action, eventType);
    const callbacks = this.callbacksMap.get(key);
    if (!callbacks) return false;

    const index = callbacks.indexOf(callback);
    if (index > -1) {
      callbacks.splice(index, 1);
      if (callbacks.length === 0) {
        this.callbacksMap.delete(key);
      }
      return true;
    }
    return false;
  }

  get(
    action: KeyboardMappingID,
    eventType: KeyboardEventType
  ): ActionBinding[] {
    const key = this.createKey(action, eventType);
    return this.callbacksMap.get(key) || [];
  }

  hasCallbacksForEventType(eventType: KeyboardEventType): boolean {
    for (const key of this.callbacksMap.keys()) {
      if (key.endsWith(`+${eventType}`)) {
        return true;
      }
    }
    return false;
  }

  clear(): void {
    this.callbacksMap.clear();
  }

  private createKey(
    action: KeyboardMappingID,
    eventType: KeyboardEventType
  ): ActionCallbacksDictKey {
    return `${action}+${eventType}`;
  }
}

//// Utility functions

/**
 * Check if an element is a text input element
 */
function isElementTextInput(element: Element): boolean {
  const tagName = element.tagName.toLowerCase();
  const inputType = (element as HTMLInputElement).type?.toLowerCase();

  // Check for text input elements
  if (tagName === "input") {
    return (
      inputType === "text" ||
      inputType === "email" ||
      inputType === "password" ||
      inputType === "search" ||
      inputType === "url" ||
      inputType === "tel" ||
      !inputType
    ); // Default input type is text
  }

  if (tagName === "textarea") {
    return true;
  }

  // Check for contenteditable elements
  if (element.getAttribute("contenteditable") === "true") {
    return true;
  }

  return false;
}

function fillInActionOptionsDefaults(
  options: Partial<ActionOptions>
): ActionOptions {
  return { ...defaultActionOptions, ...options };
}

/**
 * Execute a callback with error handling
 */
function executeCallbackSafely(
  callback: () => unknown,
  action: KeyboardMappingID
): void {
  try {
    callback();
  } catch (error) {
    console.error(`🚨 Error in keyboard action callback for ${action}:`, error);
  }
}
