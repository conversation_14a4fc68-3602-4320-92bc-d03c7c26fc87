import { logger } from "firebase-functions/v2";
import { Liveblocks, LiveblocksError, PlainLsonObject } from "@liveblocks/node";
import { HttpsError } from "firebase-functions/v2/https";
import {
  LiveblocksAuthResponse,
  LiveblocksCloneRoomRequest,
  LiveblocksCreateRoomRequest,
  LiveblocksCreateRoomResponse,
  LiveblocksResponse,
} from "@nilo/firebase-schema";
import { Logger } from "../logger";
import { runWithRetry } from "../utils";
import emptyRoom from "./emptyRoom.json";

const LIVEBLOCKS_SECRET_KEY =
  "sk_dev_sAv5qiwo4ckdopoPgCC5l3Oo7RO_tJ3SGr-QfmVPoS_MJpXOB8DeomD7eAjWp0nQ";
if (!LIVEBLOCKS_SECRET_KEY) {
  throw new Error("LIVEBLOCKS_SECRET_KEY is not set");
}

const liveblocks = new Liveblocks({ secret: LIVEBLOCKS_SECRET_KEY });

export async function authenticate(
  userId: string
): Promise<LiveblocksAuthResponse> {
  const { status, body } = await liveblocks.identifyUser(
    { userId, groupIds: ["main"] },
    { userInfo: {} }
  );

  logger.log("🍁 Liveblocks auth request", userId, status, body);
  if (!body) {
    throw new HttpsError("not-found", "ID token issue");
  }

  const { success, data } = LiveblocksAuthResponse.safeParse(JSON.parse(body));
  if (!success) {
    throw new HttpsError("internal", "Failed to authenticate with Liveblocks");
  }
  return data;
}

export async function createRoom({
  id,
}: LiveblocksCreateRoomRequest): Promise<LiveblocksCreateRoomResponse> {
  try {
    logger.log("🍁 Liveblocks create room request", id);

    try {
      const existingRoom = await liveblocks.getRoom(id);
      if (existingRoom) {
        return {
          status: "ok",
          isNewRoom: false,
          message: "Room already exists",
        };
      }
    } catch (error) {
      if (error instanceof LiveblocksError && error.status === 404) {
        logger.debug("Room does not exist, creating it");
      } else {
        logger.warn("Error checking room existence:", error);
      }
    }

    await liveblocks.createRoom(id, {
      defaultAccesses: ["room:write"],
    });

    await liveblocks.initializeStorageDocument(
      id,
      emptyRoom as PlainLsonObject
    );
    return { status: "ok", isNewRoom: true };
  } catch (error) {
    if (error instanceof LiveblocksError && error.status === 409) {
      return { status: "ok", isNewRoom: false, message: "Room already exists" };
    }
    logger.error("Error creating room:", error);
    throw new HttpsError(
      "internal",
      "Failed to create room",
      processError(error)
    );
  }
}

export async function cloneRoom({
  roomIdFrom,
  roomIdTo,
  inspectStorageDocument,
  logger,
}: LiveblocksCloneRoomRequest & {
  inspectStorageDocument?: (
    storageDocument: PlainLsonObject
  ) => unknown | Promise<unknown>;
  logger?: Logger;
}): Promise<LiveblocksResponse> {
  logger = (logger || Logger.create()).createChild({
    roomIdFrom,
    roomIdTo,
  });
  try {
    //// Get the source room data
    logger.info("📥 Fetching source room data");
    const [sourceRoomData, sourceRoomYjsDoc] = await Promise.all([
      liveblocks.getStorageDocument(roomIdFrom, "plain-lson"),
      liveblocks.getYjsDocumentAsBinaryUpdate(roomIdFrom),
    ]);
    logger.info("Source room data fetched", {
      dataSize: JSON.stringify(sourceRoomData).length,
      dataStructure: Object.keys(sourceRoomData || {}),
      yjsSize: sourceRoomYjsDoc.byteLength,
    });
    if (inspectStorageDocument) {
      await inspectStorageDocument(sourceRoomData);
    }

    //// Create the target room
    logger.info("🏗️ Creating target room");
    const result = await runWithRetry(
      {
        logger,
        retryDelayMs: 5000,
      },
      async () => {
        try {
          await liveblocks.createRoom(roomIdTo, {
            defaultAccesses: ["room:write"],
          });
          return "ok";
        } catch (error) {
          if (error instanceof LiveblocksError && error.status === 409) {
            logger.debug("Room already exists");
            return "already-exists";
          }
          throw error;
        }
      }
    );
    if (result === "already-exists") {
      throw new HttpsError(
        "internal",
        "Failed to clone room",
        "Room already exists"
      );
    }
    logger.info("🏗️ Target room created successfully");

    //// Clone the source room data to the target room
    logger.info("📤 Initializing target room with source data");
    await Promise.all([
      runWithRetry(
        {
          logger,
          retryDelayMs: 5000,
        },
        () => liveblocks.initializeStorageDocument(roomIdTo, sourceRoomData)
      ),
      runWithRetry(
        {
          logger,
          retryDelayMs: 5000,
        },
        () =>
          liveblocks.sendYjsBinaryUpdate(
            roomIdTo,
            new Uint8Array(sourceRoomYjsDoc)
          )
      ),
    ]);
    logger.info("✅ Room data cloned successfully");

    logger.info("✅ Clone operation completed successfully");
    return { status: "ok" };
  } catch (error) {
    if (error instanceof LiveblocksError) {
      logger.warn("Liveblocks error", {
        status: error.status,
        message: error.message,
      });
      if (error.status === 404) {
        logger.debug("Room does not exist");
        throw new HttpsError("not-found", "Room does not exist");
      }
    }
    logger.error("❌ Failed to clone room", {
      error: processError(error),
      errorStack: error instanceof Error ? error.stack : undefined,
    });
    throw new HttpsError(
      "internal",
      "Failed to clone room",
      processError(error)
    );
  }
}

function processError(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  try {
    return JSON.parse(error as string);
  } catch {
    return String(error);
  }
}
