name: "Setup GCP Authentication"
description: "Authenticate with Google Cloud using workload identity"
inputs:
  token_format:
    description: "Token format for the authentication"
    required: false
    default: ""
outputs:
  project_id:
    description: "The Google Cloud project ID used for authentication"
    value: ${{ steps.auth.outputs.project_id }}
  credentials_file_path:
    description: "Path to the generated credentials file (available if create_credentials_file is set to true)"
    value: ${{ steps.auth.outputs.credentials_file_path }}
  auth_token:
    description: "The Google Cloud federated token (for Workload Identity Federation) or self-signed JWT (for Service Account Key JSON)"
    value: ${{ steps.auth.outputs.auth_token }}
  access_token:
    description: "The Google Cloud access token for calling other Google Cloud APIs (available when token_format is set to access_token)"
    value: ${{ steps.auth.outputs.access_token }}
  id_token:
    description: "The Google Cloud ID token (available when token_format is set to id_token)"
    value: ${{ steps.auth.outputs.id_token }}
runs:
  using: "composite"
  steps:
    - name: Authenticate to Google Cloud
      id: auth
      uses: google-github-actions/auth@v2
      with:
        # Note: these are not secrets
        service_account: "<EMAIL>"
        workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github/providers/nilo-technologies-org"
        token_format: ${{ inputs.token_format }}
