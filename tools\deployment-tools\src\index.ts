#!/usr/bin/env node

import { Command } from "commander";
import { confirm } from "@inquirer/prompts";
import {
  getComponentNames,
  getComponentNamesFromDatabaseName,
  isIsolatedEnvironmentStorageBucket,
} from "@nilo/isolated-environments";
import {
  GoogleApiClient,
  objectAgeSeconds,
  RulesService,
} from "./googleApiClient";
import { GitHubApiClient } from "./githubApiClient";

const defaultMaxAgeSeconds = 60 * 60; // 1 hour

function formatAge(ageSeconds: number): string {
  if (ageSeconds < 60) {
    return `${ageSeconds}s`;
  }
  if (ageSeconds < 60 * 60) {
    return `${Math.floor(ageSeconds / 60)}m ${ageSeconds % 60}s`;
  }
  if (ageSeconds < 60 * 60 * 24) {
    return `${Math.floor(ageSeconds / 60 / 60)}h ${Math.floor((ageSeconds % (60 * 60)) / 60)}m`;
  }
  return `${Math.floor(ageSeconds / 60 / 60 / 24)}d ${Math.floor((ageSeconds % (60 * 60 * 24)) / (60 * 60))}h ${Math.floor((ageSeconds % (60 * 60)) / 60)}m ${ageSeconds % 60}s`;
}

const program = new Command();

program
  .name("deployment-tools")
  .description("CLI tools for deployment automation")
  .version("1.0.0");

program
  .command("prune")
  .option("--dry-run", "Do not delete anything")
  .option("--force", "Do not ask for confirmation")
  .option(
    "--databases-and-storage-buckets",
    "Prune unused databases and storage buckets",
    false
  )
  .option("--functions", "Prune unused functions", false)
  .description(
    "Prune Firebase Security Rulesets and Releases of the current project"
  )
  .option("--rules", "Prune unused rulesets and ruleset releases", false)
  .action(async function () {
    const dryRun = this.opts().dryRun;
    const force = this.opts().force;
    if (dryRun) {
      console.warn("Running in dry run mode. No changes will be made.");
      if (force) {
        console.warn("Force mode ignored in dry run mode.");
      }
    }

    const client = new GoogleApiClient();
    await client.initialize();

    type ObjectToDelete = { name: string; ageSeconds: number };
    const prunedCounts: Record<string, number> = {};
    const deleteObjects = async ({
      objectsToDelete,
      objectType,
      deleteFunction,
    }: {
      objectsToDelete: ObjectToDelete[];
      objectType: string;
      deleteFunction: (name: string) => Promise<unknown>;
    }) => {
      if (objectsToDelete.length === 0) {
        console.info(`No ${objectType} to delete`);
        return;
      }
      objectsToDelete.sort((a, b) => a.name.localeCompare(b.name));
      if (dryRun) {
        console.info(
          `Would delete ${objectsToDelete.length} ${objectType}:\n${objectsToDelete.map((r) => `  - ${r.name} (age: ${formatAge(r.ageSeconds)})`).join("\n")}`
        );
      } else {
        console.info(
          `Deleting ${objectsToDelete.length} ${objectType}:\n${objectsToDelete.map((r) => `  - ${r.name} (age: ${formatAge(r.ageSeconds)})`).join("\n")}`
        );
        const answer = force
          ? true
          : await confirm({
              message: `Are you sure you want to delete ${objectsToDelete.length} ${objectType}?`,
              default: false,
            });
        if (answer) {
          for (const { name } of objectsToDelete) {
            console.info(`Deleting ${name}...`);
            await deleteFunction(name);
          }
          prunedCounts[objectType] =
            (prunedCounts[objectType] || 0) + objectsToDelete.length;
        }
      }
    };

    // Prune unused databases and storage buckets
    if (this.opts().databasesAndStorageBuckets) {
      console.info("Pruning unused databases and storage buckets");
      const githubClient = new GitHubApiClient();
      githubClient.setRepository("nilo-technologies", "Nilo");
      // list all pull requests, databases and storage buckets
      const [pullRequests, allDatabases, allStorageBuckets] = await Promise.all(
        [
          githubClient.listOpenPullRequests(),
          client.listFirestoreDatabases(),
          client
            .listStorageBuckets()
            .then((buckets) =>
              buckets.filter(
                (bucket) =>
                  bucket.name && isIsolatedEnvironmentStorageBucket(bucket.name)
              )
            ),
        ]
      );
      // decide which databases and storage buckets are used
      const usedDatabases = new Set<string>();
      const usedStorageBuckets = new Set<string>();
      const branchesToKeep = pullRequests.map(
        (pullRequest) => pullRequest.head.ref
      );
      branchesToKeep.push("main", "prod");
      for (const branch of branchesToKeep) {
        const componentNames = getComponentNames(branch);
        usedDatabases.add(client.getDatabaseName(componentNames.database));
        usedStorageBuckets.add(componentNames.bucket);
      }
      // filter out the databases and storage buckets that are not used
      const databasesToDelete: ObjectToDelete[] = [];
      for (const database of allDatabases) {
        if (database.name && !usedDatabases.has(database.name)) {
          const ageSeconds = objectAgeSeconds(database);
          if (ageSeconds && ageSeconds > defaultMaxAgeSeconds) {
            databasesToDelete.push({ name: database.name, ageSeconds });
          }
        }
      }
      const storageBucketsToDelete: ObjectToDelete[] = [];
      for (const storageBucket of allStorageBuckets) {
        if (storageBucket.name && !usedStorageBuckets.has(storageBucket.name)) {
          const ageSeconds = objectAgeSeconds(storageBucket);
          if (ageSeconds && ageSeconds > defaultMaxAgeSeconds) {
            storageBucketsToDelete.push({
              name: storageBucket.name,
              ageSeconds,
            });
          }
        }
      }
      // delete the databases and storage buckets that are not used
      await deleteObjects({
        objectsToDelete: databasesToDelete,
        objectType: "unused databases",
        deleteFunction: client.deleteDatabase.bind(client),
      });
      await deleteObjects({
        objectsToDelete: storageBucketsToDelete,
        objectType: "unused storage buckets",
        deleteFunction: (name) =>
          client.deleteStorageBucket(name, (objectsDeleted) => {
            console.info(`Deleted ${objectsDeleted} objects`);
          }),
      });
    }

    // Prune orphaned functions
    if (this.opts().functions) {
      console.info("Pruning orphaned functions");
      // list all databases and functions
      const [allDatabases, allFunctions] = await Promise.all([
        client.listFirestoreDatabases(),
        client.listAllFunctions(),
      ]);
      // list all used function groups
      const usedFunctionGroups = new Set<string>();
      usedFunctionGroups.add("auth"); // adding blocking auth functions group (it's special)
      for (const database of allDatabases) {
        const databaseId = database.name?.split("/").pop();
        if (databaseId) {
          usedFunctionGroups.add(
            getComponentNamesFromDatabaseName(databaseId).functions
          );
        } else {
          console.warn(
            `Failed to get database ID from name: ${JSON.stringify(database, null, 2)}`
          );
        }
      }
      // filter out the functions that are not used
      const functionsToDelete: ObjectToDelete[] = [];
      for (const fun of allFunctions) {
        const functionGroup = fun.name?.split("/").pop()?.split("-")[0];
        if (!fun.name || !functionGroup || functionGroup.length === 0) {
          console.warn(
            `Failed to get function group from name: ${JSON.stringify(fun, null, 2)}`
          );
          continue;
        }
        if (!usedFunctionGroups.has(functionGroup)) {
          const ageSeconds = objectAgeSeconds(fun);
          if (ageSeconds && ageSeconds > defaultMaxAgeSeconds) {
            functionsToDelete.push({ name: fun.name, ageSeconds });
          }
        }
      }
      await deleteObjects({
        objectsToDelete: functionsToDelete,
        objectType: "orphaned functions",
        deleteFunction: client.deleteFunction.bind(client),
      });
    }

    // Prune orphaned ruleset releases (releases that for not existing databases or storage buckets)
    if (this.opts().rules) {
      console.info(
        "Pruning orphaned ruleset releases for Firestore databases and storage buckets"
      );
      // list all databases and storage buckets
      const [allDatabases, allStorageBuckets] = await Promise.all([
        client.listFirestoreDatabases(),
        client.listStorageBuckets(),
      ]);
      // list all used release names
      const usedReleaseNames = new Set<string>();
      for (const database of allDatabases) {
        if (database.name) {
          usedReleaseNames.add(
            client.getReleaseName({
              service: RulesService.Firestore,
              databaseName: database.name,
            })
          );
        } else {
          console.warn(
            `Database has no name: ${JSON.stringify(database, null, 2)}`
          );
        }
      }
      for (const storageBucket of allStorageBuckets) {
        if (storageBucket.name) {
          usedReleaseNames.add(
            client.getReleaseName({
              service: RulesService.Storage,
              storageBucket: storageBucket.name,
            })
          );
        } else {
          console.warn(
            `Storage bucket has no name: ${JSON.stringify(storageBucket, null, 2)}`
          );
        }
      }
      // list all releases and filter out the ones that are not used
      const allReleases = await client.listAllReleases();
      const releasesToDelete: ObjectToDelete[] = [];
      for (const release of allReleases) {
        if (release.name && !usedReleaseNames.has(release.name)) {
          const ageSeconds = objectAgeSeconds(release);
          if (ageSeconds && ageSeconds > defaultMaxAgeSeconds) {
            releasesToDelete.push({ name: release.name, ageSeconds });
          }
        }
      }
      // delete the releases that are not used
      await deleteObjects({
        objectsToDelete: releasesToDelete,
        objectType: "orphaned releases",
        deleteFunction: client.deleteRelease.bind(client),
      });
    }

    // Prune orphaned rulesets (rulesets that are not used in any release)
    if (this.opts().rules) {
      console.info("Pruning orphaned rulesets");
      // list all rulesets and releases
      const [allRulesets, allReleases] = await Promise.all([
        client.listAllRulesets(),
        client.listAllReleases(),
      ]);
      // list all used ruleset names
      const usedRulesetNames = new Set<string>();
      for (const release of allReleases) {
        if (release.rulesetName) {
          usedRulesetNames.add(release.rulesetName);
        }
      }
      // filter out rulesets that are not used
      const rulesetsToDelete: ObjectToDelete[] = [];
      for (const ruleset of allRulesets) {
        if (ruleset.name && !usedRulesetNames.has(ruleset.name)) {
          const ageSeconds = objectAgeSeconds(ruleset);
          if (ageSeconds && ageSeconds > defaultMaxAgeSeconds) {
            rulesetsToDelete.push({ name: ruleset.name, ageSeconds });
          }
        }
      }
      // delete the rulesets that are not used
      await deleteObjects({
        objectsToDelete: rulesetsToDelete,
        objectType: "unused rulesets",
        deleteFunction: client.deleteRuleset.bind(client),
      });
    }

    // print statistics
    if (
      Object.values(prunedCounts).reduce((acc, count) => acc + count, 0) > 0
    ) {
      console.info(
        `Pruned ${Object.entries(prunedCounts)
          .map(([objectType, count]) => `${count} ${objectType}`)
          .join(", ")}`
      );
    } else {
      console.info("No objects pruned");
      const opts = this.opts();
      if (
        ![opts.databasesAndStorageBuckets, opts.functions, opts.rules].some(
          (opt) => opt
        )
      ) {
        console.info("Did you forget to pass some options?");
      }
    }
  });

program
  .command("stats")
  .option("-v, --verbose", "Print verbose statistics")
  .description("Print statistics about the current project")
  .action(async function () {
    const verbose = this.opts().verbose;
    const client = new GoogleApiClient();
    await client.initialize();
    // list all databases, storage buckets, rulesets and releases
    const [allDatabases, allStorageBuckets, allRulesets, allReleases] =
      await Promise.all([
        client.listFirestoreDatabases(),
        client
          .listStorageBuckets()
          .then((buckets) =>
            buckets.filter(
              (bucket) =>
                bucket.name && isIsolatedEnvironmentStorageBucket(bucket.name)
            )
          ),
        client.listAllRulesets(),
        client.listAllReleases(),
      ]);
    // check what's used
    const usedRulesServices: Parameters<typeof client.getReleaseName>[0][] = [];
    for (const database of allDatabases) {
      if (database.name) {
        usedRulesServices.push({
          service: RulesService.Firestore,
          databaseName: database.name,
        });
      }
    }
    for (const storageBucket of allStorageBuckets) {
      if (storageBucket.name) {
        usedRulesServices.push({
          service: RulesService.Storage,
          storageBucket: storageBucket.name,
        });
      }
    }
    const usedReleaseNames = new Set<string>(
      usedRulesServices.map((serviceDesc) => client.getReleaseName(serviceDesc))
    );
    const usedRulesetNames = new Map<string, string>();
    for (const release of allReleases) {
      if (
        release.name &&
        usedReleaseNames.has(release.name) &&
        release.rulesetName
      ) {
        usedRulesetNames.set(release.rulesetName, release.name);
      }
    }
    // print statistics
    if (verbose) {
      console.info(
        `Databases (${allDatabases.length} total):\n${Array.from(allDatabases.map((database) => `  - ${database.name}`)).join("\n")}`
      );
    } else {
      console.info(`Databases: ${allDatabases.length}`);
    }
    if (verbose) {
      console.info(
        `Storage buckets (${allStorageBuckets.length} total):\n${Array.from(allStorageBuckets.map((storageBucket) => `  - ${storageBucket.name}`)).join("\n")}`
      );
    } else {
      console.info(`Storage buckets: ${allStorageBuckets.length}`);
    }

    if (verbose) {
      console.info(
        `Rulesets (${usedRulesetNames.size} used(+) + ${allRulesets.length - usedRulesetNames.size} unused(-)):\n${Array.from(
          allRulesets.map((ruleset) => {
            const releaseName =
              ruleset.name && usedRulesetNames.get(ruleset.name);
            if (releaseName) {
              return `  + ${ruleset.name} (used in ${releaseName})`;
            }
            return `  - ${ruleset.name} (unused)`;
          })
        ).join("\n")}`
      );
    } else {
      console.info(
        `Rulesets: ${allRulesets.length} (${allRulesets.length - usedRulesetNames.size} unused)`
      );
    }

    if (verbose) {
      console.info(
        `Releases (${usedReleaseNames.size} used(+) + ${allReleases.length - usedReleaseNames.size} unused(-)):\n${Array.from(allReleases.map((release) => `  ${release.name && usedReleaseNames.has(release.name) ? "+" : "-"} ${release.name}`)).join("\n")}`
      );
    } else {
      console.info(
        `Releases: ${allReleases.length} (${allReleases.length - usedReleaseNames.size} unused)`
      );
    }
  });

program.parse();
