# Radial Menu

Here you can find information about the Radial Menu component, its usage, and examples.

## Structure

The Radial Menu is structured into several key components:

1. **Builder** `/builder/index.ts`: The main entry point when constructing the radial menu before it is rendered.
2. **Global Builder** `builder/globalBuilder.tsx`: A builder for global actions and options that can be accessed from a radial menu when nothing is selected.
3. **Selection Builder** `builder/selectionBuilder.tsx`: A builder for actions and options related to the currently selected entities in the radial menu.

Global/Selection builders are returning a `RadialMenu` React component which renders the constructed menu.

It accepts an array of menu segments to render.

## Segment

A segment represents a section of the radial menu that contains an icon, title, and an interactive item. Each segment can have the following properties:

- `icon`: React node to display as the segment icon
- `title`: String title for the segment
- `shortcut`: Optional keyboard shortcut display
- `onClick`: Optional click handler (for simple actions)
- `item`: Interactive radial component (buttons, slider, switch, etc.)
- `reduceMenuOpacity`: Optional boolean to reduce menu opacity
- `menuOpacityOutside`: Optional opacity value for area when pointer is outside of the menu

## Radial Components

The radial menu system provides several interactive components that can be used within segments:

### `radial.buttons(options)`

Creates a customizable button array with multiple layers arranged in a radial pattern.

**Parameters:**

- `layers: number[]` - Array specifying number of buttons per layer
- `Component: FC<RadialButtonProps>` - Custom component to render for each button
- `offset: number` - Distance from menu center to first layer
- `angleOffset?: number` - Angular offset for button positioning
- `layersGap: number` - Gap between concentric layers
- `buttonsGap: number` - Gap between buttons within a layer
- `buttonSize: number` - Size of each button
- `closeOnClick?: boolean` - Whether to close menu when button is clicked
- `onUnmount?: () => void` - Cleanup function when component unmounts
- `onMobileInteractionFinish?: (e: PointerEvent) => void` - Mobile interaction handler
- `tooltip?: (props: RadialButtonProps) => string | null` - Dynamic tooltip function

**Example:**

```tsx
item: radial.buttons({
  layers: [6, 8], // 6 buttons in first layer, 8 in second
  offset: 20,
  angleOffset: 100,
  layersGap: 12,
  buttonsGap: 12,
  buttonSize: 72,
  closeOnClick: true,
  Component: ({ itemIndex }) => (
    <div className="w-full h-full bg-blue-500 rounded-full flex items-center justify-center">
      {itemIndex + 1}
    </div>
  ),
  tooltip: ({ itemIndex }) => `Button ${itemIndex + 1}`,
});
```

### `radial.buttonsSet(options)`

Creates a simplified button set using predefined button items. Ideal for simple icon-based menus.

**Parameters:**

- `layers: ButtonsItem[][]` - 2D array of button items per layer
- `closeOnClick?: boolean` - Whether to close menu when button is clicked
- `onUnmount?: () => void` - Cleanup function when component unmounts
- `onMobileInteractionFinish?: (e: PointerEvent) => void` - Mobile interaction handler

**ButtonsItem Interface:**

```tsx
interface ButtonsItem {
  icon: ReactNode; // Icon to display
  tooltip?: string; // Optional tooltip text
  // ...DOM attributes (onClick, onPointerEnter, etc.)
}
```

**Example:**

```tsx
const buttonItems = [
  { icon: <PlayIcon />, tooltip: "Play", onClick: () => play() },
  { icon: <PauseIcon />, tooltip: "Pause", onClick: () => pause() },
  { icon: <StopIcon />, tooltip: "Stop", onClick: () => stop() },
];

item: radial.buttonsSet({
  layers: [buttonItems],
  closeOnClick: true,
});
```

### `radial.slider(options)`

Creates a radial slider for value selection with visual feedback.

**Parameters:**

- `angle: number` - Arc angle of the slider in degrees
- `min: number` - Minimum slider value
- `max: number` - Maximum slider value
- `value: number` - Current slider value
- `step?: number` - Step increment for value changes
- `stepsVisible?: boolean` - Whether to show step indicators
- `onChange: (value: number) => void` - Callback when value changes

**Example:**

```tsx
item: radial.slider({
  angle: 70,
  min: 0,
  max: 5,
  value: 2,
  step: 1,
  stepsVisible: true,
  onChange: (value) => {
    console.log("Slider value:", value);
    updateSetting(value);
  },
});
```

### `radial.switch(options)`

Creates a toggle switch component.

**Parameters:**

- `offset: number` - Distance from menu center
- `angle: number` - Arc angle of the switch in degrees
- `size: number` - Size of the switch component
- `value: boolean` - Current switch state
- `onChange: (value: boolean) => void` - Callback when state changes

**Example:**

```tsx
item: radial.switch({
  offset: 15,
  angle: 45,
  size: 30,
  value: isEnabled,
  onChange: (enabled) => {
    setIsEnabled(enabled);
    toggleFeature(enabled);
  },
});
```

## Builder Examples

### Global Builder

The global builder creates menus for actions available when no entities are selected:

```tsx
const buildPrimitives = (): RadialSection => {
  return {
    icon: <PrimitiveIcon />,
    title: "Create Primitive",
    shortcut: buildShortcut([CMD, "F"]),
    reduceMenuOpacity: true,
    menuOpacityOutside: 0.25,
    item: radial.buttons({
      layers: [primitives.length],
      closeOnClick: true,
      Component: ({ itemIndex }) => (
        <img
          src={primitives[itemIndex].icon}
          alt={`${primitives[itemIndex].type} Icon`}
          draggable="false"
        />
      ),
      offset: 20,
      angleOffset: 100,
      layersGap: 12,
      buttonsGap: 12,
      buttonSize: 72,
    }),
  };
};
```

### Selection Builder

The selection builder creates context-sensitive menus based on selected entities:

```tsx
const buildLOD = (selected: GameEntity[]): RadialSection | null => {
  return {
    icon: <PrimitiveIcon />,
    title: "LOD",
    reduceMenuOpacity: true,
    menuOpacityOutside: 0.25,
    item: radial.slider({
      angle: 70,
      min: 0,
      max: PRESETS.length - 1,
      value: PRESETS.length - 1,
      step: 1,
      stepsVisible: true,
      onChange: (value) => {
        selectedEntitiesDetailCommit(PRESETS[value]);
      },
    }),
  };
};
```

## Usage

To use the radial menu system:

1. Import the builder functions:

```tsx
import { buildRadialMenu } from "./builder";
```

2. Call the builder to get a RadialMenu component:

```tsx
const menu = buildRadialMenu();
// Returns: <RadialMenu sections={state} />
```

3. Render the menu in your component:

```tsx
return <div>{menu}</div>;
```

## Best Practices

- Use `radial.buttonsSet` for simple icon-based menus with consistent styling
- Use `radial.buttons` when you need custom components or complex interactions
- Use `radial.slider` for continuous value selection with visual feedback
- Use `radial.switch` for binary on/off toggles
- Always provide meaningful tooltips for accessibility
- Set `closeOnClick: true` for actions that should dismiss the menu
- Use `reduceMenuOpacity` and `menuOpacityOutside` for focus-intensive interactions
- Implement proper cleanup in `onUnmount` for preview states or subscriptions
