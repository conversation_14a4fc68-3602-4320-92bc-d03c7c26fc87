{"name": "vercel-deployment", "version": "1.0.0", "main": "dist/index.js", "bin": {"vercel-deployment": "./dist/index.js"}, "scripts": {"build": "tsup", "build:watch": "tsup --watch", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "deploy": "node dist/index.js set-tag-and-redeploy"}, "keywords": [], "author": "", "license": "ISC", "description": "Vercel deployment utilities for Nilo", "dependencies": {"@vercel/sdk": "^1.8.0", "commander": "^14.0.0", "dotenv": "^16.4.7"}, "devDependencies": {"@types/node": "^24.5.2", "ts-node": "^10.9.2", "tsup": "^8.5.0", "typescript": "^5.9.2"}}