import { Quaternion, QuaternionLike, Vector3, Vector3<PERSON>ike } from "three";

export const defaultCharacterSettings: CharacterPhysicsSettings = {
  characterHeight: 0.5,
  characterRadius: 0.2,
  initialPosition: new Vector3(),
  initialOrientation: new Quaternion(),
};

export interface CharacterPhysicsSettings {
  initialPosition: Vector3Like;
  initialOrientation: QuaternionLike;
  characterHeight: number;
  characterRadius: number;
}
