import { CharacterState } from "../CharacterState";
import { CharacterContext } from "../CharacterContext";
import { CharacterStates } from "../CharacterStateMachine";
import { EntityData } from "@nilo/ecs";
import { RagdollState as PhysicsRagdollState } from "@/core/animator/ragdoll/MeshRagdoll";
import { AnimatorComponent } from "@/core/components";

export class DeathState extends CharacterState {
  constructor(context: CharacterContext, states: CharacterStates, id: number) {
    super(context, "DeathState", id, states);
  }

  override onEnter(entityData: EntityData) {
    super.onEnter(entityData);

    const animatorComponent = entityData.getComponent(AnimatorComponent);
    const ragdoll = animatorComponent?.getRagdoll();

    animatorComponent?.disable();

    if (ragdoll) {
      ragdoll.setRagdollState(PhysicsRagdollState.Ragdoll);
      this.context.character?.disableCharacterControl();
    }
  }

  override onExit(entityData: EntityData) {
    super.onExit(entityData);

    const animatorComponent = entityData.getComponent(AnimatorComponent);
    const ragdoll = animatorComponent?.getRagdoll();

    animatorComponent?.enable();

    if (ragdoll) {
      ragdoll.setRagdollState(PhysicsRagdollState.Kinematic);
    }
  }
}
