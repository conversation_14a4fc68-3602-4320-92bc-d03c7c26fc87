import { getGlobalEventTrackingContext } from "@/providers/event-tracking-provider";
import { getBrowserName, getDeviceType } from "@/core/util/UserAgent";

/**
 * Centralized event tracking utilities
 * These functions can be used anywhere in the codebase, both in React and non-React contexts
 */

/**
 * Track an event with optional properties
 * @param eventName - The name of the event to track
 * @param properties - Optional properties to include with the event
 */
export function trackEvent(
  eventName: string,
  properties: Record<string, unknown> = {}
): void {
  const context = getGlobalEventTrackingContext();

  if (!context) {
    console.warn(
      "📊 Event Tracking: Context not available, skipping event:",
      eventName
    );
    return;
  }

  context.trackEvent(eventName, properties);
}

/**
 * Identify a user with optional properties
 * @param userId - The user ID to identify
 * @param properties - Optional user properties
 */
export function identifyUser(
  userId: string,
  properties: Record<string, unknown> = {}
): void {
  const context = getGlobalEventTrackingContext();

  if (!context) {
    console.warn("📊 Event Tracking: Context not available, skipping identify");
    return;
  }

  context.identify(userId, properties);
}

/**
 * Set user properties
 * @param properties - User properties to set
 */
export function setUserProperties(properties: Record<string, unknown>): void {
  const context = getGlobalEventTrackingContext();

  if (!context) {
    console.warn(
      "📊 Event Tracking: Context not available, skipping user properties"
    );
    return;
  }

  context.setUserProperties(properties);
}

/**
 * Create an alias for the current user
 * @param alias - The alias to create
 */
export function aliasUser(alias: string): void {
  const context = getGlobalEventTrackingContext();

  if (!context) {
    console.warn("📊 Event Tracking: Context not available, skipping alias");
    return;
  }

  context.alias(alias);
}

/**
 * Reset the current user session
 */
export function resetTracking(): void {
  const context = getGlobalEventTrackingContext();

  if (!context) {
    console.warn("📊 Event Tracking: Context not available, skipping reset");
    return;
  }

  context.reset();
}

/**
 * Check if event tracking is initialized and available
 */
export function isTrackingInitialized(): boolean {
  const context = getGlobalEventTrackingContext();
  return context?.isInitialized ?? false;
}

/**
 * Get the PostHog client instance (for advanced usage)
 */
export function getPostHogClient() {
  const context = getGlobalEventTrackingContext();
  return context?.posthogClient ?? null;
}

// Convenience functions for common event patterns

/**
 * Track a page view event
 * @param pageName - The name/path of the page
 * @param properties - Optional additional properties
 */
export function trackPageView(
  pageName: string,
  properties: Record<string, unknown> = {}
): void {
  trackEvent("page_view", {
    page_name: pageName,
    url: window.location.href,
    path: window.location.pathname,
    ...properties,
  });
}

/**
 * Track a user action event
 * @param action - The action performed
 * @param target - The target of the action (e.g., button name, element ID)
 * @param properties - Optional additional properties
 */
export function trackUserAction(
  action: string,
  target?: string,
  properties: Record<string, unknown> = {}
): void {
  trackEvent("user_action", {
    action,
    target,
    ...properties,
  });
}

/**
 * Track an error event
 * @param error - The error that occurred
 * @param context - Additional context about where the error occurred
 * @param properties - Optional additional properties
 */
export function trackError(
  error: Error | string,
  context?: string,
  properties: Record<string, unknown> = {}
): void {
  const errorMessage = error instanceof Error ? error.message : error;
  const errorStack = error instanceof Error ? error.stack : undefined;

  trackEvent("error", {
    error_message: errorMessage,
    error_stack: errorStack,
    error_context: context,
    ...properties,
  });
}

/**
 * Track a performance event
 * @param metric - The performance metric name
 * @param value - The metric value
 * @param unit - The unit of measurement (e.g., 'ms', 'bytes', 'count')
 * @param properties - Optional additional properties
 */
export function trackPerformance(
  metric: string,
  value: number,
  unit?: string,
  properties: Record<string, unknown> = {}
): void {
  trackEvent("performance", {
    metric,
    value,
    unit,
    ...properties,
  });
}

/**
 * Track a feature usage event
 * @param feature - The feature name
 * @param action - The action performed with the feature
 * @param properties - Optional additional properties
 */
export function trackFeatureUsage(
  feature: string,
  action: string,
  properties: Record<string, unknown> = {}
): void {
  trackEvent("feature_usage", {
    feature,
    action,
    ...properties,
  });
}

/**
 * Track a session event (start, end, etc.)
 * @param sessionAction - The session action (start, end, pause, resume)
 * @param sessionId - The session ID
 * @param properties - Optional additional properties
 */
export function trackSession(
  sessionAction: string,
  sessionId: string,
  properties: Record<string, unknown> = {}
): void {
  trackEvent(`session_${sessionAction}`, {
    session_id: sessionId,
    timestamp: new Date().toISOString(),
    device_type: getDeviceType(),
    browser: getBrowserName(),
    referrer_url: document.referrer || undefined,
    ...properties,
  });
}
