import { expect } from "@playwright/test";
import {
  getEntitiesCount,
  getSceneChildrenCount,
  roomTest,
  TestWindow,
  poll,
} from "./utils";

roomTest("generate a 3D model from text via API call", async ({ page }) => {
  // Get initial scene children count
  const initialChildrenCount = await page.evaluate(getSceneChildrenCount);
  const initialEntitiesCount = await page.evaluate(getEntitiesCount);

  //wait for physics to start, in other case adding MeshEntity does not work
  await poll(page, () => (window as unknown as TestWindow).Client.physics, {
    timeout: 30_000,
  }).toBeTruthy();

  await page.evaluate(() => {
    const w = window as unknown as TestWindow;

    w.Client.userEntity.addPromptEntity(new w.Vector3(-2, 0, 0), false);
  });

  await page.keyboard.press("a");
  await page.keyboard.press("p");
  await page.keyboard.press("p");
  await page.keyboard.press("l");
  await page.keyboard.press("e");
  await page.keyboard.press("Enter");

  await poll(page, getSceneChildrenCount, {
    timeout: 40_000,
  })
    // +2 means here +1 for entity and +1 since TransformControls appeared after newly create entity was selected
    .toBe(initialChildrenCount + 2);

  // Check if a new child was added to the scene
  let entitiesCount = await page.evaluate(getEntitiesCount);

  expect(entitiesCount).toBe(initialEntitiesCount + 1);

  await page.keyboard.down("Control");
  await page.keyboard.press("KeyZ");
  await page.keyboard.up("Control");

  // Check if a new child was added to the scene
  let childrenCount = await page.evaluate(getSceneChildrenCount);
  entitiesCount = await page.evaluate(getEntitiesCount);

  expect(childrenCount).toBe(initialChildrenCount);
  expect(entitiesCount).toBe(initialEntitiesCount);

  await page.keyboard.down("Control");
  await page.keyboard.down("Shift");
  await page.keyboard.press("KeyZ");
  await page.keyboard.up("Shift");
  await page.keyboard.up("Control");

  // Check if a new child was added to the scene
  childrenCount = await page.evaluate(getSceneChildrenCount);
  entitiesCount = await page.evaluate(getEntitiesCount);

  expect(childrenCount).toBe(initialChildrenCount + 2);
  expect(entitiesCount).toBe(initialEntitiesCount + 1);

  console.info("✅ Model generation from text with undo/redo works");
});
