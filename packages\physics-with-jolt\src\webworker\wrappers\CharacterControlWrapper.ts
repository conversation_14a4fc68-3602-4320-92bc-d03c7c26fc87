import * as Comlink from "comlink";
import { Quaternion, QuaternionLike, Vector3, Vector3Like } from "three";
import {
  Character<PERSON>ontrolApi,
  CharacterControlData,
} from "../../types/webworker";
import { CharacterPhysicsSettings } from "../../types/character";
import { GenericWrapper } from "./GenericWrapper";

export class CharacterControlWrapper extends GenericWrapper {
  private _isSupported: boolean = false;
  private _id: number;
  private _position: Vector3;
  private _rotation: Quaternion;
  private _velocity: Vector3;

  public get id(): number {
    return this._id;
  }

  constructor(
    public readonly characterControlApi: Comlink.Remote<CharacterControlApi>,
    private readonly characterSettings: Partial<CharacterPhysicsSettings>,
    private readonly characterData: CharacterControlData
  ) {
    super(characterControlApi);
    this._position = new Vector3();
    this._rotation = new Quaternion();
    this._velocity = new Vector3();
    this._id = characterData.id;
    this.fromJSON(characterData);
  }

  public setLinearVelocity(velocity: Vector3Like): void {
    this.characterControlApi.setLinearVelocity(velocity);
  }

  public setPosition(position: Vector3Like): void {
    this._position.copy(position);
    this.characterControlApi.setPosition(position);
  }

  public getPosition(): Vector3Like {
    return this._position;
  }

  public getVelocity(): Vector3Like {
    return this._velocity;
  }
  public getRotation(): QuaternionLike {
    return this._rotation;
  }

  public isSupported(): boolean {
    return this._isSupported;
  }

  public disable(): void {
    this.characterControlApi.disable();
  }
  public async enable(): Promise<void> {
    this.characterControlApi.enable();
  }

  public fromJSON(characterData: CharacterControlData): void {
    this._position.copy(characterData.position);
    this._rotation.copy(characterData.rotation);
    this._velocity.copy(characterData.velocity);
    this._isSupported = characterData.isSupported;
    this._id = characterData.id;
  }
}
