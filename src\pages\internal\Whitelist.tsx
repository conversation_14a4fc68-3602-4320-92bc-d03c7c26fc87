import { useState, FormEvent } from "react";
import {
  collection,
  CollectionReference,
  doc,
  setDoc,
  updateDoc,
  deleteDoc,
  QueryDocumentSnapshot,
} from "firebase/firestore";
import { db } from "@/config/firebase";
import { DbCollections, DbWhitelistedEmails } from "@nilo/firebase-schema";
import { useFirebaseQuery } from "@/hooks/firebaseHooks";
import { Typography } from "@/components/ui-nilo/Typography";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface WhitelistRowProps {
  emailDoc: QueryDocumentSnapshot<DbWhitelistedEmails>;
  onToggleAllow: (email: string, newAllow: boolean) => void;
  onDeleteEmail: (email: string) => void;
}

const WhitelistRow = ({
  emailDoc,
  onToggleAllow,
  onDeleteEmail,
}: WhitelistRowProps) => {
  const data = emailDoc.data();
  const email = emailDoc.id;
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleToggle = (checked: boolean) => {
    onToggleAllow(email, checked);
  };

  const handleDelete = () => {
    onDeleteEmail(email);
    setIsDeleteDialogOpen(false);
  };

  return (
    <TableRow className="hover:bg-gray-50/5">
      <TableCell className="font-medium">
        <Badge
          className={
            data.allow ? "bg-green-500 text-white" : "bg-red-500 text-white"
          }
        >
          {data.allow ? "Allowed" : "Blocked"}
        </Badge>
        <span className="text-brand ml-2">{email}</span>
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-2">
          <Switch checked={data.allow} onCheckedChange={handleToggle} />
        </div>
      </TableCell>
      <TableCell className="text-right">
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="ghost" size="sm" className="p-1 h-8 w-8">
              ×
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Confirm delete</DialogTitle>
            </DialogHeader>
            <DialogDescription>
              Are you sure you want to delete <strong>{email}</strong> from the
              whitelist? This action cannot be undone.
            </DialogDescription>
            <DialogFooter>
              <Button
                variant="ghost"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDelete}>
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </TableCell>
    </TableRow>
  );
};

interface AddEmailFormProps {
  onAddEmail: (email: string) => void;
  existingEmails: string[];
}

const AddEmailForm = ({ onAddEmail, existingEmails }: AddEmailFormProps) => {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!email.trim()) return;

    const normalizedEmail = email.trim().toLowerCase();

    if (existingEmails.includes(normalizedEmail)) {
      alert(`${normalizedEmail} is already listed`);
      return;
    }

    setIsSubmitting(true);
    setError("");
    try {
      onAddEmail(normalizedEmail);
      setEmail("");
    } catch (error) {
      console.error("Error adding email:", error);
      setError("Failed to add email. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const isEmailDuplicate = (email: string) => {
    return existingEmails.includes(email.trim().toLowerCase());
  };

  return (
    <div className="space-y-2">
      <form onSubmit={handleSubmit} className="flex gap-2">
        <Input
          type="email"
          placeholder="Enter email address"
          value={email}
          onChange={(e) => {
            setEmail(e.target.value);
            setError("");
          }}
          className="flex-1"
          disabled={isSubmitting}
        />
        <Button
          type="submit"
          disabled={
            !email.trim() ||
            !isValidEmail(email) ||
            isEmailDuplicate(email) ||
            isSubmitting
          }
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          {isSubmitting ? "Adding..." : "Add Email"}
        </Button>
      </form>
      {error && <p className="text-red-500 text-sm">{error}</p>}
    </div>
  );
};

export default function Whitelist() {
  const emailsCollection = collection(
    db,
    DbCollections.whitelistedEmails
  ) as CollectionReference<DbWhitelistedEmails, DbWhitelistedEmails>;

  const emails = useFirebaseQuery(emailsCollection);

  const handleToggleAllow = async (email: string, newAllow: boolean) => {
    try {
      const emailDoc = doc(emailsCollection, email);
      await updateDoc(emailDoc, { allow: newAllow });
      console.info("Email updated:", email, newAllow);
    } catch (error) {
      console.error("Error updating email:", error);
    }
  };

  const handleAddEmail = async (email: string) => {
    try {
      const emailDoc = doc(emailsCollection, email);
      await setDoc(emailDoc, { allow: true });
    } catch (error) {
      console.error("Error adding email:", error);
    }
  };

  const handleDeleteEmail = async (email: string) => {
    try {
      const emailDoc = doc(emailsCollection, email);
      await deleteDoc(emailDoc);
      console.info("Email deleted:", email);
    } catch (error) {
      console.error("Error deleting email:", error);
    }
  };

  // Extract email addresses for duplicate checking
  const existingEmails = emails.map((emailDoc) => emailDoc.id);

  return (
    <div className="h-screen bg-black text-icon-primary overflow-y-auto scrollbar-thin-nilo">
      <div className="mx-auto space-y-12 pt-8 pb-24 max-w-6xl">
        <header className="text-center">
          <Typography.Title level={1} color="light" className="mb-4">
            Emails Whitelisting 🚪
          </Typography.Title>
        </header>
        <div className="bg-surface-primary rounded-lg border border-accessories-divider p-6">
          <AddEmailForm
            onAddEmail={handleAddEmail}
            existingEmails={existingEmails}
          />
        </div>
        <div className="bg-surface-primary rounded-lg border border-accessories-divider overflow-x-auto">
          <Table className="min-w-full">
            <TableHeader>
              <TableRow>
                <TableHead className="w-1/2">
                  Email Address (total of {emails.length})
                </TableHead>
                <TableHead className="w-1/3">Status</TableHead>
                <TableHead className="w-1/6 text-right"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {emails.map((emailDoc) => (
                <WhitelistRow
                  key={emailDoc.id}
                  emailDoc={emailDoc}
                  onToggleAllow={handleToggleAllow}
                  onDeleteEmail={handleDeleteEmail}
                />
              ))}
              {emails.length === 0 && (
                <TableRow>
                  <TableCell
                    colSpan={3}
                    className="text-center text-muted-foreground py-8"
                  >
                    No whitelisted emails found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
