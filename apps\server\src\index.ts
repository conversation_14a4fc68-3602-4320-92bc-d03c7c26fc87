import { getLogger } from "@nilo/logger";
import { defaultPort } from "@nilo/network";
import { NetworkSystem } from "./NetworkSystem";
import {
  generateWTCertificates,
  CertificateResponse,
  loadCertificateChain,
} from "./certs";
import { initAgones } from "./agones";

const logger = getLogger();

const isSecure = process.env.UNSAFE !== "true";
const tlsCertPath = process.env.TLS_CERT_FILE;
const tlsKeyPath = process.env.TLS_KEY_FILE;
const skipAgones = process.env.SKIP_AGONES === "true";

async function init() {
  let certificate: CertificateResponse;
  if (isSecure) {
    if (!tlsCertPath || !tlsKeyPath) {
      throw new Error("Missing TLS certificate files!");
    }

    certificate = await loadCertificateChain(tlsCertPath, tlsKeyPath);
  } else {
    certificate = await generateWTCertificates();
    logger.info("Generated certificate", {
      fingerprint: certificate.fingerprint,
    });
    logger.warn("Running in unsafe mode");
  }

  const httpPort = Number(
    process.env.HTTP_PORT || process.env.PORT || defaultPort
  );
  const wtPort = Number(process.env.WT_PORT || defaultPort);

  const ns = new NetworkSystem({
    wtOptions: {
      port: wtPort,
      host: "0.0.0.0",
      certificate,
    },
    wsOptions: isSecure
      ? {
          port: httpPort,
          host: "0.0.0.0",
          certificate,
        }
      : {
          port: httpPort,
          host: "0.0.0.0",
        },
  });

  const app = ns.express;
  const fingerprint = certificate.fingerprint;

  app.get("/fingerprint.json", (_req, res) => {
    // You can also set using the following method
    res.setHeader(
      "Access-Control-Allow-Origin",
      "*"
    ); /* @dev First, read about security */
    res.setHeader("Access-Control-Allow-Methods", "OPTIONS, GET");
    res.setHeader("Access-Control-Max-Age", 2592000); // 30 days
    res.setHeader("Access-Control-Allow-Headers", "content-type"); // Might be helpful

    res.setHeader("Content-Type", "application/json");
    res.end(JSON.stringify({ fingerprint }));
  });

  app.get("/ping", (_req, res) => {
    res.send("OK");
  });

  ns.start();

  if (!skipAgones) {
    await initAgones();
  }
}

init();
