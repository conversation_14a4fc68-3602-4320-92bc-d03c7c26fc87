#!/usr/bin/env node

import { config } from "dotenv";
import { Command } from "commander";
import { setEnvAndRedeploy } from "./setEnvAndRedeploy";
import { listProjects } from "./listProjects";

// Load environment variables from .env.local and .env files
config({ path: ".env.local" });
config();

const program = new Command();

program
  .name("vercel-deployment")
  .description("Vercel deployment utilities for Nilo")
  .version("1.0.0");

program
  .command("set-tag-and-redeploy")
  .description("Set NILO_GIT_TAG environment variable and trigger a redeploy")
  .requiredOption("--tag <tag>", "Git tag to set as NILO_GIT_TAG")
  .option(
    "--project-id <projectId>",
    "Vercel project ID (defaults to VERCEL_PROJECT_ID env var)"
  )
  .option(
    "--token <token>",
    "Vercel authentication token (defaults to VERCEL_TOKEN env var)"
  )
  .option(
    "--repo-id <repoId>",
    "GitHub repository ID (defaults to GITHUB_REPOSITORY_ID env var)"
  )
  .action(
    async (options: {
      tag: string;
      projectId?: string;
      token?: string;
      repoId?: string;
    }) => {
      try {
        // Get values from options or environment variables
        const projectId = options.projectId || process.env.VERCEL_PROJECT_ID;
        const token = options.token || process.env.VERCEL_TOKEN;
        const repoId = options.repoId || process.env.GITHUB_REPOSITORY_ID;

        // Validate that all required values are present
        if (!projectId) {
          throw new Error(
            "Project ID is required. Provide --project-id or set VERCEL_PROJECT_ID environment variable."
          );
        }
        if (!token) {
          throw new Error(
            "Vercel token is required. Provide --token or set VERCEL_TOKEN environment variable."
          );
        }
        if (!repoId) {
          throw new Error(
            "Repository ID is required. Provide --repo-id or set GITHUB_REPOSITORY_ID environment variable."
          );
        }

        await setEnvAndRedeploy({
          tag: options.tag,
          projectId,
          token,
          repoId,
        });
      } catch (error) {
        console.error(
          "❌ Error:",
          error instanceof Error ? error.message : String(error)
        );
        process.exit(1);
      }
    }
  );

program
  .command("list-projects")
  .description("List all your Vercel projects to find the correct project ID")
  .option(
    "--token <token>",
    "Vercel authentication token (defaults to VERCEL_TOKEN env var)"
  )
  .action(async (options: { token?: string }) => {
    try {
      const token = options.token || process.env.VERCEL_TOKEN;

      if (!token) {
        throw new Error(
          "Vercel token is required. Provide --token or set VERCEL_TOKEN environment variable."
        );
      }

      await listProjects(token);
    } catch (error) {
      console.error(
        "❌ Error:",
        error instanceof Error ? error.message : String(error)
      );
      process.exit(1);
    }
  });

program.parse();
