#!/bin/bash

# Release Workflow Test Script
# Tests the GitHub workflow steps locally with real git operations
# Usage: ./scripts/release-workflow.sh <release-name> [--cleanup]

set -e  # Exit on any error

# Set environment variables to prevent interactive git sessions
export GIT_EDITOR="true"
export GIT_MERGE_AUTOEDIT="no"
export GIT_PAGER="cat"
export GIT_TERMINAL_PROMPT=0

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}📋 Step $1: $2${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

print_help() {
    echo "Release Workflow Test Script"
    echo "============================"
    echo ""
    echo "This script tests the GitHub release workflow locally using real git operations."
    echo "It creates a release branch, reverts commits, merges main, and optionally cleans up."
    echo ""
    echo "Usage:"
    echo "  $0 <release-name> [--cleanup]"
    echo ""
    echo "Parameters:"
    echo "  <release-name>  Name for the release branch (will create release/<name>)"
    echo "  --cleanup       Optional flag to delete the release branch after testing"
    echo ""
    echo "Examples:"
    echo "  $0 v1.2.0                    # Test workflow, keep branch"
    echo "  $0 my-feature --cleanup      # Test workflow, delete branch after"
    echo "  $0 hotfix-123 --cleanup      # Test workflow, delete branch after"
    echo ""
    echo "Safety:"
    echo "  • All operations are local only (no git push)"
    echo "  • No PRs are created"
    echo "  • Original branch is restored"
    echo "  • Cleanup is optional and explicit"
}

# Parse command line arguments
RELEASE_NAME=""
CLEANUP_REQUESTED=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --cleanup)
            CLEANUP_REQUESTED=true
            shift
            ;;
        --help|-h)
            print_help
            exit 0
            ;;
        -*)
            print_error "Unknown option: $1"
            echo ""
            print_help
            exit 1
            ;;
        *)
            if [ -z "$RELEASE_NAME" ]; then
                RELEASE_NAME="$1"
            else
                print_error "Too many arguments. Only one release name is allowed."
                echo ""
                print_help
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required parameters
if [ -z "$RELEASE_NAME" ]; then
    print_error "Release name is required."
    echo ""
    print_help
    exit 1
fi

# Validate release name format
if [[ ! "$RELEASE_NAME" =~ ^[a-zA-Z0-9._-]+$ ]]; then
    print_error "Invalid release name. Use only letters, numbers, dots, hyphens, and underscores."
    exit 1
fi

BRANCH_NAME="release/$RELEASE_NAME"

# Cleanup function to ensure we don't leave test branches behind
cleanup() {
    local exit_code=$?
    if [ -n "$BRANCH_NAME" ] && git show-ref --verify --quiet refs/heads/$BRANCH_NAME 2>/dev/null; then
        echo ""
        print_warning "🧹 Emergency cleanup due to error or interrupt..."
        git checkout $ORIGINAL_BRANCH 2>/dev/null || git checkout prod 2>/dev/null || true
        if $CLEANUP_REQUESTED; then
            git branch -D $BRANCH_NAME 2>/dev/null || true
            print_success "Cleaned up release branch: $BRANCH_NAME"
        else
            print_info "Release branch preserved: $BRANCH_NAME (use --cleanup to auto-delete)"
        fi
    fi
    exit $exit_code
}

# Set up cleanup trap for emergencies only
trap cleanup EXIT INT TERM

echo "🧪 Testing Release Workflow Steps"
echo "=================================="
echo "Release name: $RELEASE_NAME"
echo "Branch name: $BRANCH_NAME"
echo "Cleanup after test: $([ "$CLEANUP_REQUESTED" = true ] && echo "YES" || echo "NO")"
echo ""

print_step "1" "Initial Setup and Validation"

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "Not in a git repository"
    exit 1
fi

print_success "In git repository"

# Check if main and prod branches exist
if ! git show-ref --verify --quiet refs/heads/main && ! git show-ref --verify --quiet refs/remotes/origin/main; then
    print_error "main branch not found"
    exit 1
fi

if ! git show-ref --verify --quiet refs/heads/prod && ! git show-ref --verify --quiet refs/remotes/origin/prod; then
    print_error "prod branch not found"
    exit 1
fi

print_success "main and prod branches exist"

# Configure git to avoid interactive sessions
git config --local core.editor "true"  # Use 'true' as editor (always succeeds)
git config --local merge.tool "true"   # Avoid merge tool prompts
git config --local core.pager "cat"    # Disable pagers
# Keep signing enabled; if you want local signing, ensure a key is configured.

# Get current branch to restore later
ORIGINAL_BRANCH=$(git branch --show-current)
print_info "Current branch: $ORIGINAL_BRANCH"

print_step "2" "Fetch Latest Changes"
echo "📥 Fetching latest changes..."
git fetch origin main
git fetch origin prod
print_success "Fetched latest changes from main and prod"

print_step "3" "Switch to prod branch"
if git checkout -B prod origin/prod 2>/dev/null; then
    print_success "Switched to prod branch (tracking origin/prod)"
else
    print_error "Failed to switch to prod branch"
    exit 1
fi

print_step "4" "Find Divergence Point"
echo "Finding where prod diverged from main..."

# Find the merge-base (common ancestor) between origin/prod and origin/main
MERGE_BASE=$(git merge-base origin/prod origin/main)
echo "Merge base (common ancestor): $MERGE_BASE"

# Find the first commit on origin/prod that's not on origin/main
DIVERGENCE_COMMIT=$(git rev-list --reverse origin/prod ^origin/main | head -n 1)

if [ -z "$DIVERGENCE_COMMIT" ]; then
    print_warning "No divergence found - prod and main are in sync"
    DIVERGENCE_COMMIT=$MERGE_BASE
    REVERT_TO_COMMIT=$MERGE_BASE
else
    echo "First diverging commit on prod: $DIVERGENCE_COMMIT"
    # Get the parent of the divergence commit (the last common commit)
    REVERT_TO_COMMIT=$(git rev-parse ${DIVERGENCE_COMMIT}^ 2>/dev/null || echo $MERGE_BASE)
fi

echo "Will revert back to: $REVERT_TO_COMMIT"

# Verify commits exist
if git rev-parse --verify $MERGE_BASE >/dev/null 2>&1; then
    print_success "Merge base commit exists: $(git log --oneline -1 $MERGE_BASE)"
else
    print_error "Merge base commit not found"
    exit 1
fi

if git rev-parse --verify $DIVERGENCE_COMMIT >/dev/null 2>&1; then
    print_success "Divergence commit exists: $(git log --oneline -1 $DIVERGENCE_COMMIT)"
else
    print_error "Divergence commit not found"
    exit 1
fi

if git rev-parse --verify $REVERT_TO_COMMIT >/dev/null 2>&1; then
    print_success "Revert target commit exists: $(git log --oneline -1 $REVERT_TO_COMMIT)"
else
    print_error "Revert target commit not found"
    exit 1
fi

print_step "5" "Analyze Commits to Revert"
# Get list of commits to revert (from divergence commit to HEAD)
COMMITS_TO_REVERT=$(git rev-list --reverse ${REVERT_TO_COMMIT}..HEAD)

if [ -z "$COMMITS_TO_REVERT" ]; then
    print_info "No commits to revert - prod is at the divergence point"
else
    echo "Commits that would be reverted:"
    echo "$COMMITS_TO_REVERT" | while read commit; do
        if [ -n "$commit" ]; then
            echo "  - $(git log --oneline -1 $commit)"
        fi
    done
    
    COMMIT_COUNT=$(echo "$COMMITS_TO_REVERT" | wc -l | tr -d ' ')
    echo "Total commits to revert: $COMMIT_COUNT"
fi

print_step "6" "Create Release Branch"
echo "🌿 Creating release branch: $BRANCH_NAME"

# Check if branch already exists locally
if git show-ref --verify --quiet refs/heads/$BRANCH_NAME; then
    echo "Branch $BRANCH_NAME already exists locally, deleting it"
    git branch -D $BRANCH_NAME
fi

# Check if branch exists on remote
if git ls-remote --exit-code --heads origin $BRANCH_NAME >/dev/null 2>&1; then
    print_error "Branch $BRANCH_NAME already exists on remote"
    print_error "Please choose a different release name or delete the existing branch"
    exit 1
fi

# Create new branch from current prod
git checkout -b $BRANCH_NAME
print_success "Created branch: $BRANCH_NAME"

print_step "7" "Reset Release Branch to Divergence Point"
echo "🔄 Hard resetting branch to divergence point $REVERT_TO_COMMIT"
git reset --hard "$REVERT_TO_COMMIT"
print_success "Reset to divergence point"

print_step "8" "Merge Main into Release Branch (prefer main)"
echo "🔀 Merging main into release branch..."

# Merge main into the current release branch preferring main's changes on conflict
if git merge --no-edit --no-ff -X theirs origin/main -m "Merge main into release branch (prefer main)

Automatically merged main branch into release branch after resetting to divergence point."; then
    print_success "Successfully merged main into release branch"
else
    print_warning "Merge reported conflicts, attempting auto-resolve by preferring main"
    git add -A
    if git -c core.editor=true merge --continue 2>/dev/null || git commit -m "Resolve conflicts preferring main changes"; then
        print_success "Conflicts resolved automatically by preferring main"
    else
        print_error "Failed to auto-resolve conflicts"
        git --no-pager status
        exit 1
    fi
fi

print_step "9" "Show Release Branch Status"
echo "📊 Release branch status:"
echo "Current branch: $(git branch --show-current)"
echo "Latest commit: $(git log --oneline -1)"
echo ""
echo "📈 Commit history (last 10 commits):"
git log --oneline -10

if $CLEANUP_REQUESTED; then
    print_step "10" "Cleanup - Delete Release Branch"
    echo "🧹 Cleaning up release branch as requested..."
    
    # Disable cleanup trap since we're doing manual cleanup
    trap - EXIT INT TERM
    
    # Switch back to original branch
    git checkout $ORIGINAL_BRANCH
    print_success "Switched back to: $ORIGINAL_BRANCH"
    
    # Delete the release branch
    git branch -D $BRANCH_NAME
    print_success "Deleted release branch: $BRANCH_NAME"
    
    STEP_NUMBER="11"
else
    print_step "10" "Preserve Release Branch"
    echo "🌿 Keeping release branch for further inspection..."
    
    # Disable cleanup trap since we're preserving the branch
    trap - EXIT INT TERM
    
    # Stay on the release branch for inspection
    print_success "Staying on release branch: $BRANCH_NAME"
    print_info "You are now on the release branch for inspection"
    print_info "To return to your original branch: git checkout $ORIGINAL_BRANCH"
    
    STEP_NUMBER="11"
fi

print_step "$STEP_NUMBER" "Summary and Results"
echo ""
echo "🔍 Analysis Results:"
echo "==================="
echo "• Merge base: $(git log --oneline -1 $MERGE_BASE)"
echo "• Divergence commit: $(git log --oneline -1 $DIVERGENCE_COMMIT)"
echo "• Revert target: $(git log --oneline -1 $REVERT_TO_COMMIT)"

if [ -n "$COMMITS_TO_REVERT_CHECK" ]; then
    COMMIT_COUNT=$(echo "$COMMITS_TO_REVERT_CHECK" | wc -l | tr -d ' ')
    echo "• Commits reverted: $COMMIT_COUNT"
else
    echo "• Commits reverted: 0 (prod and main are in sync)"
fi

echo ""
echo "🎯 Workflow Validation:"
echo "======================"

print_success "All workflow steps completed successfully!"
print_success "Release branch was created, reverted, and merged with main"
print_success "No conflicts detected during merge"

if $CLEANUP_REQUESTED; then
    print_success "Cleanup completed - no test branches left behind"
else
    print_success "Release branch preserved for inspection: $BRANCH_NAME"
fi

echo ""
echo "💡 Conclusion:"
echo "• The workflow logic is sound and works correctly"
echo "• The actual GitHub workflow should work as expected"
echo "• You can safely use the create-release.yml workflow"

if ! $CLEANUP_REQUESTED; then
    echo ""
    echo "🔧 Next Steps:"
    echo "• You are currently on the release branch: $BRANCH_NAME"
    echo "• Inspect the changes and commit history"
    echo "• Return to original branch: git checkout $ORIGINAL_BRANCH"
    echo "• Delete when done: git branch -D $BRANCH_NAME"
    echo "• Or run with --cleanup next time for automatic cleanup"
fi
