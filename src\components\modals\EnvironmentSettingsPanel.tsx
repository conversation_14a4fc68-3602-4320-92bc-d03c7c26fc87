import { useRoom } from "@liveblocks/react/suspense";
import { useCallback, useState, useEffect, useMemo } from "react";
import { toast } from "react-hot-toast";
import { MeshStandardMaterial, RepeatWrapping, TextureLoader } from "three";

import { <PERSON><PERSON> } from "../ui/button";
import { Label } from "../ui/label";
import { ColorPicker } from "../ui-nilo/ColorPicker";
import { TexturePicker } from "../ui-nilo/TexturePicker";
import { BasePanel } from "./BasePanel";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  useEditorUIActions,
  useEditorUIState,
} from "@/contexts/EditorUIContext";
import { Client } from "@/core/client";
import { FogType } from "@/core/composer/Composer";
import {
  NamedTextureData,
  RoomData,
  RoomDataService,
} from "@/services/RoomDataService";

/** Retains environment textures with firebase */
function useRoomTextureData(roomId: string, setData: (data: RoomData) => void) {
  useEffect(() => {
    RoomDataService.getRoomData(roomId).then((roomData) => {
      if (roomData) setData(roomData);
    });

    const unsubscribe = RoomDataService.subscribeToRoomData(
      roomId,
      (roomData) => {
        if (roomData) setData(roomData);
      }
    );

    return unsubscribe;
  }, [roomId, setData]);
}

export function EnvironmentSettingsPanel() {
  const { toggleEnvironmentSettingsPanel, toggleLeftPanelPinned } =
    useEditorUIActions();
  const { isLeftPanelPinned } = useEditorUIState();
  const room = useRoom();

  return (
    <BasePanel
      title="Environment"
      onClose={() => toggleEnvironmentSettingsPanel(false)}
      onPin={() => toggleLeftPanelPinned()}
      isPinned={isLeftPanelPinned}
      contentClassName="flex-1 min-h-0"
    >
      <Tabs defaultValue="sky" className="h-full flex flex-col">
        <div className="flex-shrink-0">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="sky">Sky</TabsTrigger>
            <TabsTrigger value="ground">Ground</TabsTrigger>
            <TabsTrigger value="fog">Fog</TabsTrigger>
          </TabsList>
          <Separator className="mb-0 mt-3" />
        </div>

        <div className="flex-1 min-h-0 pt-3">
          <TabsContent value="sky" className="space-y-7 gap-y-5 pt-1">
            <SkyTab roomId={room.id} />
          </TabsContent>

          <TabsContent value="ground" className="space-y-5 pt-1">
            <GroundTab roomId={room.id} />
          </TabsContent>

          <TabsContent value="fog" className="space-y-5 pt-1">
            <FogTab />
          </TabsContent>
        </div>
      </Tabs>
    </BasePanel>
  );
}

// Sky Tab Component
function SkyTab({ roomId }: { roomId: string }) {
  // Initialize state from Client
  const initialLightingOptions = Client.lighting?.getOptions() || {
    envIntensity: 0.15,
    bgIntensity: 0.5,
    bgBlur: 0.91,
    sunIntensity: 1,
    sunPhi: 0.9553166181245093,
    sunTheta: 0.7853981633974483,
  };

  // Texture states - initialize with loaded data
  const [skyMap, setSkyMap] = useState("" as NamedTextureData | string);

  const updateData = useMemo(
    () => (data: RoomData) => {
      if (data.textures?.environment) {
        setSkyMap(data.textures.environment);
      }
    },
    []
  );

  useRoomTextureData(roomId, updateData);

  // Lighting states
  const [envIntensity, setEnvIntensity] = useState<number>(
    initialLightingOptions.envIntensity
  );
  const [bgIntensity, setBgIntensity] = useState<number>(
    initialLightingOptions.bgIntensity
  );
  const [bgBlur, setBgBlur] = useState<number>(initialLightingOptions.bgBlur);
  const [sunIntensity, setSunIntensity] = useState<number>(
    initialLightingOptions.sunIntensity
  );
  const [sunPhi, setSunPhi] = useState<number>(initialLightingOptions.sunPhi);
  const [sunTheta, setSunTheta] = useState<number>(
    initialLightingOptions.sunTheta
  );

  const handleEnvIntensityChange = useCallback(([value]: number[]) => {
    setEnvIntensity(value);
    Client.lighting?.applyOptions({ envIntensity: value });
  }, []);

  const handleBgIntensityChange = useCallback(([value]: number[]) => {
    setBgIntensity(value);
    Client.lighting?.applyOptions({ bgIntensity: value });
  }, []);

  const handleBgBlurChange = useCallback(([value]: number[]) => {
    setBgBlur(value);
    Client.lighting?.applyOptions({ bgBlur: value });
  }, []);

  const handleSunIntensityChange = useCallback(([value]: number[]) => {
    setSunIntensity(value);
    Client.lighting?.applyOptions({ sunIntensity: value });
  }, []);

  const handleSunPhiChange = useCallback(([value]: number[]) => {
    setSunPhi(value);
    Client.lighting?.applyOptions({ sunPhi: value });
  }, []);

  const handleSunThetaChange = useCallback(([value]: number[]) => {
    setSunTheta(value);
    Client.lighting?.applyOptions({ sunTheta: value });
  }, []);

  return (
    <div className="space-y-7 gap-y-5">
      <div className="space-y-2">
        <Label htmlFor="sky-preset">Preset</Label>
        <Select disabled>
          <SelectTrigger>
            <SelectValue placeholder="Coming soon" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="cloudy-sky">Cloudy Sky</SelectItem>
            <SelectItem value="clear-sky">Clear Sky</SelectItem>
            <SelectItem value="sunset">Sunset</SelectItem>
            <SelectItem value="night">Night</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <TexturePicker
          id="sky-map"
          value={skyMap}
          onChange={setSkyMap}
          roomId={roomId}
          type="environment"
          defaultText="Sky texture"
        />
      </div>

      <div className="space-y-7">
        <div className="space-y-2">
          <Label>Environment Intensity</Label>
          <Slider
            value={[envIntensity]}
            max={1}
            step={0.01}
            className="w-full"
            onValueChange={handleEnvIntensityChange}
          />
        </div>

        <div className="space-y-2">
          <Label>Background Intensity</Label>
          <Slider
            value={[bgIntensity]}
            max={1}
            step={0.01}
            className="w-full"
            onValueChange={handleBgIntensityChange}
          />
        </div>

        <div className="space-y-2">
          <Label>Background Blur</Label>
          <Slider
            value={[bgBlur]}
            max={1}
            step={0.01}
            className="w-full"
            onValueChange={handleBgBlurChange}
          />
        </div>

        <div className="space-y-2">
          <Label>Sun Intensity</Label>
          <Slider
            value={[sunIntensity]}
            max={5}
            step={0.1}
            className="w-full"
            onValueChange={handleSunIntensityChange}
          />
        </div>

        <div className="space-y-2">
          <Label>Sun Phi</Label>
          <Slider
            value={[sunPhi]}
            max={Math.PI}
            step={0.01}
            className="w-full"
            onValueChange={handleSunPhiChange}
          />
        </div>

        <div className="space-y-2">
          <Label>Sun Theta</Label>
          <Slider
            value={[sunTheta]}
            max={Math.PI * 2}
            step={0.01}
            className="w-full"
            onValueChange={handleSunThetaChange}
          />
        </div>
      </div>
    </div>
  );
}

// Ground Tab Component
function GroundTab({ roomId }: { roomId: string }) {
  // Initialize state from Client
  const initialComposerOptions = Client.composer?.options || {
    gridRepeat: 1,
  };

  // Texture states - initialize with loaded data
  const [groundMap, setGroundMap] = useState("" as NamedTextureData | string);
  const [roughnessMap, setRoughnessMap] = useState(
    "" as NamedTextureData | string
  );
  const [metalnessMap, setMetalnessMap] = useState(
    "" as NamedTextureData | string
  );
  const [normalMap, setNormalMap] = useState("" as NamedTextureData | string);
  const [aoMap, setAoMap] = useState("" as NamedTextureData | string);

  // Retain state with relation to firebase
  const updateData = useMemo(
    () => (data: RoomData) => {
      if (data.textures?.grid) {
        setGroundMap(data.textures.grid.map || "");
        setRoughnessMap(data.textures.grid.roughnessMap || "");
        setMetalnessMap(data.textures.grid.metalnessMap || "");
        setNormalMap(data.textures.grid.normalMap || "");
        setAoMap(data.textures.grid.aoMap || "");
      }
    },
    []
  );

  useRoomTextureData(roomId, updateData);

  // Grid states
  const [gridRepeat, setGridRepeat] = useState<number>(
    initialComposerOptions.gridRepeat
  );

  const handleGridRepeatChange = useCallback(([value]: number[]) => {
    setGridRepeat(value);
    if (Client.composer) {
      Client.composer.options.gridRepeat = value;
      Client.composer.updateOptions();
    }
  }, []);

  const handleResetGridTextures = useCallback(async () => {
    try {
      await toast.promise(
        (async () => {
          const grid = Client.grid;
          const material = grid.mesh.material as MeshStandardMaterial;

          // Reset all grid textures
          grid.gridRoughnessMap = null;
          grid.gridMetalnessMap = null;
          grid.gridNormalMap = null;
          grid.gridAOMap = null;

          // Reset material properties
          material.roughnessMap = null;
          material.metalnessMap = null;
          material.normalMap = null;
          material.aoMap = null;
          material.roughness = 0.5;
          material.metalness = 0;

          // Load default grid texture
          const loader = new TextureLoader();
          const texture = loader.load("/textures/grid/texture_08.png");
          texture.wrapS = texture.wrapT = RepeatWrapping;
          texture.repeat.set(
            grid.gridSize * grid.gridScale,
            grid.gridSize * grid.gridScale
          );
          grid.gridMap = texture;
          material.map = texture;
          material.needsUpdate = true;

          // Update Firebase storage state
          await RoomDataService.updateTextures(roomId, {
            grid: {
              map: null,
              roughnessMap: null,
              metalnessMap: null,
              normalMap: null,
              aoMap: null,
            },
          });

          // Clear local state
          setGroundMap("");
          setRoughnessMap("");
          setMetalnessMap("");
          setNormalMap("");
          setAoMap("");
        })(),
        {
          loading: "Resetting grid textures...",
          success: "Grid textures reset to default",
          error: (err: Error) => err.message,
        }
      );
    } catch (err) {
      console.error("❌ Failed to reset grid textures:", err);
    }
  }, [roomId]);

  return (
    <div className="space-y-5">
      <div className="space-y-2">
        <Label htmlFor="ground-preset">Preset</Label>
        <Select disabled>
          <SelectTrigger>
            <SelectValue placeholder="Coming soon" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="grassy-field">Grassy Field</SelectItem>
            <SelectItem value="desert">Desert</SelectItem>
            <SelectItem value="snow">Snow</SelectItem>
            <SelectItem value="concrete">Concrete</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Grid Repeat</Label>
        <Slider
          value={[gridRepeat]}
          min={0.1}
          max={10}
          step={0.1}
          className="w-full"
          onValueChange={handleGridRepeatChange}
        />
      </div>

      <div className="space-y-2">
        <div className="space-y-2">
          <TexturePicker
            id="ground-map"
            value={groundMap}
            onChange={setGroundMap}
            roomId={roomId}
            type="grid"
            subtype="map"
            defaultText="Ground texture"
          />
        </div>

        <div className="space-y-2">
          <TexturePicker
            id="roughness-map"
            value={roughnessMap}
            onChange={setRoughnessMap}
            roomId={roomId}
            type="grid"
            subtype="roughnessMap"
            defaultText="Roughness map"
          />
        </div>

        <div className="space-y-2">
          <TexturePicker
            id="metalness-map"
            value={metalnessMap}
            onChange={setMetalnessMap}
            roomId={roomId}
            type="grid"
            subtype="metalnessMap"
            defaultText="Metalness map"
          />
        </div>

        <div className="space-y-2">
          <TexturePicker
            id="normal-map"
            value={normalMap}
            onChange={setNormalMap}
            roomId={roomId}
            type="grid"
            subtype="normalMap"
            defaultText="Normal map"
          />
        </div>

        <div className="space-y-2">
          <TexturePicker
            id="ao-map"
            value={aoMap}
            onChange={setAoMap}
            roomId={roomId}
            type="grid"
            subtype="aoMap"
            defaultText="AO map"
          />
        </div>

        <Button
          variant="outline"
          className="w-full"
          onClick={handleResetGridTextures}
        >
          Reset Grid Textures
        </Button>
      </div>
    </div>
  );
}

// Fog Tab Component
function FogTab() {
  // Initialize state from Client
  const initialComposerOptions = Client.composer?.options || {
    fog: {
      enabled: false,
      type: FogType.Linear,
      color: "#cccccc",
      near: 10,
      far: 50,
      density: 0.00025,
    },
  };

  // Fog states
  const [fogEnabled, setFogEnabled] = useState<boolean>(
    initialComposerOptions.fog.enabled
  );
  const [fogType, setFogType] = useState<FogType>(
    initialComposerOptions.fog.type
  );
  const [fogColor, setFogColor] = useState<string>(
    initialComposerOptions.fog.color
  );
  const [fogNear, setFogNear] = useState<number>(
    initialComposerOptions.fog.near
  );
  const [fogFar, setFogFar] = useState<number>(initialComposerOptions.fog.far);
  const [fogDensity, setFogDensity] = useState<number>(
    initialComposerOptions.fog.density
  );

  const handleFogEnabledChange = useCallback((checked: boolean) => {
    setFogEnabled(checked);
    if (Client.composer) {
      Client.composer.options.fog.enabled = checked;
      Client.composer.updateOptions();
    }
  }, []);

  const handleFogTypeChange = useCallback((value: string) => {
    const type = value as FogType;
    setFogType(type);
    if (Client.composer) {
      Client.composer.options.fog.type = type;
      Client.composer.updateOptions();
    }
  }, []);

  const handleFogColorChange = useCallback((color: string) => {
    setFogColor(color);
    if (Client.composer) {
      Client.composer.options.fog.color = color;
      Client.composer.updateOptions();
    }
  }, []);

  const handleFogNearChange = useCallback(([value]: number[]) => {
    setFogNear(value);
    if (Client.composer) {
      Client.composer.options.fog.near = value;
      Client.composer.updateOptions();
    }
  }, []);

  const handleFogFarChange = useCallback(([value]: number[]) => {
    setFogFar(value);
    if (Client.composer) {
      Client.composer.options.fog.far = value;
      Client.composer.updateOptions();
    }
  }, []);

  const handleFogDensityChange = useCallback(([value]: number[]) => {
    setFogDensity(value);
    if (Client.composer) {
      Client.composer.options.fog.density = value;
      Client.composer.updateOptions();
    }
  }, []);

  return (
    <div className="space-y-5">
      <div className="flex items-center justify-between">
        <Label htmlFor="fog-enabled">Enabled</Label>
        <Switch
          id="fog-enabled"
          checked={fogEnabled}
          onCheckedChange={handleFogEnabledChange}
        />
      </div>

      {fogEnabled && (
        <>
          <div className="space-y-2">
            <Label htmlFor="fog-type">Type</Label>
            <Select value={fogType} onValueChange={handleFogTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={FogType.Linear}>Linear</SelectItem>
                <SelectItem value={FogType.Exponential}>Exponential</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="fog-color">Color</Label>
            <ColorPicker
              id="fog-color"
              placeholder="#FFFFFF"
              defaultColor="#FFFFFF"
              value={fogColor}
              onChange={handleFogColorChange}
            />
          </div>

          {fogType === FogType.Linear && (
            <div className="space-y-5">
              <div className="space-y-2">
                <Label>Near Distance</Label>
                <Slider
                  value={[fogNear]}
                  min={10}
                  max={100}
                  step={1}
                  className="w-full"
                  onValueChange={handleFogNearChange}
                />
              </div>

              <div className="space-y-2">
                <Label>Far Distance</Label>
                <Slider
                  value={[fogFar]}
                  min={10}
                  max={100}
                  step={1}
                  className="w-full"
                  onValueChange={handleFogFarChange}
                />
              </div>
            </div>
          )}

          {fogType === FogType.Exponential && (
            <div className="space-y-2">
              <Label>Density</Label>
              <Slider
                value={[fogDensity]}
                min={0.00001}
                max={0.01}
                step={0.00001}
                className="w-full"
                onValueChange={handleFogDensityChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
