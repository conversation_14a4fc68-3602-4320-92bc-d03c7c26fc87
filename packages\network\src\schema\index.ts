import {
  ByteStreamReader,
  ByteStreamWriter,
  f32,
  Schema,
  Struct,
  text,
  u16,
  List,
  u32,
  u8Array,
  Optional,
  u8,
} from "@evenstar/byteform";
import { QuaternionLike, Vector3Like } from "three";

/** A Byteform schema for Boolean values. */
export class Boolean implements Schema<boolean> {
  /**
   * The internal number schema to use for representing Booleans.
   * Zero is false, non-zero is true.
   */
  private readonly _schema: Schema<number>;

  /** Creates a new Boolean schema. */
  public constructor() {
    // use u8 as the underlying numeric representation
    this._schema = u8;
  }

  public write(writer: ByteStreamWriter, value: boolean) {
    this._schema.write(writer, value ? 1 : 0);
  }

  public read(reader: ByteStreamReader): boolean {
    return this._schema.read(reader) !== 0;
  }
}

/** Type alias for network schema entity IDs */
export type EntityId = string;

/** Type alias for component IDs */
export type ComponentId = number;

/** Schema of entity identifiers */
export const entityIdSchema = text;

/** Schema of component identifiers */
export const componentSchema = u32;

/** 3D vector schema */
export const vec3Schema = new Struct({
  x: f32,
  y: f32,
  z: f32,
});

/** Quaternion schema */
export const quatSchema = new Struct({
  x: f32,
  y: f32,
  z: f32,
  w: f32,
});

/** Complete affine transform */
export interface Transform {
  position: Vector3Like;
  scale: Vector3Like;
  orientation: QuaternionLike;
}

export const transformSchema: Schema<Transform> = new Struct({
  position: vec3Schema,
  scale: vec3Schema,
  orientation: quatSchema,
});

export interface UserData {
  transform: Transform;
  color: number;
  selectedEntities: EntityId[];
  targetEntity: EntityId | null;
  profile: UserProfileData;
}

export interface PrimitiveData {
  primitiveType: string;
  originType: string;
  transform: Transform;
  color: number;
  userId: EntityId | null;
  transforms: TransformationDataSchema[];
  material: MaterialData;
  dragging: boolean;
}

export interface TransformationDataSchema {
  type: string;
  params: string;
}

/** Material options for entity rendering */
export interface MaterialData {
  metalness: number;
  roughness: number;
  wireframe: boolean;
  transmission: number;
  ior: number;
  thickness: number;
  dispersion: number;
}

/** Each field in this struct can be empty to mark absence */
export interface UserProfileData {
  username: string | null;
  displayName: string | null;
  avatarUrl: string | null;
  bio: string | null;
}

/** User entity-specific data */
export const userDataSchema: Schema<UserData> = new Struct({
  transform: transformSchema,
  // #rrggbb
  color: u32,
  selectedEntities: new List(text),
  targetEntity: new Optional(entityIdSchema),
  profile: new Struct({
    username: new Optional(text),
    displayName: new Optional(text),
    avatarUrl: new Optional(text),
    bio: new Optional(text),
  }),
});

export const materialSchema: Schema<MaterialData> = new Struct({
  metalness: f32,
  roughness: f32,
  wireframe: new Boolean(),
  transmission: f32,
  ior: f32,
  thickness: f32,
  dispersion: f32,
});

// NOTE: Using a simplified approach for now - we serialize params as a JSON string
// This allows dynamic key-value pairs without needing to define static schemas for each transformation type
// TODO: Consider migrating to individual schemas per transformation type for better type safety and performance
export const transformationSchema: Schema<TransformationDataSchema> =
  new Struct({
    type: text,
    /** JSON string representation of Record<string, number> */
    params: text,
  });

export const primitiveDataSchema: Schema<PrimitiveData> = new Struct({
  primitiveType: text,
  originType: text,
  transform: transformSchema,
  userId: new Optional<EntityId>(entityIdSchema), // can be empty
  // #rrggbb
  color: u32,
  transforms: new List(transformationSchema),
  material: materialSchema,
  dragging: new Boolean(),
});

/** Defines a message with a unique numeric tag and data schema. */
export class MessageSchema<T> {
  public readonly tag: number;
  public readonly schema: Schema<T>;

  static _tagNames = new Array<string>();

  /** Provides read access to the immutable mapping of message tags to schema names */
  public static get tagNames() {
    return MessageSchema._tagNames;
  }

  /**
   * Creates a new message schema with a unique tag.
   * This function ensures each message type has a unique identifier.
   * @param name Human-readable name for debugging
   * @param schema The data schema for this message type
   */
  private constructor(name: string, schema: Schema<T>) {
    // not concurrency safe. this is why the constructor is private.
    this.tag = MessageSchema._tagNames.length;
    MessageSchema._tagNames.push(name);
    this.schema = schema;
    console.debug("🔌 Constructed message schema", { tag: this.tag, name });
  }

  /** Serialize a message of this schema.
   * @param encoder An external ByteStreamWriter to reuse.
   * @param value The message data to be serialized.
   * @returns Serialized message as Uint8Array
   */
  public serialize(encoder: ByteStreamWriter, value: T): Uint8Array {
    encoder.reset();
    encoder.writeSchema(u16, this.tag);
    encoder.writeSchema(this.schema, value);
    return encoder.commit();
  }

  /** Empty message schema for connection testing */
  static readonly Ping: MessageSchema<object> = new MessageSchema(
    "Ping",
    new Struct({})
  );

  /** Sent to users upon a user joining. Contains that user's entity ID */
  static readonly UserJoin: MessageSchema<EntityId> = new MessageSchema(
    "UserJoin",
    entityIdSchema
  );

  /** Spawn an entity. */
  static readonly SpawnEntity: MessageSchema<EntityId> = new MessageSchema(
    "SpawnEntity",
    entityIdSchema
  );

  /** Kill an entity. */
  static readonly KillEntity: MessageSchema<EntityId> = new MessageSchema(
    "KillEntity",
    entityIdSchema
  );

  /** Insert (create or update) a component onto an entity. */
  static readonly InsertComponent = new MessageSchema(
    "InsertComponent",
    new Struct({
      entity: entityIdSchema,
      component: componentSchema,
      data: u8Array,
    })
  );

  /** Remove a component from an entity. */
  static readonly RemoveComponent = new MessageSchema(
    "RemoveComponent",
    new Struct({
      entity: entityIdSchema,
      component: componentSchema,
    })
  );
}

/**
 * Handler function type for processing messages.
 * @template T The context data type
 * @template M The message data type
 */
export type Handler<T, M> = (data: T, message: M) => void;

/** Internal handler type for raw message processing */
type RawHandler<T> = (state: T, message: ByteStreamReader) => void;

/** Utility class to route tagged messages to dedicated methods. Generic over common handler state. */
export class Router<T> {
  // Maps each message's tag to a dedicated handler.
  private _handlers: Map<number, RawHandler<T>> = new Map();

  /** An error logging function to use for handlers */
  private _errorLogger: (message: string, ...args: any[]) => void;

  /**
   * Creates a new router with an error logging function.
   * @param errorLogger The function to use for logging errors.
   */
  public constructor(errorLogger: (message: string, ...args: any[]) => void) {
    this._errorLogger = errorLogger;
  }

  /**
   * Registers a handler for a specific message schema.
   * @param schema The message schema to handle
   * @param handler Function to process messages of this type
   * @returns This router instance for chaining
   */
  public addHandler<M>(schema: MessageSchema<M>, handler: Handler<T, M>): this {
    if (this._handlers.has(schema.tag)) {
      this._errorLogger(`🔌 Handler for tag ${schema.tag} already added`);
      return this;
    }

    this._handlers.set(schema.tag, (state, reader) => {
      const message = reader.readSchema(schema.schema);
      handler(state, message);
    });

    return this;
  }

  /**
   * Parses a message and invokes the appropriate handler in response.
   * @param state The state to invoke the handler on
   * @param data Raw message data as Uint8Array
   */
  public handleData(state: T, data: Uint8Array) {
    try {
      // Validate message size - prevent processing obviously corrupted data
      if (!data || data.length === 0) {
        this._errorLogger("🔌 Received empty network message, skipping");
        return;
      }

      if (data.length > 64 * 1024) {
        // 64KiB max from ByteStreamWriter config
        this._errorLogger("🔌 Received oversized network message", {
          size: data.length,
          maxSize: 64 * 1024,
        });
        return;
      }

      const reader = new ByteStreamReader(data);
      const tag = reader.readSchema(u16);
      const handler = this._handlers.get(tag);

      if (handler) {
        try {
          handler(state, reader);
        } catch (handlerError) {
          this._errorLogger("🔌 Network message handler failed", {
            tag,
            name: MessageSchema.tagNames.at(tag),
            messageSize: data.length,
            error: handlerError,
            // Log first few bytes for debugging (but not the whole message for privacy)
            dataPreview: Array.from(data.slice(0, 16))
              .map((b) => b.toString(16).padStart(2, "0"))
              .join(" "),
          });
        }
      } else {
        const name = MessageSchema.tagNames.at(tag);
        this._errorLogger("🔌 Unhandled network message tag", {
          tag,
          name,
          messageSize: data.length,
        });
      }
    } catch (parseError) {
      this._errorLogger("🔌 Failed to parse network message", {
        messageSize: data?.length || 0,
        error: parseError,
        // Log first few bytes for debugging potential corruption patterns
        dataPreview: data
          ? Array.from(data.slice(0, 16))
              .map((b) => b.toString(16).padStart(2, "0"))
              .join(" ")
          : "null",
      });
    }
  }

  /**
   * Extends this router with a new state type.
   * @param state The state to give to the handlers with the existing state type.
   */
  public extend<O>(state: T): Router<O> {
    // create a blank router to wrap existing handlers into
    const router = new Router<O>(this._errorLogger);

    // close existing handlers over given state, ignoring new one
    for (const [key, cb] of this._handlers) {
      router._handlers.set(key, (_state: O, message) => cb(state, message));
    }

    // return the modified router
    return router;
  }

  /**
   * Binds this router to a given state and returns a callback that
   * invokes this router's handlers on it.
   */
  public route(state: T): (data: Uint8Array) => void {
    return (data) => this.handleData(state, data);
  }
}
