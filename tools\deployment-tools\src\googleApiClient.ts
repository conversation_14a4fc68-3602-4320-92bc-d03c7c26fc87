import { z } from "zod";
import {
  google,
  firebaserules_v1,
  firestore_v1,
  storage_v1,
  cloudfunctions_v2,
} from "googleapis";
import { getCurrentFirebaseProject } from "./firebase";
import { authenticate, getProjectId as getProjectIdFromAuth } from "./auth";

export enum RulesService {
  Firestore = "cloud.firestore",
  Storage = "firebase.storage",
}

const DatabaseNameSchema = z
  .string()
  .regex(/^projects\/[a-z0-9-]+\/databases\/[a-z0-9()-]+$/);

// FIXME: last part is UUID
const RulesetNameSchema = z
  .string()
  .regex(/^projects\/[a-z0-9-]+\/rulesets\/[a-f0-9-]+$/);

const ReleaseNameSchema = z
  .string()
  .regex(
    /^projects\/[a-z0-9-]+\/releases\/(cloud\.firestore(\/[a-z0-9-]+)?|firebase\.storage\/[a-z0-9.-]+)$/
  );

const FunctionNameSchema = z
  .string()
  .regex(
    /^projects\/[a-z0-9-]+\/locations\/[a-z0-9-]+\/functions\/([a-z0-9]+-)?[a-zA-Z0-9]+$/
  );

export function objectAgeSeconds(
  object: {
    createTime?: string | null;
    updateTime?: string | null;
    timeCreated?: string | null;
    updated?: string | null;
  },
  now?: Date
): number | undefined {
  now = now || new Date();
  const age = (
    [
      "updateTime",
      "updated",
      "createTime",
      "timeCreated",
    ] as (keyof typeof object)[]
  )
    .map((key) =>
      object[key]
        ? Math.floor((now.getTime() - new Date(object[key]).getTime()) / 1000)
        : Infinity
    )
    .reduce((min, age) => Math.min(min, age), Infinity);
  return Number.isFinite(age) ? age : undefined;
}

/**
 * GoogleApiClient class for managing Firebase API operations
 */
export class GoogleApiClient {
  private _auth: Awaited<ReturnType<typeof authenticate>> | null = null;
  private _projectId: string | null = null;
  private _firebaserules: firebaserules_v1.Firebaserules | null = null;
  private _firestore: firestore_v1.Firestore | null = null;
  private _storage: storage_v1.Storage | null = null;
  private _functions: cloudfunctions_v2.Cloudfunctions | null = null;

  /**
   * Initializes authentication
   */
  public async initialize(): Promise<void> {
    if (this._auth && this._projectId) {
      return;
    }

    // Authenticate using firebase-tools compatible methods
    const auth = await authenticate();
    this._auth = auth;

    // Try to get project ID from authentication first, then fall back to project detection
    let projectId = await getProjectIdFromAuth(auth);
    if (!projectId) {
      projectId = getCurrentFirebaseProject();

      if (!projectId) {
        throw new Error(
          "No Firebase project found. Make sure you are in a Firebase project directory or have a .firebaserc file.\nYou can also set up a project using: firebase use <project-id>\nOr authenticate with: firebase login"
        );
      }
    }
    this._projectId = projectId;

    console.info(`Using Firebase project: ${projectId}`);
  }

  /**
   * Gets the authentication object
   */
  private get auth(): Awaited<ReturnType<typeof authenticate>> {
    if (!this._auth) {
      throw new Error(
        "GoogleApiClient not initialized. Call initialize() first."
      );
    }
    return this._auth;
  }

  /**
   * Gets the project ID
   */
  get projectId(): string {
    if (!this._projectId) {
      throw new Error(
        "GoogleApiClient not initialized. Call initialize() first."
      );
    }
    return this._projectId;
  }

  /**
   * Gets the firebaserules API object
   */
  get firebaserules(): firebaserules_v1.Firebaserules {
    if (!this._firebaserules) {
      this._firebaserules = google.firebaserules({
        version: "v1",
        auth: this.auth,
      });
    }
    return this._firebaserules;
  }

  /**
   * Gets the firestore API object
   */
  get firestore(): firestore_v1.Firestore {
    if (!this._firestore) {
      this._firestore = google.firestore({
        version: "v1",
        auth: this.auth,
      });
    }
    return this._firestore;
  }

  /**
   * Gets the storage API object
   */
  get storage(): storage_v1.Storage {
    if (!this._storage) {
      this._storage = google.storage({
        version: "v1",
        auth: this.auth,
      });
    }
    return this._storage;
  }

  /**
   * Gets the cloudfunctions API object
   */
  get functions(): cloudfunctions_v2.Cloudfunctions {
    if (!this._functions) {
      this._functions = google.cloudfunctions({
        version: "v2",
        auth: this.auth,
      });
    }
    return this._functions;
  }

  /**
   * Retrieves all Firebase Security Rulesets with pagination
   */
  async listAllRulesets(): Promise<
    Omit<firebaserules_v1.Schema$Ruleset, "source">[]
  > {
    await this.initialize();

    // Fetch all rulesets with pagination
    const allRulesets: firebaserules_v1.Schema$Ruleset[] = [];
    let pageToken: string | undefined;

    do {
      const res = await this.firebaserules.projects.rulesets.list({
        name: `projects/${this.projectId}`,
        pageSize: 100, // Maximum page size
        pageToken,
      });

      const rulesets = res.data.rulesets || [];
      allRulesets.push(...rulesets);
      pageToken = res.data.nextPageToken || undefined;

      // Safety check to prevent infinite loops
      if (allRulesets.length > 1000) {
        console.warn(
          "Warning: Found more than 1000 rulesets, stopping pagination"
        );
        break;
      }
    } while (pageToken);

    return allRulesets;
  }

  /**
   * Retrieves a specific Firebase Security Ruleset by ID
   */
  async getRuleset(
    rulesetIdOrName: string
  ): Promise<firebaserules_v1.Schema$Ruleset> {
    await this.initialize();

    const rulesetName = rulesetIdOrName.includes("/")
      ? rulesetIdOrName
      : `projects/${this.projectId}/rulesets/${rulesetIdOrName}`;
    const res = await this.firebaserules.projects.rulesets.get({
      name: rulesetName,
    });

    if (!res.data) {
      throw new Error(`Ruleset with ID or name '${rulesetIdOrName}' not found`);
    }

    return res.data;
  }

  /**
   * Deletes a specific Firebase Security Ruleset by name
   */
  async deleteRuleset(rulesetName: string): Promise<void> {
    await this.initialize();
    RulesetNameSchema.parse(rulesetName);
    await this.firebaserules.projects.rulesets.delete({ name: rulesetName });
  }

  /**
   * Gets the name of a Firebase Security Ruleset Release by service and database or storage bucket
   * @param databaseName - The name of the Firestore database (either full name `projects/<project-id>/databases/<database-id>` or just `<database-id>`)
   * @param storageBucket - The name of the Storage bucket
   */
  getReleaseName({
    service,
    databaseName,
    storageBucket,
  }:
    | {
        service: RulesService.Firestore;
        databaseName: string;
        storageBucket?: never;
      }
    | {
        service: RulesService.Storage;
        storageBucket: string;
        databaseName?: never;
      }): string {
    switch (service) {
      case RulesService.Firestore: {
        const databaseId = DatabaseNameSchema.safeParse(databaseName).success
          ? databaseName.split("/").pop()!
          : databaseName;
        console.assert(
          /^[a-z0-9()-]+$/.test(databaseId),
          `databaseId is invalid: ${databaseId}`
        );
        return ReleaseNameSchema.parse(
          databaseId === "(default)"
            ? `projects/${this.projectId}/releases/${service}`
            : `projects/${this.projectId}/releases/${service}/${databaseId}`
        );
      }
      case RulesService.Storage:
        return ReleaseNameSchema.parse(
          `projects/${this.projectId}/releases/${service}/${storageBucket}`
        );
      default:
        throw new Error(`Invalid service: ${service}`);
    }
  }

  /**
   * Retrieves all Firebase Security Ruleset Releases with pagination
   */
  async listAllReleases(): Promise<firebaserules_v1.Schema$Release[]> {
    await this.initialize();

    // Fetch all releases with pagination
    const allReleases: firebaserules_v1.Schema$Release[] = [];
    let pageToken: string | undefined;

    do {
      const res = await this.firebaserules.projects.releases.list({
        name: `projects/${this.projectId}`,
        pageSize: 100, // Maximum page size
        pageToken,
      });

      const releases = res.data.releases || [];
      allReleases.push(...releases);
      pageToken = res.data.nextPageToken || undefined;

      // Safety check to prevent infinite loops
      if (allReleases.length > 1000) {
        console.warn(
          "Warning: Found more than 1000 releases, stopping pagination"
        );
        break;
      }
    } while (pageToken);

    return allReleases;
  }

  /**
   * Retrieves a specific Firebase Security Ruleset Release by name
   */
  async getRelease({
    releaseName,
    service,
    databaseName,
    storageBucket,
  }:
    | {
        releaseName: string;
        service?: never;
        databaseName?: never;
        storageBucket?: never;
      }
    | {
        service: RulesService.Firestore;
        databaseName: string;
        releaseName?: never;
        storageBucket?: never;
      }
    | {
        service: RulesService.Storage;
        storageBucket: string;
        databaseName?: never;
        releaseName?: never;
      }): Promise<firebaserules_v1.Schema$Release> {
    await this.initialize();

    const name =
      releaseName ??
      `projects/${this.projectId}/releases/${service}/${databaseName ?? storageBucket}`;
    const res = await this.firebaserules.projects.releases.get({ name });

    if (!res.data) {
      throw new Error(
        `Release with name '${service}/${databaseName ?? storageBucket}' not found`
      );
    }

    return res.data;
  }

  /**
   * Deletes a specific Firebase Security Ruleset Release by name
   */
  async deleteRelease(releaseName: string): Promise<void> {
    await this.initialize();
    ReleaseNameSchema.parse(releaseName);
    await this.firebaserules.projects.releases.delete({ name: releaseName });
  }

  /**
   * Retrieves all Firestore databases for the project
   */
  async listFirestoreDatabases(options?: {
    showDeleted?: boolean;
  }): Promise<firestore_v1.Schema$GoogleFirestoreAdminV1Database[]> {
    await this.initialize();

    const res = await this.firestore.projects.databases.list({
      parent: `projects/${this.projectId}`,
      showDeleted: options?.showDeleted || false,
    });

    if (!res.data.databases) {
      return [];
    }

    return res.data.databases;
  }

  /**
   * Gets the full name of a Firestore database by name
   */
  getDatabaseName(databaseName: string | undefined | null): string {
    return DatabaseNameSchema.parse(
      databaseName === undefined || databaseName === null
        ? `projects/${this.projectId}/databases/(default)`
        : databaseName.includes("/")
          ? databaseName
          : `projects/${this.projectId}/databases/${databaseName}`
    );
  }

  /**
   * Deletes a specific Firestore database by name
   */
  async deleteDatabase(databaseName: string): Promise<void> {
    await this.initialize();
    const name = databaseName.includes("/")
      ? databaseName
      : `projects/${this.projectId}/databases/${databaseName}`;
    DatabaseNameSchema.parse(name);
    await this.firestore.projects.databases.delete({ name });
  }

  /**
   * Retrieves all Storage buckets for the project
   */
  async listStorageBuckets(): Promise<storage_v1.Schema$Bucket[]> {
    await this.initialize();

    // Fetch all buckets with pagination
    const allBuckets: storage_v1.Schema$Bucket[] = [];
    let pageToken: string | undefined;

    do {
      const res = await this.storage.buckets.list({
        project: this.projectId,
        maxResults: 1000, // Maximum page size
        pageToken,
      });

      const buckets = res.data.items || [];
      allBuckets.push(...buckets);
      pageToken = res.data.nextPageToken || undefined;

      // Safety check to prevent infinite loops
      if (allBuckets.length > 10000) {
        console.warn(
          "Warning: Found more than 10000 buckets, stopping pagination"
        );
        break;
      }
    } while (pageToken);

    return allBuckets;
  }

  /**
   * Deletes a specific Storage bucket by name
   * First deletes all objects in the bucket using streaming pagination, then deletes the bucket itself
   */
  async deleteStorageBucket(
    bucketName: string,
    deletionCallback?: (objectsDeleted: number) => unknown | Promise<unknown>
  ): Promise<void> {
    await this.initialize();

    let pageToken: string | undefined;
    let totalDeleted = 0;

    do {
      // Fetch a batch of objects
      const res = await this.storage.objects.list({
        bucket: bucketName,
        maxResults: 1000, // Maximum page size
        pageToken,
      });

      const objects = res.data.items || [];
      pageToken = res.data.nextPageToken || undefined;

      if (objects.length > 0) {
        const deletePromises = objects.map((object) =>
          object.name
            ? this.storage.objects.delete({
                bucket: bucketName,
                object: object.name,
              })
            : Promise.resolve()
        );

        await Promise.all(deletePromises);
        totalDeleted += objects.length;
        if (deletionCallback) {
          await deletionCallback(totalDeleted);
        }
      }

      // Safety check to prevent infinite loops
      if (totalDeleted > 100000) {
        console.warn(
          "Warning: Deleted more than 100000 objects, stopping pagination"
        );
        break;
      }
    } while (pageToken);

    await this.storage.buckets.delete({ bucket: bucketName });
  }

  /**
   * Retrieves all Cloud Functions for the project with pagination
   */
  async listAllFunctions(): Promise<cloudfunctions_v2.Schema$Function[]> {
    await this.initialize();

    // Fetch all functions with pagination
    const allFunctions: cloudfunctions_v2.Schema$Function[] = [];
    let pageToken: string | undefined;

    do {
      const res = await this.functions.projects.locations.functions.list({
        parent: `projects/${this.projectId}/locations/-`,
        pageSize: 100, // Maximum page size
        pageToken,
      });

      const functions = res.data.functions || [];
      allFunctions.push(...functions);
      pageToken = res.data.nextPageToken || undefined;

      // Safety check to prevent infinite loops
      if (allFunctions.length > 10000) {
        console.warn(
          "Warning: Found more than 10000 functions, stopping pagination"
        );
        break;
      }
    } while (pageToken);

    return allFunctions;
  }

  /**
   * Deletes a specific Cloud Function by name
   */
  async deleteFunction(functionName: string): Promise<void> {
    await this.initialize();
    const name = FunctionNameSchema.parse(functionName);
    await this.functions.projects.locations.functions.delete({
      name,
    });
  }
}
