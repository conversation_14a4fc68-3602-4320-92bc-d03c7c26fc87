import { Vercel } from "@vercel/sdk";

export async function listProjects(token: string): Promise<void> {
  const vercel = new Vercel({
    bearerToken: token,
  });

  console.info("📋 Fetching your Vercel projects...\n");

  try {
    const response = await vercel.projects.getProjects({});

    if (!response.projects || response.projects.length === 0) {
      console.info("No projects found.");
      return;
    }

    console.info("Your Vercel projects:\n");
    response.projects.forEach((project) => {
      console.info(`Name: ${project.name}`);
      console.info(`ID:   ${project.id}`);
      console.info(`URL:  https://${project.name}.vercel.app`);
      console.info("---");
    });
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Failed to fetch projects: ${error.message}`);
    }
    throw error;
  }
}
