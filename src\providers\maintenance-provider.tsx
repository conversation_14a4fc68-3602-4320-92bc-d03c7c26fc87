import React, { createContext, useContext } from "react";
import { Navigate, useLocation } from "react-router-dom";
import {
  collection,
  CollectionReference,
  limit,
  orderBy,
} from "firebase/firestore";
import { db } from "@/config/firebase";
import { DbCollections, DbMaintenance } from "@nilo/firebase-schema";
import { useIsAdmin } from "@/hooks/useIsAdmin";
import { queryT, useFirebaseQuery } from "@/hooks/firebaseHooks";

interface MaintenanceContextValue {
  isUnderMaintenance: boolean;
  maintenanceInfo: DbMaintenance | null;
}

const MaintenanceContext = createContext<MaintenanceContextValue>({
  isUnderMaintenance: false,
  maintenanceInfo: null,
});

export const useMaintenanceContext = () => useContext(MaintenanceContext);

interface MaintenanceProviderProps {
  children: React.ReactNode;
}

export const MaintenanceProvider: React.FC<MaintenanceProviderProps> = ({
  children,
}) => {
  const location = useLocation();
  const isAdmin = useIsAdmin();

  const maintenancesCollection = collection(
    db,
    DbCollections.maintenances
  ) as CollectionReference<DbMaintenance, DbMaintenance>;

  const q = queryT(
    maintenancesCollection,
    orderBy("createdAt", "desc"),
    limit(1)
  );

  const maintenances = useFirebaseQuery(q);
  const latestMaintenance = maintenances[0]?.data();

  if (!latestMaintenance) {
    return (
      <MaintenanceContext.Provider
        value={{
          isUnderMaintenance: false,
          maintenanceInfo: null,
        }}
      >
        {children}
      </MaintenanceContext.Provider>
    );
  }

  // Check if maintenance is active (no endedAt field and status is running)
  const isUnderMaintenance =
    !latestMaintenance.endedAt && latestMaintenance.status === "running";

  // Check if we're on an internal page (starts with /internal)
  const isInternalPage = location.pathname.startsWith("/internal");

  // If under maintenance and not already on maintenance page and not admin and
  // not on an internal page, then, redirect to maintenance page.
  if (
    isUnderMaintenance &&
    location.pathname !== "/maintenance" &&
    !isInternalPage &&
    !isAdmin
  ) {
    return <Navigate to="/maintenance" replace state={{ from: location }} />;
  }

  // If not under maintenance but on maintenance page, redirect back
  if (!isUnderMaintenance && location.pathname === "/maintenance") {
    const previousLocation = location.state?.from || { pathname: "/" };
    return <Navigate to={previousLocation} replace />;
  }

  // Check if maintenance is scheduled or running, not ended
  const scheduledOrRunning =
    !latestMaintenance.endedAt &&
    (latestMaintenance.status === "scheduled" ||
      latestMaintenance.status === "running");

  return (
    <MaintenanceContext.Provider
      value={{
        isUnderMaintenance,
        maintenanceInfo: scheduledOrRunning ? latestMaintenance : null,
      }}
    >
      {children}
    </MaintenanceContext.Provider>
  );
};
