/* eslint-disable no-redeclare */
import { z } from "zod";
import { DbInternalDatabaseId } from "../db";

export const InternalListDatabasesRequest = z.object({});
export type InternalListDatabasesRequest = z.infer<
  typeof InternalListDatabasesRequest
>;

export const InternalListDatabasesResponse = z.object({
  databases: z.array(DbInternalDatabaseId),
});
export type InternalListDatabasesResponse = z.infer<
  typeof InternalListDatabasesResponse
>;
