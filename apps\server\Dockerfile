FROM node:22-slim AS base
ENV CI=true
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
COPY . /app
WORKDIR /app

FROM base AS build-webtransport
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    apt update && apt install -y clang cmake build-essential perl6 golang ninja-build
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
WORKDIR /app/apps/server/node_modules/@fails-components/webtransport-transport-http3-quiche
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm run install

FROM base AS build
WORKDIR /app
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
RUN pnpm --filter server run build

FROM base AS prod
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile --prod
COPY --from=build /app/apps/server/dist /app/apps/server/dist
COPY --from=build-webtransport /app/apps/server/node_modules/@fails-components/webtransport-transport-http3-quiche /app/apps/server/node_modules/@fails-components/webtransport-transport-http3-quiche
WORKDIR /app
EXPOSE 20041/tcp
EXPOSE 20041/udp
CMD [ "pnpm", "--filter", "server", "run", "start" ]
