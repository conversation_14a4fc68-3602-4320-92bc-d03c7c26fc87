import {
  ChevronDownIcon,
  CompassIcon,
  CopyIcon,
  MessageCircleIcon,
  SettingsIcon,
  ShareIcon,
  CodeIcon,
  TreePine,
  TrashIcon,
} from "lucide-react";
import toast from "react-hot-toast";
import { Link } from "react-router-dom";
import { useState } from "react";

import { Avatar } from "@/components/common/Avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ConfirmationModal } from "@/components/ui-nilo/ConfirmationModal";
import { useUserProfileData } from "@/hooks/useUserProfileData";
import { WorldData } from "@/hooks/useWorldInfo";
import { useEditorUIActions } from "@/contexts/EditorUIContext";
import { trackWorldShared } from "@/utils/tracking/worldUtils";
import { useDeleteWorld } from "@/hooks/useDeleteWorld";
import { useFeatureFlag } from "@/hooks/useFeatureFlags";

interface PlayWorldHeaderMainMenuProps {
  userId: string | undefined;
  worldId: string | undefined;
  worldData: WorldData | undefined;
}

export function PlayWorldHeaderMainMenu({
  userId,
  worldId,
  worldData,
}: PlayWorldHeaderMainMenuProps) {
  const { data: userProfile, isLoading: profileLoading } =
    useUserProfileData(userId);

  const {
    toggleEnvironmentSettingsPanel,
    toggleDevSettingsPanel,
    toggleEntitiesPanel,
  } = useEditorUIActions();
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Feature flag for entities panel
  const entitiesPanelEnabled = useFeatureFlag("builderUiEntitiesPanel");

  const isWorldPublished = Boolean(worldData?.isPublic);

  const {
    isDeleting,
    showDeleteConfirm,
    handleDeleteWorld: deleteWorld,
    openDeleteConfirm,
    closeDeleteConfirm,
  } = useDeleteWorld({
    onSuccess: () => {
      // Redirect to home page after deletion
      window.location.href = "/";
    },
  });

  const createInviteLink = () => {
    if (!worldId) throw new Error("World ID is required");
    return `${window.location.origin}/play/${worldId}`;
  };

  const handleCopyInviteLink = async () => {
    if (!worldId) return;
    try {
      const inviteLink = createInviteLink();
      await navigator.clipboard.writeText(inviteLink);

      // Track world shared event for copy invite link
      if (userId) {
        trackWorldShared(worldId, inviteLink, {
          share_method: "copy_link",
          world_name: worldData?.displayName,
        });
      }

      toast.success("Invite link copied to clipboard");
      console.debug("🔗 Invite link copied to clipboard");
    } catch (error) {
      console.error("💣 Error copying invite link:", error);
    }
  };

  const handleShare = async () => {
    if (!worldId) return;
    try {
      const shareData = {
        title: worldData?.displayName || "Check out this world",
        text: "Join me in this amazing world!",
        url: createInviteLink(),
      };

      const hasNativeShare = typeof navigator.share === "function";

      // Track world shared event
      if (userId) {
        trackWorldShared(worldId, shareData.url, {
          share_method: hasNativeShare ? "native_share" : "clipboard",
          world_name: worldData?.displayName,
        });
      }

      if (hasNativeShare) {
        await navigator.share(shareData);
      } else {
        await navigator.clipboard.writeText(shareData.url);
        console.debug("🔗 World URL copied to clipboard");
      }
    } catch (error) {
      console.error("💣 Error sharing world:", error);
    }
  };

  const handleEnvironmentSettings = () => {
    if (!worldId) {
      console.error("💣 World ID is required");
      return;
    }

    toggleEnvironmentSettingsPanel();
  };

  const handleDevSettings = () => {
    if (!worldId) {
      console.error("💣 World ID is required");
      return;
    }

    toggleDevSettingsPanel();
  };

  const handleEntities = () => {
    if (!worldId) {
      console.error("💣 World ID is required");
      return;
    }

    toggleEntitiesPanel();
  };

  const handleDeleteWorld = () => {
    if (worldId && userId) {
      deleteWorld(worldId, userId);
    }
  };

  // Check if user owns this world for conditional menu items
  const isWorldMine = worldData?.ownerId === userId;

  return (
    <>
      <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="hud"
            size="default"
            className="h-10 pointer-events-auto rounded-full"
            aria-label="Home (logo)"
          >
            <img src="/icons/nilo-logo 1.svg" alt="Nilo" className="w-8 h-5" />
            <ChevronDownIcon
              className="text-white transition-transform duration-200"
              width={3}
            />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="ml-4 w-64 p-2 rounded-nilo-lg shadow-[0px_4px_4px_0px_rgba(0,0,0,0.25)] border-2 border-nilo-border-secondary"
          sideOffset={8}
          onCloseAutoFocus={(e) => {
            // Prevent Radix from returning focus to the trigger
            // Fixes button being focused when the dropdown is closed
            e.preventDefault();
          }}
        >
          {/* User Profile Section */}
          <DropdownMenuItem asChild>
            <Link
              to={`/user/${userId}`}
              className="w-full inline-flex justify-start items-center gap-3"
            >
              {profileLoading ? (
                <div className="w-8 h-8 rounded-full bg-nilo-fill-secondary animate-pulse" />
              ) : (
                <Avatar
                  src={userProfile?.avatarUrl}
                  alt={userProfile?.username ?? "User"}
                  className="w-8 h-8 rounded-full object-cover"
                />
              )}
              <span className="text-nilo-text-secondary text-base font-normal font-nilo-primary leading-normal">
                {userProfile?.displayName || userProfile?.username || "User"}
              </span>
            </Link>
          </DropdownMenuItem>

          <DropdownMenuSeparator className="bg-nilo-fill-secondary" />

          {/* Navigation Items */}
          <DropdownMenuItem asChild>
            <Link
              to="/"
              className="w-full inline-flex justify-start items-center gap-3"
              aria-label="Explore Worlds"
            >
              <CompassIcon className="w-4 h-4 text-nilo-icon-secondary" />
              <span className="text-nilo-text-secondary text-base font-normal font-nilo-primary leading-normal">
                Explore Worlds
              </span>
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem onClick={handleCopyInviteLink}>
            <CopyIcon className="w-4 h-4 text-nilo-icon-secondary" />
            <span className="text-nilo-text-secondary text-base font-normal font-nilo-primary leading-normal">
              Copy Invite Link
            </span>
          </DropdownMenuItem>

          <DropdownMenuItem onClick={handleShare}>
            <ShareIcon className="w-4 h-4 text-nilo-icon-secondary" />
            <span className="text-nilo-text-secondary text-base font-normal font-nilo-primary leading-normal">
              Share
            </span>
          </DropdownMenuItem>

          {isWorldMine && (
            <>
              <DropdownMenuSeparator className="bg-nilo-fill-secondary" />
              <DropdownMenuItem onClick={handleEnvironmentSettings}>
                <SettingsIcon className="w-4 h-4 text-nilo-icon-secondary" />
                <span className="text-nilo-text-secondary text-base font-normal font-nilo-primary leading-normal">
                  Environment Settings
                </span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDevSettings}>
                <CodeIcon className="w-4 h-4 text-nilo-icon-secondary" />
                <span className="text-nilo-text-secondary text-base font-normal font-nilo-primary leading-normal">
                  Dev Settings
                </span>
              </DropdownMenuItem>
              {entitiesPanelEnabled && (
                <DropdownMenuItem onClick={handleEntities}>
                  <TreePine className="w-4 h-4 text-nilo-icon-secondary" />
                  <span className="text-nilo-text-secondary text-base font-normal font-nilo-primary leading-normal">
                    Entities
                  </span>
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onSelect={(e) => {
                  // Prevent any default behavior
                  e.preventDefault();
                  // Close the dropdown menu first
                  setDropdownOpen(false);
                  // Then open the modal after a delay to ensure proper cleanup
                  setTimeout(() => {
                    openDeleteConfirm();
                  }, 100);
                }}
                disabled={isDeleting || isWorldPublished}
              >
                <TrashIcon className="w-4 h-4 text-red-500" />
                <span className="text-nilo-text-secondary text-base font-normal font-nilo-primary leading-normal">
                  {isDeleting ? "Deleting..." : "Delete World"}
                </span>
              </DropdownMenuItem>
            </>
          )}
          <DropdownMenuItem asChild>
            <a
              href="https://discord.gg/p3GM48mrXt"
              target="_blank"
              rel="noopener noreferrer"
              className="w-full inline-flex justify-start items-center gap-3"
            >
              <MessageCircleIcon className="w-4 h-4 text-violet-500" />
              <span className="text-violet-500 text-base font-normal font-nilo-primary leading-normal">
                Join Our Discord!
              </span>
            </a>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <ConfirmationModal
        open={showDeleteConfirm}
        onOpenChange={closeDeleteConfirm}
        title="Delete World"
        description="Are you sure you want to delete this world?"
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteWorld}
        loading={isDeleting}
        variant="destructive"
      />
    </>
  );
}
