import { expect, PlaywrightTestArgs } from "@playwright/test";
import {
  getEntitiesCount,
  getSceneChildrenCount,
  TestWindow,
  poll,
  roomTest,
} from "./utils";

type TestOptions = {
  paths: string[];
};

const testFn = async ({ page, paths }: PlaywrightTestArgs & TestOptions) => {
  // Get initial scene children count
  const initialChildrenCount = await page.evaluate(getSceneChildrenCount);
  const initialEntitiesCount = await page.evaluate(getEntitiesCount);

  //wait for physics to start, in other case adding MeshEntity does not work
  await poll(page, () => (window as unknown as TestWindow).Client.physics, {
    timeout: 30_000,
  }).toBeTruthy();

  await page.evaluate(async (paths) => {
    const w = window as unknown as TestWindow;

    const files: File[] = [];

    for (let i = 0; i < paths.length; i += 1) {
      const modelUrl = new URL(paths[i], w.location.origin);
      const extension = w.getFileExtensionFromURL(modelUrl.toString());
      const data = await fetch(modelUrl);
      files.push(new File([await data.blob()], `test.${extension}`));
    }

    await w.Client.userEntity.dropImport({
      clientX: w.innerWidth * 0.5,
      clientY: w.innerHeight * 0.5,
      dataTransfer: { files, getData: () => null },
    });
  }, paths);

  // Check if a new child was added to the scene
  let childrenCount = await page.evaluate(getSceneChildrenCount);
  let entitiesCount = await page.evaluate(getEntitiesCount);

  // +2 means here +1 for entity and +1 since TransformControls appeared after newly create entity was selected
  expect(childrenCount).toBe(initialChildrenCount + 2);
  expect(entitiesCount).toBe(initialEntitiesCount + 1);

  await page.keyboard.down("Control");
  await page.keyboard.press("KeyZ");
  await page.keyboard.up("Control");

  // Check if a new child was added to the scene
  childrenCount = await page.evaluate(getSceneChildrenCount);
  entitiesCount = await page.evaluate(getEntitiesCount);

  expect(childrenCount).toBe(initialChildrenCount);
  expect(entitiesCount).toBe(initialEntitiesCount);

  await page.keyboard.down("Control");
  await page.keyboard.down("Shift");
  await page.keyboard.press("KeyZ");
  await page.keyboard.up("Shift");
  await page.keyboard.up("Control");

  // Check if a new child was added to the scene
  childrenCount = await page.evaluate(getSceneChildrenCount);
  entitiesCount = await page.evaluate(getEntitiesCount);

  expect(childrenCount).toBe(initialChildrenCount + 2);
  expect(entitiesCount).toBe(initialEntitiesCount + 1);

  console.info(`✅ Model importing from ${paths[0]} file works`);
};

// https://playwright.dev/docs/test-parameterize

const testGLB = roomTest.extend<TestOptions>({
  paths: [["/assets/models/test.glb"], { option: true }],
});

const testFBX = roomTest.extend<TestOptions>({
  paths: [["/assets/models/test.fbx"], { option: true }],
});

const testOBJ = roomTest.extend<TestOptions>({
  paths: [
    [
      "/assets/models/obj/Capybara_Quad.obj",
      "/assets/models/obj/Capybara_Quad.mtl",
      "/assets/models/obj/Capybara_Quad_Roughness.png",
      "/assets/models/obj/Capybara_Quad_Normal.png",
      "/assets/models/obj/Capybara_Quad_Diffuse.png",
    ],
    { option: true },
  ],
});

testGLB("importing a 3D model from glb file", testFn);
testFBX("importing a 3D model from fbx file", testFn);
testOBJ("importing a 3D model from obj/mtl files", testFn);
