import { FieldValue, Timestamp } from "firebase-admin/firestore";
import {
  DbCollections,
  DbMaintenance,
  ScheduleMaintenanceRequest,
  ScheduleMaintenanceResponse,
  StopMaintenanceRequest,
  StopMaintenanceResponse,
  StartMaintenanceRequest,
  StartMaintenanceResponse,
} from "@nilo/firebase-schema";
import { Logger } from "../logger";
import { collection } from "../typedFirebase";
import { onCall } from "./utils";

/**
 * Callable function to request a new maintenance.
 * Creates a new maintenance document with "requested" status.
 */
export const scheduleMaintenance = onCall(
  ScheduleMaintenanceRequest,
  ScheduleMaintenanceResponse,
  { auth: "admin" },
  async (request): Promise<ScheduleMaintenanceResponse> => {
    const logger = Logger.create({ userId: request.auth.uid });
    const { delaySeconds, commit, tag } = request.data;

    logger.info("📝 Creating maintenance request", {
      userId: request.auth.uid,
      delaySeconds,
      commit,
      tag,
    });

    try {
      // Calculate scheduledAt timestamp
      const scheduledAtMillis =
        Timestamp.now().toMillis() + delaySeconds * 1000;

      // Create the maintenance document
      const maintenanceData = {
        createdAt: FieldValue.serverTimestamp(),
        status: "scheduled" as const,
        scheduledAt: Timestamp.fromMillis(scheduledAtMillis),
        delaySeconds,
        userId: request.auth.uid,
        ...(commit && { releaseCommitSha: commit }),
        ...(tag && { releaseTag: tag }),
      };

      // Create the document
      const maintenancesCollection = collection<DbMaintenance>(
        DbCollections.maintenances
      );

      const docRef = await maintenancesCollection.add(maintenanceData);

      logger.info("✅ Maintenance request created", {
        maintenanceId: docRef.id,
        userId: request.auth.uid,
      });

      return {
        maintenanceId: docRef.id,
        status: "success",
      };
    } catch (error) {
      logger.error("❌ Failed to create maintenance request", {
        userId: request.auth.uid,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }
);

/**
 * Callable function to stop an existing maintenance.
 * Updates the maintenance document with the final status and optional message.
 */
export const stopMaintenance = onCall(
  StopMaintenanceRequest,
  StopMaintenanceResponse,
  { auth: "admin" },
  async (request): Promise<StopMaintenanceResponse> => {
    const logger = Logger.create({ userId: request.auth.uid });
    const { maintenanceId, reason, message } = request.data;

    // Default to "completed" if no reason provided
    const finalReason = reason || "completed";

    logger.info("🛑 Stopping maintenance", {
      maintenanceId,
      userId: request.auth.uid,
      reason: finalReason,
      message,
    });

    try {
      // Get the maintenance document reference
      const maintenancesCollection = collection<DbMaintenance>(
        DbCollections.maintenances
      );
      const maintenanceRef = maintenancesCollection.doc(maintenanceId);

      // Check if the document exists
      const maintenanceDoc = await maintenanceRef.get();
      if (!maintenanceDoc.exists) {
        throw new Error(`Maintenance with ID ${maintenanceId} not found`);
      }

      const currentData = maintenanceDoc.data() as DbMaintenance;

      // Check if maintenance is already in a final state
      if (["completed", "cancelled", "failed"].includes(currentData.status)) {
        throw new Error(`Maintenance is already ended: ${currentData.status}`);
      }

      // Update the document
      await maintenanceRef.update({
        status: finalReason,
        endedAt: FieldValue.serverTimestamp(),
        message,
      });

      logger.info("✅ Maintenance stopped successfully", {
        maintenanceId,
        userId: request.auth.uid,
        finalStatus: finalReason,
        message,
      });

      return {
        maintenanceId,
        status: "success",
        reason: finalReason,
      };
    } catch (error) {
      logger.error("❌ Failed to stop maintenance", {
        maintenanceId,
        userId: request.auth.uid,
        reason: finalReason,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }
);

/**
 * Callable function to start a scheduled maintenance.
 * Sets the status to "running" and records the start time.
 */
export const startMaintenance = onCall(
  StartMaintenanceRequest,
  StartMaintenanceResponse,
  { auth: "admin" },
  async (request): Promise<StartMaintenanceResponse> => {
    const logger = Logger.create({ userId: request.auth.uid });
    const { maintenanceId } = request.data;

    logger.info("▶️ Starting maintenance", {
      maintenanceId,
      userId: request.auth.uid,
    });

    try {
      // Get the maintenance document reference
      const maintenancesCollection = collection<DbMaintenance>(
        DbCollections.maintenances
      );
      const maintenanceRef = maintenancesCollection.doc(maintenanceId);

      // Check if the document exists
      const maintenanceDoc = await maintenanceRef.get();
      if (!maintenanceDoc.exists) {
        return {
          maintenanceId,
          status: "error",
          message: `Maintenance with ID ${maintenanceId} not found`,
        };
      }

      const currentData = maintenanceDoc.data() as DbMaintenance;

      // Check if maintenance is already running
      if (currentData.status === "running") {
        logger.info("✅ Maintenance is already running", {
          maintenanceId,
          userId: request.auth.uid,
        });
        return {
          maintenanceId,
          status: "already_running",
          message: "Maintenance is already running",
        };
      }

      // Check if maintenance can be started (must be scheduled)
      if (currentData.status !== "scheduled") {
        const errorMessage = `Cannot start maintenance with status "${currentData.status}". Maintenance must be in "scheduled" status to be started.`;
        logger.warn("❌ Cannot start maintenance", {
          maintenanceId,
          userId: request.auth.uid,
          currentStatus: currentData.status,
          errorMessage,
        });
        return {
          maintenanceId,
          status: "error",
          message: errorMessage,
        };
      }

      // Start the maintenance
      await maintenanceRef.update({
        status: "running",
        startedAt: FieldValue.serverTimestamp(),
        delaySeconds: 0,
      });

      logger.info("✅ Maintenance started successfully", {
        maintenanceId,
        userId: request.auth.uid,
      });

      return {
        maintenanceId,
        status: "success",
        message: "Maintenance started successfully",
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("❌ Failed to start maintenance", {
        maintenanceId,
        userId: request.auth.uid,
        error: errorMessage,
      });
      return {
        maintenanceId,
        status: "error",
        message: `Failed to start maintenance: ${errorMessage}`,
      };
    }
  }
);
