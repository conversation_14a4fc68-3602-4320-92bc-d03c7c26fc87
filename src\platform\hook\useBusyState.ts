import { useState } from "react";

/**
 * Hook for managing busy state and error tracking for single actions.
 *
 * This hook is designed for scenarios where you need to track the state of
 * a single async operation at a time, providing easy busy/error tracking.
 * It automatically manages loading states and error handling for async actions.
 *
 * @example
 * ```tsx
 * const MyComponent = () => {
 *   const { busyReason, error, performBusyAction } = useBusyState();
 *
 *   const handleSave = async () => {
 *     await performBusyAction('Saving data', async () => {
 *       await saveData();
 *     });
 *   };
 *
 *   return (
 *     <div>
 *       <button onClick={handleSave} disabled={!!busyReason}>
 *         {busyReason || 'Save'}
 *       </button>
 *       {error && <div>Error: {error.message}</div>}
 *     </div>
 *   );
 * };
 * ```
 *
 * @returns Object containing:
 * - `busyReason`: String describing the current action, null if no action is running
 * - `error`: Error object if the last action failed, null otherwise
 * - `performBusyAction`: Function to wrap async actions with automatic state management
 */
export const useBusyState = () => {
  const [busyReason, setBusyReason] = useState<string | null>(null);
  const [error, setError] = useState<Error | null>(null);

  const performBusyAction = async <T>(
    reason: string,
    action: () => Promise<T>
  ): Promise<T> => {
    setBusyReason(reason);
    setError(null);

    try {
      return await action();
    } catch (error) {
      setError(error as Error);
      throw error;
    } finally {
      setBusyReason(null);
    }
  };

  return {
    busyReason,
    isBusy: !!busyReason,
    error,
    hasError: !!error,
    performBusyAction,
  };
};
