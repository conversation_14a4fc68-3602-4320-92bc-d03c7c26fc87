import { readFile } from "node:fs/promises";
import * as x509 from "@peculiar/x509";
import { getLogger } from "@nilo/logger";

const logger = getLogger();

export interface CertificateResponse {
  cert: string;
  key: string;
  fingerprint: string;
}

export async function generateWTCertificates(): Promise<CertificateResponse> {
  const alg = {
    name: "ECDSA",
    namedCurve: "P-256",
    hash: "SHA-256",
    publicExponent: new Uint8Array([1, 0, 1]),
    modulusLength: 2048, // Standard key length for HTTPS
  };

  // Step 1: Generate a private key and public key pair
  const keys = await crypto.subtle.generateKey(alg, true, ["sign", "verify"]);

  const validFrom = new Date();
  const validTo = new Date();
  validTo.setDate(validFrom.getDate() + 14); // per spec only 14 days allowed

  // Step 2: Create a self-signed X.509 certificate
  const cert = await x509.X509CertificateGenerator.createSelfSigned({
    serialNumber: "01",
    name: "CN=Test",
    notBefore: validFrom,
    notAfter: validTo,
    signingAlgorithm: {
      name: "SHA256",
    },
    keys,
    extensions: [
      new x509.BasicConstraintsExtension(false),
      new x509.ExtendedKeyUsageExtension(
        ["1.*******.6.7", "*******.6.7.8"],
        true
      ),
      new x509.KeyUsagesExtension(
        x509.KeyUsageFlags.keyCertSign |
          x509.KeyUsageFlags.digitalSignature |
          x509.KeyUsageFlags.nonRepudiation |
          x509.KeyUsageFlags.keyEncipherment |
          x509.KeyUsageFlags.dataEncipherment,
        true
      ),
      await x509.SubjectKeyIdentifierExtension.create(keys.publicKey),
    ],
  });

  const privateKeyBuffer = await crypto.subtle.exportKey(
    "pkcs8",
    keys.privateKey
  );

  const privateKey = [
    "-----BEGIN PRIVATE KEY-----",
    Buffer.from(privateKeyBuffer).toString("base64"),
    "-----END PRIVATE KEY-----",
  ].join("\n");

  const publicKey = cert.toString("pem");

  const fingerprintBuffer = await cert.getThumbprint("SHA-256");
  const fingerprint = Array.from(Buffer.from(fingerprintBuffer))
    .map((b) => b.toString(16).padStart(2, "0"))
    .join(":");

  return {
    cert: publicKey,
    key: privateKey,
    fingerprint,
  };
}

export async function loadCertificateChain(
  certPath: string,
  keyPath: string
): Promise<CertificateResponse> {
  const [cert, key] = await Promise.all([
    readFile(certPath, "utf-8"),
    readFile(keyPath, "utf-8"),
  ]);

  const certObj = new x509.X509Certificate(cert);
  const fingerprintBuffer = await certObj.getThumbprint("SHA-256");
  const fingerprint = Array.from(Buffer.from(fingerprintBuffer))
    .map((b) => b.toString(16).padStart(2, "0"))
    .join(":");
  logger.debug("Loaded certificate with fingerprint", fingerprint);

  return {
    cert,
    key,
    fingerprint,
  };
}
