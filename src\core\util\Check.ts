function logError(fnname: string, message: string): void {
  console.error(`🔥 ${fnname} failed: ${message}`);
}

export function checkUnreachable<T>(value: T): void {
  logError("checkUnreachable", `Didn't expect to get here: ${value}`);
}

export function check(condition: boolean, message: string): boolean {
  if (!condition) {
    logError("check", message);
    return false;
  }
  return true;
}

export function checkEqual<T>(a: T, b: T, message: string): boolean {
  if (a !== b) {
    logError("checkEqual", message);
    return false;
  }
  return true;
}

export function checkTrue(condition: boolean, message: string): boolean {
  if (!condition) {
    logError("checkTrue", message);
    return false;
  }
  return true;
}

export function checkFalse(condition: boolean, message: string): boolean {
  if (condition) {
    logError("checkFalse", message);
    return false;
  }
  return true;
}

export function checkNotNull<T>(value: T | null, message: string): value is T {
  if (value === null) {
    logError("checkNotNull", message);
    return false;
  }
  return true;
}

export function checkNotUndefined<T>(
  value: T | undefined,
  message: string
): value is T {
  if (value === undefined) {
    logError("checkNotUndefined", message);
    return false;
  }
  return true;
}

export function checkNotEmpty<T>(
  value: T | null | undefined | "",
  message: string
): value is T {
  if (value === null || value === undefined || value === "") {
    logError("checkNotEmpty", message);
    return false;
  }
  return true;
}

export function checkNull<T>(value: T | null, message: string): value is null {
  if (value !== null) {
    logError("checkNull", message);
    return false;
  }
  return true;
}

export function checkUndefined<T>(
  value: T | undefined,
  message: string
): value is undefined {
  if (value !== undefined) {
    logError("checkUndefined", message);
    return false;
  }
  return true;
}
