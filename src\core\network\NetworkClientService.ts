import { ByteStreamWriter } from "@evenstar/byteform";
import { DeserializeService } from "./DeserializeService";
import {
  ChannelStats,
  Client,
  MessageSchema,
  NetworkStats,
  ProviderType,
} from "@nilo/network";
import { Service, World } from "@nilo/ecs";

export class NetworkClientService extends Service {
  private readonly _channelStats: ChannelStats[];

  private _currentPing: number = 0.0;
  private _lastPingSent: number = 0.0;

  public constructor(
    world: World,
    private _client: Client
  ) {
    // add this service to the world
    super(world);

    // grab the DeserializeService
    const deserializeSystem = world.getService(DeserializeService);
    if (!deserializeSystem) {
      throw new Error("expected DeserializeService to be available");
    }

    // extend DeserializeService router with self logic
    const router = DeserializeService.ROUTER.extend<NetworkClientService>(
      deserializeSystem
    ).addHandler(MessageSchema.Ping, (client, _msg) => {
      client._currentPing = performance.now() - client._lastPingSent;
    });

    // register callbacks to process incoming network messages
    const onData = router.route(this);
    const { reliableOrdered, unreliableUnordered } = this._client.channels;
    if (this._client.provider === ProviderType.WebSocket) {
      reliableOrdered.onReceive(onData);
    } else {
      reliableOrdered.onReceive(onData);
      unreliableUnordered.onReceive(onData);
    }

    // retrieve channel stats once for later outside retrieval
    this._channelStats = Object.values(this._client.channels).map(
      (channel) => channel.stats
    );
  }

  /** Returns the current ping in milliseconds.  */
  public get ping() {
    return this._currentPing;
  }

  /** Gets this network client's overall network stats. */
  public get stats(): NetworkStats {
    return this._client.stats;
  }

  /** Retrieves a list of all channel statistics. */
  public get channelStats(): ChannelStats[] {
    return this._channelStats;
  }

  /** Gets the network client's provider type. */
  public get provider() {
    return this._client.provider;
  }

  /** Sends a ping message and starts the ping timer.*/
  public sendPing() {
    this._lastPingSent = performance.now();
    this.send(MessageSchema.Ping, {});
  }

  private _writeBuf: ByteStreamWriter = new ByteStreamWriter(256, {
    maxByteLength: 64 * 1024, // 64KiB *should* be plenty
  });

  public send<T>(schema: MessageSchema<T>, msg: T) {
    const data = schema.serialize(this._writeBuf, msg);
    this._client.channels.reliableOrdered.send(data);
  }
}
