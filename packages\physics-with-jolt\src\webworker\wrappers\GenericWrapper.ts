import * as Comlink from "comlink";
import { Vector3<PERSON>ike, Quaternion<PERSON><PERSON> } from "three";

import {
  DynamicProperties,
  DynamicProperty,
  DynamicPropertyK<PERSON>,
  GenericControlApi,
} from "../../types/webworker";
import { getDefaultValueForDynamicProperty } from "../webworker_utils";

export class GenericWrapper {
  private _dynamicProperties: DynamicProperties = {};

  private genericApi: Comlink.Remote<GenericControlApi>;

  constructor(genericApi: Comlink.Remote<GenericControlApi>) {
    this.genericApi = genericApi;
  }

  public addActiveDynamicProperties(dynamicProperties: DynamicPropertyKey[]) {
    this.genericApi.addActiveDynamicProperties(dynamicProperties);
  }

  public removeActiveDynamicProperties(
    dynamicProperties: DynamicPropertyKey[]
  ) {
    this.genericApi.removeActiveDynamicProperties(dynamicProperties);
  }

  public updateDynamicProperties(dynamicProperties: DynamicProperties) {
    this._dynamicProperties = dynamicProperties;
  }

  public getVector3DynamicProperty(
    dynamicProperty: DynamicPropertyKey
  ): Vector3<PERSON>ike {
    return this.getDynamicProperty(dynamicProperty) as Vector3Like;
  }

  public getQuaternionDynamicProperty(
    dynamicProperty: DynamicPropertyKey
  ): QuaternionLike {
    return this.getDynamicProperty(dynamicProperty) as QuaternionLike;
  }

  public getNumberDynamicProperty(dynamicProperty: DynamicPropertyKey): number {
    return this.getDynamicProperty(dynamicProperty) as number;
  }

  public getBooleanDynamicProperty(
    dynamicProperty: DynamicPropertyKey
  ): boolean {
    return this.getDynamicProperty(dynamicProperty) as boolean;
  }

  public getDynamicProperty(
    dynamicProperty: DynamicPropertyKey
  ): DynamicProperty {
    const value = this._dynamicProperties[dynamicProperty];
    if (!value) {
      // Get default value
      return getDefaultValueForDynamicProperty(dynamicProperty);
    }
    return value;
  }
}
