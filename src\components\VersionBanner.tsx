import { useEffect, useState } from "react";
import { Timestamp } from "firebase/firestore";
import {
  Home,
  Tag,
  Eye,
  GitGraph,
  BrushCleaning,
  TestTubeDiagonal,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useMaintenanceContext } from "@/providers/maintenance-provider";
import {
  isStable,
  isMain,
  isRelease,
  isPreview,
  isDevelopment,
} from "@/config/environment";
import { urlParams } from "@/utils/urlParams";
import { formatTimeRemaining } from "@/utils/formatTime";

export function VersionBanner() {
  const { maintenanceInfo } = useMaintenanceContext();
  const [countdown, setCountdown] = useState<string>("");

  useEffect(() => {
    if (!maintenanceInfo?.scheduledAt) return;

    const updateCountdown = () => {
      const currentTime = Timestamp.now().seconds;
      const scheduledTime = maintenanceInfo.scheduledAt!.seconds;

      const timeUntilMaintenance = Math.max(0, scheduledTime - currentTime);
      const isExpired = timeUntilMaintenance <= 0;

      if (isExpired) {
        setCountdown("Starting soon...");
      } else {
        setCountdown(formatTimeRemaining(timeUntilMaintenance));
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [maintenanceInfo]);

  const getStateInfo = () => {
    const commitSha = process.env.VERCEL_GIT_COMMIT_SHA;
    const prId = process.env.VERCEL_GIT_PULL_REQUEST_ID;
    const commitRef = process.env.VERCEL_GIT_COMMIT_REF;
    const gitTag = process.env.NILO_GIT_TAG;
    const shortSha = commitSha ? commitSha.substring(0, 8) : "";

    // Check for maintenance state first
    if (maintenanceInfo) {
      const getMaintenanceText = () => {
        if (maintenanceInfo.status === "running") {
          return "Maintenance in progress";
        }
        if (maintenanceInfo.status === "scheduled") {
          return countdown || "Maintenance scheduled";
        }
        return "System maintenance";
      };

      return {
        icon: BrushCleaning,
        bgColor: "rgba(234, 179, 8, 0.6)", // yellow-500/10
        text: getMaintenanceText(),
        clickable: true,
      };
    }

    if (urlParams.showVersion) {
      if (isDevelopment) {
        return {
          icon: GitGraph,
          bgColor: "rgba(244, 114, 182, 0.1)", // pink-400/10
          text: "dev",
          clickable: false,
        };
      }

      if (isStable) {
        // Show tag version if available, otherwise fall back to commit SHA
        const versionText = gitTag
          ? gitTag
          : `prod${shortSha ? `/${shortSha}` : ""}`;

        return {
          icon: Tag,
          bgColor: "rgba(113, 113, 122, 0.1)", // nilo-fill-tertiary-hover/10 equivalent
          text: versionText,
          clickable: false,
        };
      }

      if (isMain) {
        return {
          icon: Home,
          bgColor: "rgba(34, 197, 94, 0.1)", // green-500/10
          text: `main${shortSha ? `/${shortSha}` : ""}`,
          clickable: false,
        };
      }

      if (isRelease) {
        const parts = [commitRef, prId].filter(Boolean).join(" | ");
        return {
          icon: TestTubeDiagonal,
          bgColor: "rgba(96, 165, 250, 0.1)", // blue-400/10
          text: parts,
          clickable: false,
        };
      }

      if (isPreview) {
        return {
          icon: Eye,
          bgColor: "rgba(96, 165, 250, 0.1)", // blue-400/10
          text: commitRef
            ? `${commitRef} ${prId ? `#${prId}` : ""}`
            : "preview",
          clickable: false,
        };
      }
    }

    // Fallback to null
    return null;
  };

  const stateInfo = getStateInfo();

  const handleClick = () => {
    if (stateInfo?.clickable) {
      window.location.reload();
    }
  };

  const showBanner = !(
    maintenanceInfo?.status === "running" &&
    location.pathname.startsWith("/maintenance")
  );

  const Icon = stateInfo?.icon;

  return stateInfo && Icon && showBanner ? (
    <div className="fixed bottom-4 left-4 z-50 select-none">
      <Button
        variant="hud"
        size="sm"
        onClick={handleClick}
        disabled={!stateInfo.clickable}
        className="rounded-full flex justify-center items-center gap-2 pointer-events-auto"
        style={{ backgroundColor: stateInfo.bgColor }}
        title={stateInfo.clickable ? stateInfo.text : undefined}
      >
        <Icon className="w-2 h-2" />
        <span className="justify-center text-nilo-text-quartiary text-xs font-medium font-nilo-primary leading-none">
          {stateInfo.text}
        </span>
      </Button>
    </div>
  ) : null;
}
