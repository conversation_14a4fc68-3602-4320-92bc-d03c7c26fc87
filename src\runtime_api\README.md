# Runtime API

This directory contains runtime API classes that provide scripting access to core functionality.

## ApiEntityFactory

Provides methods for creating primitive entities in the scene.

## ApiModelBuilder

Provides methods for generating compound models using AI or predefined shapes.

### Usage Example

```typescript
// Generate a model from a text prompt
const entities = await modelBuilder.generateModel(
  {
    prompt: "a small red house",
    maxShapes: 10,
    position: new Vector3(0, 0, 0),
    groupTag: "my-house",
    glue: true, // Combine shapes into a single mesh
  },
  {
    onStart: () => console.log("Starting generation..."),
    onProgress: (progress, completed, total) => {
      console.log(`Progress: ${progress}% (${completed}/${total})`);
    },
    onCompleted: (entities) => {
      console.log(`Generated ${entities.length} entities`);
    },
    onError: (error) => {
      console.error("Generation failed:", error.message);
    },
  }
);

// Clean up when done
modelBuilder.cleanup();
```
