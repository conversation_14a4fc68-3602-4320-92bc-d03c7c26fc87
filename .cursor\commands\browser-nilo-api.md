We exposed a bunch of our application's api in `window`.

So you can do things like:

```js
const primitiveData = window.createPrimitiveEntityData({
  primitiveType: "Cube",
  positionX: 0,
  positionY: 2,
  positionZ: 0,
  color: 0x00ff00,
});

window.runCommands([
  new window.CreateEntityCommand(primitiveData),
  new window.SelectEntitiesCommand([primitiveData.id], "replace"),
]);
```

and

```js
// Get the actual entity instance and select it
const entity = window.Client.getEntity(entityId);
if (!entity) {
  throw new Error(`Entity not found: ${entityId}`);
}

// Verify selection worked
const selectedEntities = window.Client.userEntity.getSelectedEntities();
console.info("Selected entities:", selectedEntities);

const selectedEntity = selectedEntities[0] as { id: string };
const selectedEntityId = selectedEntity.id;
```

You can easily use this by executing js scripts in the browser.
