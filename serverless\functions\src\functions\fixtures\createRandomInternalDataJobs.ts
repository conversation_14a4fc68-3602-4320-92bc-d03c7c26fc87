import { FieldValue } from "firebase-admin/firestore";
import {
  DbCollections,
  DbInternalDataJob,
  DbInternalDataJobLog,
} from "@nilo/firebase-schema";
import { collection } from "../../typedFirebase";
import { Logger } from "../../logger";
import { FixtureRequest, pickRandom, randomText } from "./types";

function randomLogMessage(): string {
  return randomText(pickRandom([1, 1, 3, 5, 5, 5, 5, 5, 5, 5, 100]));
}

export default async function createRandomInternalDataJobs(
  logger: Logger,
  request: FixtureRequest<"createRandomInternalDataJobs">
) {
  const userId = request.auth.token.uid;
  const internalDataJobsCollection = collection<DbInternalDataJob>(
    DbCollections.internalDataJobs
  );
  const promises: Promise<unknown>[] = [];
  const count = pickRandom(request.data.count ?? { min: 10, max: 10 });
  logger.debug("Creating", count, "internal data jobs");
  for (let i = 0; i < count; i++) {
    const subJobCount = pickRandom(
      request.data.subJobCount ?? { min: 0, max: 3 }
    );
    const internalDataJob = await internalDataJobsCollection.add({
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
      userId,
      status: "pending",
      config: { type: "noop" },
      subJobs: subJobCount,
      message: randomText(5),
    });

    // emit some logs
    const logsCollection = internalDataJob.collection<DbInternalDataJobLog>(
      DbCollections.internalJobLogs
    );
    const logCount = pickRandom(request.data.logCount ?? { min: 0, max: 5 });
    for (let j = 0; j < logCount; j++) {
      promises.push(
        logsCollection.add({
          createdAt: FieldValue.serverTimestamp(),
          level: pickRandom(["info", "warn", "error"]),
          message: randomLogMessage(),
        })
      );
    }

    const subJobsCollection = internalDataJob.collection<DbInternalDataJob>(
      DbCollections.internalSubJobs
    );
    for (let k = 0; k < subJobCount; k++) {
      const subJob = await subJobsCollection.add({
        createdAt: FieldValue.serverTimestamp(),
        updatedAt: FieldValue.serverTimestamp(),
        userId,
        status: "pending",
        config: { type: "noop" },
        message: randomText(5),
      });
      const subLogsCollection = subJob.collection<DbInternalDataJobLog>(
        DbCollections.internalJobLogs
      );
      const subLogCount = pickRandom(
        request.data.subJobLogCount ?? { min: 0, max: 2 }
      );
      for (let l = 0; l < subLogCount; l++) {
        promises.push(
          subLogsCollection.add({
            createdAt: FieldValue.serverTimestamp(),
            level: pickRandom(["info", "warn", "error"]),
            message: randomLogMessage(),
          })
        );
      }
    }
  }
  logger.debug("Waiting for", promises.length, "promises to resolve");
  await Promise.all(promises);
  logger.debug("Promises resolved");
  return { success: true };
}
