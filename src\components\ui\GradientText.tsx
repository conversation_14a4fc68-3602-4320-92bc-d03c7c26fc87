import { type ReactNode } from "react";
import { cn } from "@/lib/utils";

export const GradientText = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => {
  return (
    <span
      className={cn(
        "font-bold text-center bg-clip-text text-transparent",
        "bg-gradient-to-r from-[#5bf085] via-[#8baef8] to-[#5bf085] bg-[length:200%_100%] animate-gradient-slow-delayed",
        className
      )}
    >
      {children}
    </span>
  );
};
