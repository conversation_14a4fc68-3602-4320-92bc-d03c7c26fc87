import { Vector3Like } from "three";
import { BodyGeometry, BodyTransform } from "./Body";

export interface JoltBody {
  id: number;
  transform: BodyTransform;
  type: "body" | "character";
  motionType: "static" | "kinematic" | "dynamic";
  shape: JoltShape | undefined;
}

interface JoltShapeBase {
  type: JoltShapeType;
  offset?: Vector3Like;
}

export interface SphereShape extends JoltShapeBase {
  type: "sphere";
  radius: number;
}

export interface BoxShape extends JoltShapeBase {
  type: "box";
  size: Vector3Like;
}

export interface CapsuleShape extends JoltShapeBase {
  type: "capsule";
  radius: number;
  height: number;
}

export interface CylinderShape extends JoltShapeBase {
  type: "cylinder";
  radius: number;
  height: number;
}

export interface ConvexHullShape extends JoltShapeBase {
  type: "convex";
  geometry: BodyGeometry;
}

export interface MeshShape extends JoltShapeBase {
  type: "mesh";
  geometry: BodyGeometry;
}

export interface MutableCompoundShape extends JoltShapeBase {
  type: "mutable_compound";
  geometry: BodyGeometry;
}

export interface StaticCompoundShape extends JoltShapeBase {
  type: "static_compound";
  geometry: BodyGeometry;
}

export interface EmptyShape extends JoltShapeBase {
  type: "empty";
  geometry: null;
}

export type JoltShapeType =
  | "box"
  | "sphere"
  | "capsule"
  | "cylinder"
  | "convex"
  | "mutable_compound"
  | "static_compound"
  | "rotated_translated"
  | "scaled"
  | "offset_center_of_mass"
  | "mesh"
  | "empty";
export type JoltShape =
  | BoxShape
  | SphereShape
  | CapsuleShape
  | CylinderShape
  | ConvexHullShape
  | MutableCompoundShape
  | StaticCompoundShape
  | MeshShape
  | EmptyShape;
