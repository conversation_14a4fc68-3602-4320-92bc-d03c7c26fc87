import * as Comlink from "comlink";
import { QuaternionLike, Vector3<PERSON>ike } from "three";
import { BodyGeometry, PhysicsBody } from "./Body";
import { CharacterPhysicsSettings } from "./character";
import { JoltBody } from "./debug";
import {
  Matrix4Array,
  PhysicsOptions,
  PhysicsShapeType,
} from "./PhysicsOptions";
import { PhysicsStepInfo } from "./PhysicsStep";
import {
  RagdollPose,
  RagdollPoseWithPositionAndRotation,
  RagdollSettings,
} from "./Ragdoll";

//Dynamic properties exist so that we only send relevant properties back to the main thread at the end of each physics step
//Preventing sending all the properties at each step and enabling us to read them synchronously
export type DynamicProperty = Vector3Like | QuaternionLike | number | boolean;
export type DynamicPropertyKey =
  | "linearVelocity"
  | "angularVelocity"
  | "contactCount"
  | "isActive";
export type DynamicProperties = {
  [key in DynamicPropertyKey]?: DynamicProperty;
};

export interface GenericControlApi {
  addActiveDynamicProperties: (dynamicProperties: DynamicPropertyKey[]) => void;
  removeActiveDynamicProperties: (
    dynamicProperties: DynamicPropertyKey[]
  ) => void;
}

export interface BodyControlApi extends GenericControlApi {
  getBodyIDValue: () => number;
  get physicsBody(): PhysicsBody;

  updatePhysicsBodyOffsetMatrix: (offsetMatrix: Matrix4Array) => void;
  getBodyMass: () => number;
  setPhysicsBodyPosition: (position: Vector3Like) => void;
  setPhysicsBodyRotation: (rotation: QuaternionLike) => void;
  setPhysicsBodyScale: (scale: Vector3Like) => void;

  getWorldTransform: () => {
    position: Vector3Like;
    rotation: QuaternionLike;
  };
  isActive: () => boolean;
  activate: () => void;
  getStatic: () => boolean;
  setStatic: (isStatic: boolean) => void;
  getMotionType: () => "static" | "dynamic" | "kinematic";
  setMotionType: (motionType: "static" | "dynamic" | "kinematic") => void;
  setRestitution: (restitution: number) => void;
  setFriction: (friction: number) => void;
  getSensor: () => boolean;
  setSensor: (isSensor: boolean) => void;
  getGravityFactor: () => number;
  setGravityFactor: (factor: number) => void;
  setLinearVelocity: (velocity: Vector3Like) => void;
  getLinearVelocity: () => Vector3Like;
  addLinearVelocity: (velocity: Vector3Like) => void;
  setAngularVelocity: (angularVelocity: Vector3Like) => void;
  getAngularVelocity: () => Vector3Like;
  addAngularVelocity: (angularVelocity: Vector3Like) => void;
  applyImpulse: (impulse: Vector3Like) => void;
  applyImpulseAtPoint: (impulse: Vector3Like, point: Vector3Like) => void;
  applyForce: (force: Vector3Like) => void;
  applyTorque: (torque: Vector3Like) => void;
  getPhysicsOptions: () => PhysicsOptions;
  updatePhysicsOptions: (newOptions: Partial<PhysicsOptions>) => void;
  removeFromWorld: () => void;
  addToWorld: () => void;
  swapShape: (shapeType: PhysicsShapeType) => Promise<void>;
  destroyBody: () => void;
  getContactCount: () => number;
  setAllowSleeping: (allowSleeping: boolean) => void;
}

export interface RagdollControlApi extends GenericControlApi {
  get ragdollGroupID(): number;
  changeBoneCollision(boneName: string, isActive: boolean): void;
  updatePoseWithOriginal(pose: RagdollPose, deltaTime: number): void;
  getPose(): RagdollPoseWithPositionAndRotation;
  getBodyControls(): {
    [key: string]: Comlink.Remote<BodyControlApi> | BodyControlApi;
  };
  setKinematic(): void;
  setDynamic(): void;
  removeFromPhysicsSystem(): void;
}

export interface CharacterControlApi extends GenericControlApi {
  // TODO: Define character control methods
  setLinearVelocity: (velocity: Vector3Like) => void;
  setPosition: (position: Vector3Like) => void;
  isSupported: () => boolean;
  getPosition: () => Vector3Like;
  getBodyID: () => number;
  dispose: () => void;
  disable: () => void;
  enable: () => void;
  toJSON: () => CharacterControlData | null;
}

export interface CharacterControlData {
  id: number;
  position: Vector3Like;
  rotation: QuaternionLike;
  velocity: Vector3Like;
  isSupported: boolean;
}

export interface JoltPhysicsServiceApi {
  readonly isInitialized: boolean;
  getInitPromise: () => Promise<boolean>;
  startPhysicsLoop: () => void;
  stopPhysicsLoop: () => void;
  isPhysicsLoopRunning: () => boolean;
  getLastStepInfo: () => PhysicsStepInfo;
  getPhysicsFPS: () => number;
  getObjectBodyControlPairs: () => [string, BodyControlApi][];
  registerRagdollForPhysics: (
    ragdollSettings: RagdollSettings
  ) =>
    | Comlink.Remote<RagdollControlApi | undefined>
    | RagdollControlApi
    | undefined;
  registerObjectForPhysics: (
    physicsBody: PhysicsBody,
    geometry: BodyGeometry
  ) => Promise<Comlink.Remote<BodyControlApi> | BodyControlApi | undefined>;
  unregisterObjectFromPhysics: (id: string) => void;
  unregisterRagdollFromPhysics: (ragdollGroupID: number) => void;
  unregisterCharacterFromPhysics: (characterControl: number) => void;
  destroy: () => void;
  findBestShape: (geometry: BodyGeometry) => Promise<PhysicsShapeType>;
  setGravityEnabled: (enabled: boolean) => void;
  getStaticBodiesCount: () => number;
  getDynamicBodiesCount: () => number;
  createCharacterPhysics: (
    characterSettings: Partial<CharacterPhysicsSettings>
  ) => Comlink.Remote<CharacterControlApi> | CharacterControlApi;
  getAllBodies: (getAllShapes: boolean) => JoltBody[];

  getBodyControlById: (
    id: string
  ) => Comlink.Remote<BodyControlApi> | BodyControlApi | null;
}
