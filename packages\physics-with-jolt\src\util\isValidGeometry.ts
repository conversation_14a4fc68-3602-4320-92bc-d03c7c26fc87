import { BodyGeometry } from "../types/Body";
import { PhysicsInvalidGeometryError } from "../types/errors";

export function assertValidGeometry(geometry: BodyGeometry): void {
  const vertices = new Float32Array(geometry.vertices);
  if (vertices.length < 9) {
    throw new PhysicsInvalidGeometryError(
      "Invalid geometry: vertices must have at least 3 points"
    );
  }
  if (vertices.length === 9) {
    if (vertices.every((vertex) => vertex === 0)) {
      // This tests for a geometry with 3 vertices as zero.
      // We have this condition because there are emptyMeshes with 3 vertices as (0, 0, 0).
      throw new PhysicsInvalidGeometryError(
        "Invalid geometry: vertices are all zeros"
      );
    }
  }

  //Contains NaN values
  // Probably not necessary, commented in case we need it later
  // if (vertices.some((vertex) => isNaN(vertex))) {
  //   throw new PhysicsInvalidGeometryError(
  //     "Invalid geometry: vertices contain NaN values"
  //   );
  // }
}

export function isValidGeometry(geometry: BodyGeometry): boolean {
  try {
    assertValidGeometry(geometry);
    return true;
  } catch (error) {
    if (error instanceof PhysicsInvalidGeometryError) {
      return false;
    }
    //Unexpected error
    throw error;
  }
}
