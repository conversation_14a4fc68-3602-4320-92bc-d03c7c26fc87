import { trackEvent } from "./eventUtils";

/**
 * World-specific event tracking utilities
 * These functions handle tracking for world-related actions like creation, publishing, etc.
 */

/**
 * Entry points for world viewing
 */
export type WorldViewEntryPoint = "home" | "link" | "explore";

/**
 * Detect entry point based on current URL and referrer
 */
export function detectEntryPoint(): WorldViewEntryPoint {
  const referrer = document.referrer;

  // If no referrer, assume direct link
  if (!referrer) {
    return "link";
  }

  try {
    const referrerUrl = new URL(referrer);
    const referrerPath = referrerUrl.pathname;

    // If coming from same origin
    if (referrerUrl.origin === window.location.origin) {
      // Coming from root/explore page
      if (referrerPath === "/" || referrerPath === "/explore") {
        return "explore";
      }
      // Coming from lobby/home page
      if (referrerPath === "/lobby") {
        return "home";
      }
      // Coming from user profile, create world, etc.
      return "explore";
    }

    // Coming from external source
    return "link";
  } catch {
    // If referrer URL is malformed, assume direct link
    return "link";
  }
}

/**
 * Track when a world is created
 * @param worldId - The ID of the created world
 * @param properties - Additional properties for the event
 */
export function trackWorldCreated(
  worldId: string,
  properties: Record<string, unknown> = {}
): void {
  trackEvent("world_created", {
    world_id: worldId,
    timestamp: new Date().toISOString(),
    ...properties,
  });
}

/**
 * Track when a world is published (made public)
 * @param worldId - The ID of the published world
 * @param properties - Additional properties for the event
 */
export function trackWorldPublished(
  worldId: string,
  properties: Record<string, unknown> = {}
): void {
  trackEvent("world_published", {
    world_id: worldId,
    timestamp: new Date().toISOString(),
    ...properties,
  });
}

/**
 * Track when a world is remixed (cloned)
 * @param worldId - The ID of the new remixed world
 * @param originalWorldId - The ID of the original world that was remixed
 * @param properties - Additional properties for the event
 */
export function trackWorldRemixed(
  worldId: string,
  originalWorldId: string,
  properties: Record<string, unknown> = {}
): void {
  trackEvent("world_remixed", {
    world_id: worldId,
    original_world_id: originalWorldId,
    timestamp: new Date().toISOString(),
    ...properties,
  });
}

/**
 * Track when a world is viewed/played
 * @param worldId - The ID of the viewed world
 * @param viewerId - The ID of the user viewing the world
 * @param entryPoint - Where the action was executed from (home, link, explore). Auto-detected if not provided.
 * @param properties - Additional properties for the event
 */
export function trackWorldViewed(
  worldId: string,
  viewerId: string,
  entryPoint: WorldViewEntryPoint = detectEntryPoint(),
  properties: Record<string, unknown> = {}
): void {
  trackEvent("world_viewed", {
    world_id: worldId,
    viewer_id: viewerId,
    entry_point: entryPoint,
    timestamp: new Date().toISOString(),
    ...properties,
  });
}

/**
 * Track when a world is shared
 * @param worldId - The ID of the shared world
 * @param url - The URL that was shared
 * @param properties - Additional properties for the event
 */
export function trackWorldShared(
  worldId: string,
  url: string,
  properties: Record<string, unknown> = {}
): void {
  trackEvent("world_shared", {
    world_id: worldId,
    url: url,
    timestamp: new Date().toISOString(),
    ...properties,
  });
}

/**
 * Track when a world is deleted
 * @param worldId - The ID of the deleted world
 * @param properties - Additional properties for the event
 */
export function trackWorldDeleted(
  worldId: string,
  properties: Record<string, unknown> = {}
): void {
  trackEvent("world_deleted", {
    world_id: worldId,
    timestamp: new Date().toISOString(),
    ...properties,
  });
}
