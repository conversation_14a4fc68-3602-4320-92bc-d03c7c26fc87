import { memo } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";

interface JoinWaitlistModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const JoinWaitlistModal = ({ open, onOpenChange }: JoinWaitlistModalProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-bold">
            Join Our Waitlist
          </DialogTitle>
          <DialogDescription className="text-center text-gray-400 mt-2">
            We&apos;re currently in private beta. Join our waitlist to get early
            access to Nilo.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-6">
          <div className="flex justify-center">
            <Button
              className="w-full"
              onClick={() => {
                window.open("https://nilo.io/", "_blank");
                onOpenChange(false);
              }}
            >
              Join Waitlist
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default memo(JoinWaitlistModal);
