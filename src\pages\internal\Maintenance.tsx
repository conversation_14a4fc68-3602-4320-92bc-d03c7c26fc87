import { useState, useEffect } from "react";
import {
  collection,
  CollectionReference,
  limit,
  Timestamp,
  deleteDoc,
  doc,
} from "firebase/firestore";
import {
  Plus,
  Calendar,
  Clock,
  User,
  GitCommit,
  Tag,
  Copy,
  Check,
  Eye,
  CircleStop,
  CirclePlay,
  Trash2,
} from "lucide-react";
import toast from "react-hot-toast";
import { db, bindFunction } from "@/config/firebase";
import { orderByT, queryT, useFirebaseQuery } from "@/hooks/firebaseHooks";
import {
  DbCollections,
  DbMaintenance,
  ScheduleMaintenanceRequest,
  ScheduleMaintenanceResponse,
  StopMaintenanceRequest,
  StopMaintenanceResponse,
  StartMaintenanceRequest,
  StartMaintenanceResponse,
} from "@nilo/firebase-schema";
import { formatTimeRemaining } from "@/utils/formatTime";
import { Typography } from "@/components/ui-nilo/Typography";
import { ModalDialog } from "@/components/ui-nilo/ModalDialog";
import { ConfirmationModal } from "@/components/ui-nilo/ConfirmationModal";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { FirestoreDocumentUrl } from "@/pages/internal/devUrls";

const LIMIT = 50;

const scheduleMaintenance = bindFunction<
  ScheduleMaintenanceRequest,
  ScheduleMaintenanceResponse
>("scheduleMaintenance");

const stopMaintenance = bindFunction<
  StopMaintenanceRequest,
  StopMaintenanceResponse
>("stopMaintenance");

const startMaintenance = bindFunction<
  StartMaintenanceRequest,
  StartMaintenanceResponse
>("startMaintenance");

const formatDate = (
  timestamp?: Timestamp | { toDate?: () => Date; seconds?: number }
) => {
  if (!timestamp) return "N/A";
  // Handle both client and server Timestamp types
  const date = timestamp.toDate
    ? timestamp.toDate()
    : new Date((timestamp.seconds || 0) * 1000);
  return date.toLocaleString();
};

const getStatusVariant = (status: DbMaintenance["status"]) => {
  switch (status) {
    case "requested":
      return "secondary";
    case "scheduled":
      return "default";
    case "running":
      return "default";
    case "completed":
      return "secondary";
    case "cancelled":
      return "secondary";
    case "failed":
      return "destructive";
    default:
      return "secondary";
  }
};

const getStatusIcon = (status: DbMaintenance["status"]) => {
  switch (status) {
    case "requested":
      return "🔵";
    case "scheduled":
      return "⏰";
    case "running":
      return "🏃";
    case "completed":
      return "✅";
    case "cancelled":
      return "🚫";
    case "failed":
      return "❌";
    default:
      return "❓";
  }
};

interface CreateMaintenanceDialogProps {
  commitSha?: string | undefined;
  commitRef?: string | undefined;
}

const CreateMaintenanceDialog = ({
  commitSha,
  commitRef,
}: CreateMaintenanceDialogProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [delaySeconds, setDelaySeconds] = useState("300"); // Default 5 minutes
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    const seconds = parseInt(delaySeconds);
    if (isNaN(seconds) || seconds < 0) {
      alert("Please enter a valid number of seconds");
      return;
    }

    setIsSubmitting(true);
    try {
      const requestData: ScheduleMaintenanceRequest = {
        delaySeconds: seconds,
      };

      // Only add commit and tag if they have values
      if (commitSha) {
        requestData.commit = commitSha;
      }
      if (commitRef) {
        requestData.tag = commitRef;
      }

      await scheduleMaintenance(requestData);

      // Successfully scheduled - just close the modal
      setIsOpen(false);
      setDelaySeconds("300");
    } catch (error) {
      console.error("Failed to create maintenance:", error);
      toast.error(
        `Failed to create maintenance: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Button
        variant="default"
        className="gap-2"
        onClick={() => setIsOpen(true)}
      >
        <Plus className="w-4 h-4" />
        Schedule Maintenance
      </Button>

      <ModalDialog
        open={isOpen}
        onOpenChange={setIsOpen}
        title="Schedule New Maintenance"
        description="Create a new maintenance window with a delay before activation."
        footer={
          <>
            <Button
              variant="ghost"
              onClick={() => setIsOpen(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Maintenance"}
            </Button>
          </>
        }
      >
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="delay">Delay (seconds)</Label>
            <Input
              id="delay"
              type="number"
              value={delaySeconds}
              onChange={(e) => setDelaySeconds(e.target.value)}
              placeholder="Enter delay in seconds"
              min="0"
            />
            <p className="text-sm text-muted-foreground">
              {parseInt(delaySeconds) >= 60 &&
                `≈ ${Math.floor(parseInt(delaySeconds) / 60)} minutes`}
              {parseInt(delaySeconds) >= 3600 &&
                ` (${Math.floor(parseInt(delaySeconds) / 3600)} hours)`}
            </p>
          </div>
          {(commitSha || commitRef) && (
            <div className="space-y-2 rounded-lg bg-muted p-3">
              <p className="text-sm font-medium">Git Information</p>
              {commitRef && (
                <div className="flex items-center gap-2 text-sm">
                  <Tag className="w-3 h-3" />
                  <span className="font-mono">{commitRef}</span>
                </div>
              )}
              {commitSha && (
                <div className="flex items-center gap-2 text-sm">
                  <GitCommit className="w-3 h-3" />
                  <span className="font-mono">{commitSha.substring(0, 8)}</span>
                </div>
              )}
            </div>
          )}
        </div>
      </ModalDialog>
    </>
  );
};

interface StopMaintenanceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  maintenanceId: string | null;
  onMaintenanceStopped?: () => void;
}

const StopMaintenanceModal = ({
  open,
  onOpenChange,
  maintenanceId,
  onMaintenanceStopped,
}: StopMaintenanceModalProps) => {
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleStop = async (reason: "completed" | "cancelled") => {
    if (!maintenanceId) return;

    setIsSubmitting(true);
    try {
      await stopMaintenance({
        maintenanceId,
        reason,
        ...(message.trim() && { message: message.trim() }),
      });

      // Successfully stopped - just close the modal
      setMessage("");
      onOpenChange(false);
      onMaintenanceStopped?.();
    } catch (error) {
      console.error("Failed to stop maintenance:", error);
      toast.error(
        `Failed to stop maintenance: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ModalDialog
      open={open}
      onOpenChange={onOpenChange}
      title="Stop Maintenance"
      description="Choose how to stop this maintenance and optionally provide a message."
      footer={
        <>
          <Button
            variant="ghost"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            variant="outline"
            onClick={() => handleStop("cancelled")}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Cancelling..." : "Cancel Maintenance"}
          </Button>
          <Button
            onClick={() => handleStop("completed")}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Completing..." : "Complete Maintenance"}
          </Button>
        </>
      }
    >
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="stop-message">Message (optional)</Label>
          <textarea
            id="stop-message"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Add a message explaining why this maintenance is being stopped..."
            className="w-full min-h-[80px] px-3 py-2 text-sm bg-background border border-input rounded-md resize-none"
          />
          <p className="text-xs text-muted-foreground">
            This message will be stored with the maintenance record for
            reference.
          </p>
        </div>
      </div>
    </ModalDialog>
  );
};

export default function Maintenance() {
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [stopModalOpen, setStopModalOpen] = useState(false);
  const [maintenanceToStop, setMaintenanceToStop] = useState<string | null>(
    null
  );
  const [startingMaintenanceId, setStartingMaintenanceId] = useState<
    string | null
  >(null);
  const [deletingMaintenanceId, setDeletingMaintenanceId] = useState<
    string | null
  >(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [maintenanceToDelete, setMaintenanceToDelete] = useState<string | null>(
    null
  );
  const [currentTime, setCurrentTime] = useState(Timestamp.now().seconds);

  const git = {
    commitRef: process.env.VERCEL_GIT_COMMIT_REF || undefined,
    commitSha: process.env.VERCEL_GIT_COMMIT_SHA || undefined,
  };

  // Update current time every second for real-time countdown
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Timestamp.now().seconds);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedId(text);
      setTimeout(() => setCopiedId(null), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
      alert("Failed to copy to clipboard");
    }
  };

  const handleStartMaintenance = async (maintenanceId: string) => {
    setStartingMaintenanceId(maintenanceId);
    try {
      const result = await startMaintenance({ maintenanceId });

      if (result.data.status === "success") {
        // Successfully started - no notification needed
      } else if (result.data.status === "already_running") {
        toast.error("Maintenance is already running!");
      } else {
        toast.error(`Failed to start maintenance: ${result.data.message}`);
      }
    } catch (error) {
      console.error("Failed to start maintenance:", error);
      toast.error(
        `Failed to start maintenance: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setStartingMaintenanceId(null);
    }
  };

  const handleDeleteMaintenance = (maintenanceId: string) => {
    setMaintenanceToDelete(maintenanceId);
    setDeleteConfirmOpen(true);
  };

  const confirmDeleteMaintenance = async () => {
    if (!maintenanceToDelete) return;

    setDeletingMaintenanceId(maintenanceToDelete);
    try {
      await deleteDoc(doc(db, DbCollections.maintenances, maintenanceToDelete));
      // Successfully deleted - no notification needed
    } catch (error) {
      console.error("Failed to delete maintenance:", error);
      toast.error(
        `Failed to delete maintenance: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setDeletingMaintenanceId(null);
      setMaintenanceToDelete(null);
    }
  };

  const maintenancesCollection = collection(
    db,
    DbCollections.maintenances
  ) as CollectionReference<DbMaintenance, DbMaintenance>;

  const q = queryT(
    maintenancesCollection,
    orderByT("createdAt", "desc"),
    limit(LIMIT)
  );

  const maintenances = useFirebaseQuery(q);

  return (
    <div className="h-screen bg-black text-icon-primary overflow-y-auto scrollbar-thin-nilo">
      <div className="mx-auto space-y-8 pt-8 pb-24 max-w-6xl">
        {/* Header */}
        <header className="text-center">
          <Typography.Title level={1} color="light" className="mb-4">
            Maintenance Windows
          </Typography.Title>
          <Typography.Body level={1} color="light" weight="medium">
            Manage and monitor system maintenance schedules
          </Typography.Body>
        </header>

        {/* Actions */}
        <div className="flex justify-end">
          <CreateMaintenanceDialog
            commitSha={git.commitSha}
            commitRef={git.commitRef}
          />
        </div>

        {/* Maintenances Table */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Maintenance Records
            </CardTitle>
          </CardHeader>
          <CardContent>
            {maintenances.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-muted-foreground">ID</TableHead>
                    <TableHead className="text-muted-foreground">
                      Status
                    </TableHead>
                    <TableHead className="text-muted-foreground">
                      Created
                    </TableHead>
                    <TableHead className="text-muted-foreground">
                      Scheduled
                    </TableHead>
                    <TableHead className="text-muted-foreground">
                      Countdown
                    </TableHead>
                    <TableHead className="text-muted-foreground">
                      User
                    </TableHead>
                    <TableHead className="text-muted-foreground">
                      Git Info
                    </TableHead>
                    <TableHead className="text-right text-muted-foreground">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {maintenances.map((doc) => {
                    const data = doc.data();
                    const id = doc.id;
                    return (
                      <TableRow key={id}>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <span className="font-mono text-xs text-muted-foreground">
                              {id.substring(0, 8)}...
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={() => copyToClipboard(id)}
                              title="Copy full ID"
                            >
                              {copiedId === id ? (
                                <Check className="h-3 w-3 text-green-500" />
                              ) : (
                                <Copy className="h-3 w-3 text-muted-foreground" />
                              )}
                            </Button>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getStatusVariant(data.status)}>
                            {getStatusIcon(data.status)} {data.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="font-mono text-sm text-foreground">
                          {formatDate(data.createdAt)}
                        </TableCell>
                        <TableCell className="font-mono text-sm text-foreground">
                          {formatDate(data.scheduledAt)}
                        </TableCell>
                        <TableCell>
                          {data.status === "scheduled" ? (
                            (() => {
                              const timeUntilMaintenance = Math.max(
                                0,
                                (data.scheduledAt?.seconds || 0) - currentTime
                              );
                              const isExpired = timeUntilMaintenance <= 0;
                              return (
                                <div
                                  className={`flex items-center gap-1 ${
                                    isExpired
                                      ? "text-destructive"
                                      : "text-foreground"
                                  }`}
                                >
                                  <Clock
                                    className={`w-3 h-3 ${
                                      isExpired ? "" : "animate-pulse"
                                    }`}
                                  />
                                  <span className="font-mono text-sm">
                                    {formatTimeRemaining(timeUntilMaintenance)}
                                  </span>
                                </div>
                              );
                            })()
                          ) : data.status === "running" ? (
                            <Badge variant="default" className="gap-1">
                              <Clock className="w-3 h-3" />
                              Active
                            </Badge>
                          ) : (
                            <div className="flex items-center gap-1 text-muted-foreground">
                              <Clock className="w-3 h-3" />
                              <span className="font-mono text-sm">
                                {data.delaySeconds}s delay
                              </span>
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          {data.userId && (
                            <div className="flex items-center gap-1 text-foreground">
                              <User className="w-3 h-3" />
                              <span className="font-mono text-xs truncate max-w-[100px]">
                                {data.userId}
                              </span>
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {data.releaseTag && (
                              <div className="flex items-center gap-1 text-foreground">
                                <Tag className="w-3 h-3" />
                                <span className="font-mono text-xs">
                                  {data.releaseTag}
                                </span>
                              </div>
                            )}
                            {data.releaseCommitSha && (
                              <div className="flex items-center gap-1 text-foreground">
                                <GitCommit className="w-3 h-3" />
                                <span className="font-mono text-xs">
                                  {data.releaseCommitSha.substring(0, 8)}
                                </span>
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-1">
                            <FirestoreDocumentUrl
                              path={[DbCollections.maintenances, id]}
                            >
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </FirestoreDocumentUrl>
                            {data.status === "scheduled" && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => handleStartMaintenance(id)}
                                disabled={startingMaintenanceId === id}
                                title="Start maintenance"
                              >
                                <CirclePlay className="h-4 w-4" />
                              </Button>
                            )}
                            {!["completed", "cancelled", "failed"].includes(
                              data.status
                            ) && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => {
                                  setMaintenanceToStop(id);
                                  setStopModalOpen(true);
                                }}
                                title="Stop maintenance"
                              >
                                <CircleStop className="h-4 w-4" />
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleDeleteMaintenance(id)}
                              disabled={deletingMaintenanceId === id}
                              title="Delete maintenance"
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No maintenance records found
              </div>
            )}
          </CardContent>
        </Card>

        <StopMaintenanceModal
          open={stopModalOpen}
          onOpenChange={setStopModalOpen}
          maintenanceId={maintenanceToStop}
          onMaintenanceStopped={() => {
            // Could add any refresh logic here if needed
            setMaintenanceToStop(null);
          }}
        />

        <ConfirmationModal
          open={deleteConfirmOpen}
          onOpenChange={setDeleteConfirmOpen}
          title="Delete Maintenance"
          description="Are you sure you want to delete this maintenance? This action cannot be undone."
          confirmText="Delete"
          cancelText="Cancel"
          onConfirm={confirmDeleteMaintenance}
          loading={deletingMaintenanceId !== null}
          variant="destructive"
        />
      </div>
    </div>
  );
}
