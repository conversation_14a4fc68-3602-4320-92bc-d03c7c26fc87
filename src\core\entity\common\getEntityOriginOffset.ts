import { Mesh, Vector3<PERSON><PERSON> } from "three";
import { EntityOriginTypes } from "..";

export function getOriginOffset(
  mesh: Mesh,
  type: EntityOriginTypes
): Vector3Like {
  const zeroVector = {
    x: 0,
    y: 0,
    z: 0,
  };

  if (!mesh) return zeroVector;
  let geometryBox = mesh.geometry.boundingBox;
  if (!geometryBox) {
    mesh.geometry.computeBoundingBox();
    geometryBox = mesh.geometry.boundingBox!;
  }
  switch (type) {
    case EntityOriginTypes.NativeOrigin:
      return zeroVector;
    case EntityOriginTypes.GeometryCenter:
      return {
        x: -(geometryBox.max.x + geometryBox.min.x) * 0.5,
        y: -(geometryBox.max.y + geometryBox.min.y) * 0.5,
        z: -(geometryBox.max.z + geometryBox.min.z) * 0.5,
      };
    case EntityOriginTypes.GeometryBottomCenter:
      return {
        x: -(geometryBox.max.x + geometryBox.min.x) * 0.5,
        y: -geometryBox.min.y,
        z: -(geometryBox.max.z + geometryBox.min.z) * 0.5,
      };
    default:
      throw new Error(`Invalid origin type: ${type}`);
  }
}
