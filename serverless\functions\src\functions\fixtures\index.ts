import { RunFixtureRequest, RunFixtureResponse } from "@nilo/firebase-schema";
import { onCall } from "../utils";
import { Logger } from "../../logger";
import { FixtureRequest } from "./types";
import createRandomInternalDataJobs from "./createRandomInternalDataJobs";

const fixtures: {
  [key in RunFixtureRequest["fixture"]]: (
    logger: Logger,
    request: FixtureRequest<key>
  ) => Promise<RunFixtureResponse>;
} = {
  noop,
  createRandomInternalDataJobs,
};

function runIt<T extends RunFixtureRequest["fixture"]>(
  logger: Logger,
  request: FixtureRequest<T>
) {
  return fixtures[request.data.fixture](logger, request);
}

export const runFixture = onCall(
  RunFixtureRequest,
  RunFixtureResponse,
  { auth: "admin" },
  async (request) => {
    const logger = Logger.create({ fixture: request.data });
    logger.debug("Running fixture", request.data.fixture);
    try {
      return await runIt(logger, request);
    } catch (error) {
      logger.error("Error running fixture", error);
      return { success: false, message: "Error running fixture" };
    }
  }
);

async function noop(_logger: Logger, _request: FixtureRequest<"noop">) {
  return { success: true };
}
