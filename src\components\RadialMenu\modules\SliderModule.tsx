import { motion } from "framer-motion";
import { useEffect, useMemo, useRef } from "react";
import { MathUtils, Vector2 } from "three";
import { useSnapshot } from "valtio";
import { useRadialItem } from "../useRadialItem";
import { getArcAngle } from "../utils";
import { BaseRadialItem, RadialItemType } from "./types";

export interface RadialSliderItem extends BaseRadialItem {
  /** Type of the item */
  type: RadialItemType.Slider;

  /** Angle of the slider, e.g. arc angle */
  angle: number;

  /** Minimum value of the slider */
  min: number;

  /** Maximum value of the slider */
  max: number;

  /** Value of the slider, e.g. progress value */
  value: number;

  /** Step value for the slider */
  step?: number;

  /** Callback function called when the slider value changes */
  onChange: (value: number) => void;

  /** Whether the steps should be rendered or not */
  stepsVisible?: boolean;
}

interface SliderOptions {
  /** Offset from the outer radius of the main menu */
  startRadius: number;

  /** Size of slider module (e.g. outer radius - inner radius) */
  size: number;

  /** Size of the track */
  trackSize: number;

  /** Size of the progress bar */
  progressSize: number;

  /** Size of the handle */
  handleSize: number;

  /** Start angle of the Slider */
  startAngle: number;

  /** End angle of the Slider */
  endAngle: number;
}

const sp1 = new Vector2();
const sp2 = new Vector2();

function generateSliderPanel(opts: SliderOptions) {
  const { startRadius, size, startAngle, endAngle, handleSize } = opts;

  // Generate the outer track as a rounded ARC line with size = size
  const radius = startRadius + size / 2;

  const centerAngle = (startAngle + endAngle) / 2;
  const angleDt = endAngle - startAngle;

  const arcStartAngle = centerAngle - angleDt / 2;
  const arcEndAngle = centerAngle + angleDt / 2;

  const paddingAngle = getArcAngle(handleSize, radius);
  const handleStartAngle = arcStartAngle + paddingAngle / 2;
  const handleEndAngle = arcEndAngle - paddingAngle / 2;

  sp1.set(radius * Math.cos(arcStartAngle), radius * Math.sin(arcStartAngle));

  sp2.set(radius * Math.cos(arcEndAngle), radius * Math.sin(arcEndAngle));

  const trackBg = [
    `M ${sp1.x} ${sp1.y}`,
    `A ${radius} ${radius} ${angleDt} 0 1 ${sp2.x} ${sp2.y}`,
  ];

  const track = [
    `M ${sp1.x} ${sp1.y}`,
    `A ${radius} ${radius} ${angleDt} 0 1 ${sp2.x} ${sp2.y}`,
  ];

  const handle = [
    `M ${radius} ${-handleSize * 0.4}`,
    `L ${radius} ${handleSize * 0.4}`,
  ];

  return {
    trackBg: trackBg.join(" "),
    track: track.join(" "),
    handle: handle.join(" "),
    handleStartAngle: handleStartAngle * MathUtils.RAD2DEG,
    handleEndAngle: handleEndAngle * MathUtils.RAD2DEG,
  };
}
const SLIDER_OFFSET = 20;
const SLIDER_SIZE = 42;

export const RadialSliderModule = ({ item }: { item: RadialSliderItem }) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const ref = useRef<SVGGElement>(null);
  const pathRef = useRef<SVGPathElement>(null);
  const { angle, radius, lock } = useRadialItem();
  const snap = useSnapshot(item);

  const rads = snap.angle * MathUtils.DEG2RAD;
  const startAngle = angle - rads / 2;
  const endAngle = angle + rads / 2;

  const size = (radius + SLIDER_OFFSET + SLIDER_SIZE) * 2; // Size of the slider panel

  const { min, max, step = 0, value, stepsVisible, onChange } = snap;
  const steps = (max - min) / Math.max(step, 0.001);

  const { trackBg, track, handle, handleStartAngle, handleEndAngle } =
    useMemo(() => {
      return generateSliderPanel({
        startRadius: radius + SLIDER_OFFSET,
        startAngle: startAngle,
        endAngle: endAngle,

        size: SLIDER_SIZE,
        trackSize: 10,
        progressSize: 10,
        handleSize: 14,
      });
    }, [radius, startAngle, endAngle]);

  useEffect(() => {
    const svg = svgRef.current;
    const target = pathRef.current;

    if (!svg || !target) return;

    let isDragging = false;

    const center = new Vector2();
    const size = new Vector2();

    const TWO_PI = 2 * Math.PI;
    const norm = (v: number) => ((v % TWO_PI) + TWO_PI) % TWO_PI;

    const clampValue = (dt: number) => {
      let value = min + (max - min) * dt;
      if (step) {
        value = Math.round(value / step) * step;
      }

      return Math.min(Math.max(value, min), max);
    };

    let lastDt = 0.0;
    const updateProgress = (event: PointerEvent) => {
      const rect = svg.getBoundingClientRect();
      const offsetX = event.clientX - rect.left;
      const offsetY = event.clientY - rect.top;

      const cx = size.x / 2;
      const cy = size.y / 2;
      const dx = offsetX - cx;
      const dy = offsetY - cy;

      // Angle in [0, 2π) starting at 3 o'clock (positive X axis), counter-clockwise:
      let angle = Math.atan2(dy, dx);
      if (angle < 0) angle += 2 * Math.PI;

      const a1 = norm(startAngle);
      const a2 = norm(endAngle);

      // Arc length from a1 to a2 going CCW
      const arcLen = (a2 - a1 + TWO_PI) % TWO_PI || TWO_PI;

      // Delta from a1 to the pointer angle, normalized to [0, 2π)
      const delta = (angle - a1 + TWO_PI) % TWO_PI;

      // If delta is larger than the arc length -> outside
      if (delta > arcLen) {
        const next = clampValue(Math.round(lastDt));
        if (next !== item.value) {
          item.value = next;
          onChange(next);
        }
      } else {
        // Progress 0..1 along the arc
        lastDt = delta / arcLen;
        const next = clampValue(lastDt);
        if (next !== item.value) {
          item.value = next;
          onChange(next);
        }
      }
    };

    const onTouchStart = (event: TouchEvent) => {
      event.stopPropagation();
      event.preventDefault();
    };

    const onPointerDown = (event: PointerEvent) => {
      if (event.button !== 0) return; // Only allow left mouse button drag
      event.stopPropagation();

      isDragging = true;

      const rect = svg.getBoundingClientRect();
      center.set(rect.left + rect.width / 2, rect.top + rect.height / 2);
      size.set(rect.width, rect.height);

      updateProgress(event);
      lock(true);
    };

    const onPointerMove = (event: PointerEvent) => {
      if (!isDragging) return;
      updateProgress(event);
    };

    const onPointerUp = () => {
      isDragging = false;
      lock(false);
    };

    target.addEventListener("touchstart", onTouchStart);
    target.addEventListener("pointerdown", onPointerDown);
    window.addEventListener("pointerup", onPointerUp);
    window.addEventListener("pointermove", onPointerMove);

    return () => {
      target.removeEventListener("touchstart", onTouchStart);
      target.removeEventListener("pointerdown", onPointerDown);
      window.removeEventListener("pointerup", onPointerUp);
      window.removeEventListener("pointermove", onPointerMove);
    };
  }, [startAngle, endAngle, item, lock, min, max, step, onChange]);

  const progress = (value - min) / (max - min);
  const dashOffset = progress * 100;
  const handleAngle =
    handleStartAngle + (handleEndAngle - handleStartAngle) * progress;

  return (
    <motion.div className="absolute z-1 w-0 h-0 flex justify-center items-center pointer-events-none touch-none">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
      >
        <svg
          ref={svgRef}
          width={size}
          height={size}
          viewBox={`${-size / 2} ${-size / 2} ${size} ${size}`}
        >
          <g ref={ref}>
            <path
              key={`sub-segment-trackBg`}
              id={`sub-segment-trackBg`}
              ref={pathRef}
              d={trackBg}
              style={{
                strokeWidth: 42,
                strokeLinecap: "round",
                pointerEvents: "stroke",
              }}
              className="fill-none stroke-nilo-fill-tertiary"
            />

            <path
              key={`sub-segment-track`}
              id={`sub-segment-track`}
              d={track}
              style={{
                strokeWidth: 4,
                strokeLinecap: "round",
              }}
              className="fill-none stroke-nilo-fill-secondary"
            />

            {stepsVisible && steps && (
              <path
                key={`sub-segment-steps`}
                id={`sub-segment-steps`}
                d={track}
                pathLength={steps}
                style={{
                  strokeWidth: 4,
                  strokeLinecap: "round",
                  strokeDasharray: "0, 0.9999",
                }}
                className="fill-none stroke-nilo-fill-primary"
              />
            )}

            <path
              key={`sub-segment-progress`}
              id={`sub-segment-progress`}
              d={track}
              pathLength={100}
              style={{
                strokeWidth: 4,
                strokeLinecap: "round",
                strokeDasharray: `${dashOffset}, 101`,
              }}
              className="fill-none stroke-nilo-fill-primary"
            />

            <path
              key={`sub-segment-handle`}
              id={`sub-segment-handle`}
              d={handle}
              style={{
                strokeWidth: 14,
                strokeLinecap: "round",
                transform: `rotate(${handleAngle}deg)`,
              }}
              className="fill-none stroke-nilo-fill-primary"
            />
          </g>
        </svg>
      </motion.div>
    </motion.div>
  );
};

export const buildSliderModule = (
  payload: Omit<RadialSliderItem, "type">
): RadialSliderItem => {
  return {
    type: RadialItemType.Slider,
    ...payload,
  };
};
