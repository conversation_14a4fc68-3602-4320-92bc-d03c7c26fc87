{"name": "deployment-tools", "version": "1.0.0", "main": "dist/index.js", "bin": {"deployment-tools": "./dist/index.js"}, "scripts": {"build": "tsup", "build:watch": "tsup --watch", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@inquirer/prompts": "^7.8.6", "@octokit/rest": "^21.0.2", "commander": "^14.0.0", "googleapis": "^160.0.0", "zod": "^3.24.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.14", "@types/node": "^24.5.2", "jest": "^29.7.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsup": "^8.5.0", "typescript": "^5.9.2"}}