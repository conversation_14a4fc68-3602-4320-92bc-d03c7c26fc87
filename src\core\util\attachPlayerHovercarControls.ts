import {
  AdditiveBlending,
  <PERSON><PERSON>er<PERSON>eometry,
  Float32BufferAttribute,
  PerspectiveCamera,
  Points,
  Quaternion,
  ShaderMaterial,
  Vector3,
} from "three";
import gsap from "gsap";
import { fitClamp, lerpFloat } from "./MathUtils";
import { CameraMode } from "./controls/CameraControls";
import vertexShader from "./../shader/particles/vertex.glsl";
import fragmentShader from "./../shader/particles/fragment.glsl";
import { simpleDeepClone } from "./JsObjectUtils";
import { Client } from "@/core/client";
import { globalDelegates } from "@/core/client/globalDelegates";
import { GameEntity, MeshEntity } from "@/core/entity";
import { allLayers, defaultLayers } from "@/core/util/RaycastUtils";
import { KeyboardAxisConfig } from "@/core/input/initializePlayerInputWithKeyboard";
import { directionVectorFromString } from "@/debug/experiments/behavior-templates/helpers";
import { onEnterFrame } from "@/debug/experiments/lib/onEnterFrame";
import { initializeTouchControls } from "@/core/play/utils/initializeTouchControls";
import { createDelegateFunction } from "@nilo/utilities";
import { initPlayerCharacterInput } from "@/core/controls/character/initPlayerCharacterInput";
import { BodyControlWrapper } from "packages/physics-with-jolt/src/webworker/wrappers/BodyControlWrapper";
import { PhysicsUserData } from "@/liveblocks.config";

export function attachPlayerHovercarControls(
  targetEntity: MeshEntity,
  forwardStr: string = "back"
) {
  const physics = Client.physics;
  if (!physics) {
    throw new Error("Physics service not found");
  }

  const onDispose = createDelegateFunction();

  let previousPhysicsState: PhysicsUserData = {};

  const currentUserConnectionId = Client.room.getSelf()?.connectionId;

  const initialize = async () => {
    console.debug("🔧 Attaching hovercar controls");

    const bodyControl = targetEntity.getPhysicsBodyControl();
    if (!bodyControl) {
      throw new Error("Body control not found");
    }

    const initPromise = bodyControl.addActiveDynamicProperties([
      "linearVelocity",
      "angularVelocity",
      "contactCount",
      "isActive",
    ]);
    onDispose.on(() => {
      bodyControl.removeActiveDynamicProperties([
        "linearVelocity",
        "angularVelocity",
        "contactCount",
        "isActive",
      ]);
    });

    targetEntity.state.addData("controlledBy", currentUserConnectionId);
    Client.markEntityAsDirty(targetEntity, false, false);

    Client.userEntity.deselectEntity(targetEntity);
    Client.authority.addAuthorizedEntity(targetEntity);
    onDispose.on(() => {
      Client.authority.removeAuthorizedEntity(targetEntity);
    });
    //// Input

    const defaultKeyboardConfig = {
      move: [
        ["w", "a", "s", "d"], // WASD
        ["ArrowUp", "ArrowLeft", "ArrowDown", "ArrowRight"], // Arrow keys
      ] as KeyboardAxisConfig[],
      look: [], // Not used with keyboard
      jump: [" "],
      sprint: ["Shift"],
      firstPerson: ["q"],
      zoom: [],
      exit: ["Escape"], // ESC to exit hovercar controls
    };

    const playerInput = initPlayerCharacterInput(
      onDispose,
      defaultKeyboardConfig
    );

    const cleanupTouchControls = initializeTouchControls(playerInput);
    onDispose.on(cleanupTouchControls);

    //// Physics

    const MOVE_SPEED = 70; // Linear movement speed
    const TURN_SPEED = 0.35; // Angular movement speed (radians per second)
    const JUMP_FORCE = 600;
    const UP = new Vector3(0, 1, 0);
    const rotatedUp = new Vector3(0, 0, 1);
    const QUAT_IDENTITY = new Quaternion().identity();
    const boundingBoxSize = new Vector3();
    const BOOST_FORCE = 105;
    const MAX_DELTA_TIME = 0.1;
    const _dummyCamera = new PerspectiveCamera();
    const CAMERA_ROTATION_LERP = 0.6;
    const CAMERA_FOV_LERP = 0.1;

    // Define the forward vector relative to the model's local space
    // For example, if your model faces +X direction by default, set it to (1, 0, 0)
    const forwardVector = directionVectorFromString(forwardStr);

    let hasContacts = false;
    let isBoosting = false;
    let isFirstPerson = false;

    let transitionStarted = false;
    let transitionFinished = false;

    previousPhysicsState = simpleDeepClone(
      targetEntity.getPhysicsUserData() || {}
    );

    // Ensure the entity's physics is dynamic and moving
    targetEntity.setPhysicsUserData({
      ...previousPhysicsState,
      physics: {
        ...previousPhysicsState.physics,
        isDisabled: false,
        isStatic: false,
        isSensor: false,
      },
    });

    bodyControl.setAllowSleeping(true);

    const updateCarPhysics = (deltaTime: number) => {
      hasContacts = bodyControl.getNumberDynamicProperty("contactCount") > 0;
      deltaTime = Math.min(deltaTime, MAX_DELTA_TIME);
      // Initialize linear and angular velocities
      const linearVelocity = new Vector3();
      const angularVelocity = new Vector3();
      const currentLinearVelocity = new Vector3().copy(
        bodyControl.getVector3DynamicProperty("linearVelocity")
      );
      const currentAngularVelocity = new Vector3().copy(
        bodyControl.getVector3DynamicProperty("angularVelocity")
      );

      // Get current rotation as a quaternion
      const currentQuaternion = bodyControl.rotation;
      if (!currentQuaternion) {
        console.warn(
          "❌ bodyControl.getQuaternion() returned null or undefined."
        );
        return;
      }

      rotatedUp.copy(UP).applyQuaternion(currentQuaternion);
      if (
        rotatedUp.y < 0.5 &&
        bodyControl.getBooleanDynamicProperty("isActive") == false
      ) {
        console.warn("⬆️ car is down, resetting... ⬆️");
        const boundingBox = targetEntity.getBoundingBox();
        if (!boundingBox) {
          console.warn("❌ bounding box not found");
          return;
        }
        boundingBox.getSize(boundingBoxSize);
        const currentPosition = bodyControl.position;
        bodyControl.setPosition({
          x: currentPosition.x,
          // we use the size of the bounding box of the object
          // to know how high we should move the object up,
          // in case we scale it, or in case it is large
          y: currentPosition.y + 4 * boundingBoxSize.y,
          z: currentPosition.z,
        });
        bodyControl.setRotation(QUAT_IDENTITY);
        return;
      }

      // Calculate the forward and right vectors based on the current rotation
      const forward = forwardVector
        .clone()
        .applyQuaternion(currentQuaternion)
        .normalize();

      // Determine forward/backward movement
      const moveDirection = playerInput.getAxis("move").y > 0 ? 1 : -1;
      const moveSpeedMultiplierAbs = Math.min(
        Math.abs(playerInput.getAxis("move").y),
        1.0
      );
      const moveSpeedMultiplier = Math.pow(moveSpeedMultiplierAbs, 0.5);
      const isMovingBackward = playerInput.getAxis("move").y < 0;

      const moveSpeed =
        moveDirection *
        (MOVE_SPEED * moveSpeedMultiplier + (isBoosting ? BOOST_FORCE : 0));

      if (hasContacts) {
        // Handle forward/backward movement
        linearVelocity.addScaledVector(forward, moveSpeed);
      }

      const velocityMagnitude = linearVelocity.length();

      // Handle rotation (left/right)
      const turnDirection = playerInput.getAxis("move").x > 0 ? 1 : -1;
      const turnFactorFromSpeedAndContacts =
        (velocityMagnitude / MOVE_SPEED) * //
        (hasContacts ? 1 : 0.1); //
      const turnFactorFromInput = Math.pow(
        Math.abs(playerInput.getAxis("move").x),
        0.5
      );
      angularVelocity.y +=
        TURN_SPEED *
        turnDirection *
        turnFactorFromSpeedAndContacts *
        turnFactorFromInput *
        (isMovingBackward ? 1 : -1);

      // Apply linear velocity
      if (linearVelocity.lengthSq() > 0) {
        linearVelocity.multiplyScalar(deltaTime);
        currentLinearVelocity.add({
          x: linearVelocity.x,
          y: 0,
          z: linearVelocity.z,
        });
      }

      // Apply angular velocity
      if (angularVelocity.y !== 0) {
        currentAngularVelocity.add({
          x: angularVelocity.x,
          y: angularVelocity.y,
          z: angularVelocity.z,
        });
      }

      // Dampen horizontal velocity for better control
      const damping = hasContacts ? 0.9 : 0.999;
      const dampenedVelocity = new Vector3(
        currentLinearVelocity.x * damping,
        currentLinearVelocity.y,
        currentLinearVelocity.z * damping
      );
      bodyControl.setLinearVelocity({
        x: dampenedVelocity.x,
        y: dampenedVelocity.y,
        z: dampenedVelocity.z,
      });

      // Optionally, dampen angular velocity as well
      const dampenedAngularVelocity = new Vector3(
        currentAngularVelocity.x * damping,
        currentAngularVelocity.y * damping,
        currentAngularVelocity.z * damping
      );
      bodyControl.setAngularVelocity({
        x: dampenedAngularVelocity.x,
        y: dampenedAngularVelocity.y,
        z: dampenedAngularVelocity.z,
      });
    };

    const handleFrameUpdate = (_: unknown, deltaTime: number) => {
      if (playerInput.getAnyInput()) {
        if (bodyControl.getBooleanDynamicProperty("isActive") == false) {
          bodyControl.activate(); // activate the body on keypress, in case it is asleep
        }
      }

      // Check for ESC key through proper input system
      if (playerInput.getButton("exit").wasPressedThisFrame) {
        Client.playerControls.destroyActivePlayerControls();
        return; // Exit early to avoid further processing
      }

      isBoosting = playerInput.getButton("sprint").isHeld;
      if (playerInput.getButton("jump").wasPressedThisFrame) {
        jump();
      }

      if (
        playerInput.getButton("firstPerson").wasPressedThisFrame &&
        !playerInput.getButton("sprint").isHeld &&
        !playerInput.getButton("jump").isHeld
      ) {
        isFirstPerson = !isFirstPerson;
        Client.userEntity
          .getCameraControls()
          ?.setCameraMode(
            isFirstPerson ? CameraMode.FIRST_PERSON : CameraMode.FREE_LOOK
          );
        // Client.setSelectionRenderEnabled(!isFirstPerson);

        if (!isFirstPerson) {
          transitionStarted = false;
          transitionFinished = false;
        }

        const camera = Client.userEntity.getCamera();
        if (camera) {
          if (isFirstPerson) {
            camera.layers = defaultLayers;
          } else {
            camera.layers = allLayers;
          }
        }
      }

      updateCarPhysics(deltaTime / 1000);
    };

    await initPromise;
    const stopPhysicsFrameUpdate = onEnterFrame(handleFrameUpdate);
    onDispose.on(stopPhysicsFrameUpdate);

    const jump = async () => {
      if (bodyControl.getNumberDynamicProperty("contactCount") > 0) {
        const jumpForce = JUMP_FORCE * bodyControl.mass;
        return bodyControl.applyForce({ x: 0, y: jumpForce, z: 0 });
      }
    };

    const camera = Client.userEntity.getCamera()!;
    const stopCameraFrameUpdate = onEnterFrame(async () => {
      if (isFirstPerson) {
        const rotation = bodyControl.rotation;
        const position = bodyControl.position;

        const forward = forwardVector
          .clone()
          .applyQuaternion(rotation)
          .normalize();

        if (position && rotation) {
          const boxSize = targetEntity.getSize();

          const radius = Math.max(boxSize.x, Math.max(boxSize.y, boxSize.z));

          _dummyCamera.position
            .copy(position)
            .sub(forward.clone().multiplyScalar(radius * 1.85));
          _dummyCamera.position.y += boxSize.y * 1.75;

          _dummyCamera.lookAt(
            _dummyCamera.position.x + forward.x,
            _dummyCamera.position.y + forward.y,
            _dummyCamera.position.z + forward.z
          );

          if (transitionFinished) {
            camera.position.copy(_dummyCamera.position);
            camera.quaternion.slerp(
              _dummyCamera.quaternion,
              CAMERA_ROTATION_LERP
            );
          } else {
            if (!transitionStarted) {
              transitionStarted = true;
              const duration = 0.5;
              const ease = "expoScale(0.5,7,none)";

              gsap.to(camera.position, {
                x: _dummyCamera.position.x,
                y: _dummyCamera.position.y,
                z: _dummyCamera.position.z,
                duration,
                ease,
                onComplete: () => {
                  transitionFinished = true;
                },
              });

              const prevRot = camera.quaternion.clone();
              const target = { time: 0 };
              gsap.to(target, {
                time: 1,
                duration,
                ease,
                onUpdate: () => {
                  camera.quaternion.slerpQuaternions(
                    prevRot,
                    _dummyCamera.quaternion,
                    target.time
                  );
                },
              });
            }
          }

          const currentLinearVelocity =
            bodyControl.getVector3DynamicProperty("linearVelocity");
          const speed = new Vector3(
            currentLinearVelocity.x,
            currentLinearVelocity.y,
            currentLinearVelocity.z
          ).length();
          const fov01 = fitClamp(speed, 5, 11, 0, 1);
          const destFov = isBoosting ? 50 + 25 * fov01 : 50;
          camera.fov = lerpFloat(camera.fov, destFov, CAMERA_FOV_LERP);
        }
      }
    });
    onDispose.on(stopCameraFrameUpdate);

    //// Particles

    const {
      updateParticleSystem,
      setParticleSystemVisible,
      destroy: destroyParticles,
    } = initParticleSystem();

    const stopParticlesLoop = onEnterFrame((_, deltaTime: number) => {
      updateParticleSystem(bodyControl, forwardVector, deltaTime / 1000);
      setParticleSystemVisible(isBoosting);
    });
    onDispose.on(stopParticlesLoop);
    onDispose.on(destroyParticles);
  };

  ////

  function dispose() {
    onDispose.invoke();
    onDispose.clear();

    // Remove the controlling user ID when controls are disposed
    const controlledBy = targetEntity.state.getData<number>("controlledBy");
    // Only remove if it's the current user's control
    if (
      controlledBy !== undefined &&
      controlledBy === currentUserConnectionId
    ) {
      targetEntity.setPhysicsUserData(previousPhysicsState);
      targetEntity.state.removeData("controlledBy");
    }

    Client.markEntityAsDirty(targetEntity);
  }

  const stopListeningForEntityRemoval = globalDelegates.removeEntity.on(
    (entity: GameEntity) => {
      if (entity === targetEntity) {
        dispose();
      }
    }
  );
  onDispose.on(stopListeningForEntityRemoval);

  return {
    entity: targetEntity,
    onDispose,
    initialize,
    dispose,

    //// Only here for backwards compatibility. TODO: Implement.
    enable: () => {
      ///
    },
    //// Only here for backwards compatibility. TODO: Implement.
    disable: () => {
      ///
    },
  };
}

function initParticleSystem() {
  const PARTICLE_COUNT = 15000;
  const PARTICLE_SPREAD = 0.2;
  const PARTICLE_SPEED = 25;
  const PARTICLE_SIZE = 0.2;
  const PARTICLE_LIFE = 0.1;
  const TRAIL_LENGTH = 5;
  const trailPositions: Vector3[] = [];

  //// Create particle system

  const geometry = new BufferGeometry();
  const positions = new Float32Array(PARTICLE_COUNT * 3);
  const colors = new Float32Array(PARTICLE_COUNT * 3);
  const sizes = new Float32Array(PARTICLE_COUNT);
  const life = new Float32Array(PARTICLE_COUNT);

  for (let i = 0; i < PARTICLE_COUNT; i++) {
    positions[i * 3] = 0;
    positions[i * 3 + 1] = 0;
    positions[i * 3 + 2] = 0;
    colors[i * 3] = 1;
    colors[i * 3 + 1] = 0.5;
    colors[i * 3 + 2] = 0;
    sizes[i] = Math.random() * PARTICLE_SIZE + PARTICLE_SIZE * 0.5;
    life[i] = Math.random() * PARTICLE_LIFE;
  }

  geometry.setAttribute("position", new Float32BufferAttribute(positions, 3));
  geometry.setAttribute("color", new Float32BufferAttribute(colors, 3));
  geometry.setAttribute("size", new Float32BufferAttribute(sizes, 1));
  geometry.setAttribute("life", new Float32BufferAttribute(life, 1));

  const material = new ShaderMaterial({
    uniforms: {
      time: { value: 0 },
      pLife: { value: PARTICLE_LIFE },
    },
    vertexShader,
    fragmentShader,
    blending: AdditiveBlending,
    depthWrite: false,
    transparent: true,
    vertexColors: true,
  });

  const particleSystem = new Points(geometry, material);
  particleSystem.visible = true;
  particleSystem.frustumCulled = false;
  Client.scene.add(particleSystem);

  ////

  function updateParticleSystem(
    bodyControl: BodyControlWrapper,
    forwardVector: Vector3,
    deltaTime: number
  ) {
    if (!particleSystem) return;

    const positions = particleSystem.geometry.attributes.position
      .array as Float32Array;
    const colors = particleSystem.geometry.attributes.color
      .array as Float32Array;
    const sizes = particleSystem.geometry.attributes.size.array as Float32Array;
    const life = particleSystem.geometry.attributes.life.array as Float32Array;

    const carPosition = bodyControl.position;
    const carRotation = bodyControl.rotation;
    const carVelocity = bodyControl.getVector3DynamicProperty("linearVelocity");
    if (!carPosition || !carRotation || !carVelocity) return;

    const carForward = forwardVector.clone().applyQuaternion(carRotation);
    const vel = new Vector3(carVelocity.x, carVelocity.y, carVelocity.z);
    const isMovingForward = vel.dot(carForward) > 0.1; // Threshold to account for small movements

    if (!isMovingForward) {
      // Clear all particles
      for (let i = 0; i < PARTICLE_COUNT; i++) {
        life[i] = 0;
        trailPositions.length = 0;
      }
    } else {
      // Update trail positions
      trailPositions.unshift(
        new Vector3(carPosition.x, carPosition.y, carPosition.z)
      );
      if (trailPositions.length > TRAIL_LENGTH) {
        trailPositions.pop();
      }

      const carBackward = carForward.negate();
      const emitterOffset = carBackward.multiplyScalar(0.8); // Offset behind the car

      for (let i = 0; i < PARTICLE_COUNT; i++) {
        life[i] -= deltaTime;

        if (life[i] <= 0) {
          // Reset particle
          const trailIndex = Math.floor(Math.random() * trailPositions.length);
          const trailPosition = trailPositions[trailIndex];
          const offset = new Vector3(
            (Math.random() - 0.5) * PARTICLE_SPREAD,
            (Math.random() - 0.5) * PARTICLE_SPREAD,
            (Math.random() - 0.5) * PARTICLE_SPREAD
          ).applyQuaternion(carRotation);

          positions[i * 3] = trailPosition.x + emitterOffset.x + offset.x;
          positions[i * 3 + 1] = trailPosition.y + emitterOffset.y + offset.y;
          positions[i * 3 + 2] = trailPosition.z + emitterOffset.z + offset.z;

          // Generate a color from orange to blue
          const t = Math.random();
          colors[i * 3] = 1 - t * 0.5; // Red
          colors[i * 3 + 1] = 0.5 - t * 0.5; // Green
          colors[i * 3 + 2] = t * 0.5; // Blue

          sizes[i] = Math.random() * PARTICLE_SIZE + PARTICLE_SIZE * 0.5;
          life[i] = Math.random() * PARTICLE_LIFE;
        } else {
          // Move existing particles
          const speed = PARTICLE_SPEED * (1 - life[i] / PARTICLE_LIFE);
          positions[i * 3] += carBackward.x * speed * deltaTime;
          positions[i * 3 + 1] += carBackward.y * speed * deltaTime;
          positions[i * 3 + 2] += carBackward.z * speed * deltaTime;
          sizes[i] *= 0.99; // Gradually reduce size
        }
      }
    }

    particleSystem.geometry.attributes.position.needsUpdate = true;
    particleSystem.geometry.attributes.color.needsUpdate = true;
    particleSystem.geometry.attributes.size.needsUpdate = true;
    particleSystem.geometry.attributes.life.needsUpdate = true;

    (particleSystem.material as ShaderMaterial).uniforms.time.value +=
      deltaTime;
  }

  function destroy() {
    // Remove from scene
    Client.scene.remove(particleSystem);

    // Dispose of geometry
    geometry.dispose();

    // Dispose of material
    material.dispose();

    // Clear arrays
    trailPositions.length = 0;

    // Clear typed arrays
    positions.fill(0);
    colors.fill(0);
    sizes.fill(0);
    life.fill(0);
  }

  const destroyed = false;

  return {
    updateParticleSystem,
    setParticleSystemVisible: (visible: boolean) => {
      if (particleSystem) {
        particleSystem.visible = visible;
      }
    },
    destroy,
    get destroyed() {
      return destroyed;
    },
  };
}
