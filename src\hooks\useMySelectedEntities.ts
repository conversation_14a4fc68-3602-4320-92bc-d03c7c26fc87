import { useEffect, useState } from "react";

import { Client } from "@/core/client";
import { GameEntity, UserEntity } from "@/core/entity";

export function useMySelectedEntities() {
  const [selectedEntities, setSelectedEntities] = useState<GameEntity[]>(() => {
    // Get initial selected entities
    return Client.userEntity.getSelectedEntities();
  });

  useEffect(() => {
    const handleEntitySelect = (user: UserEntity, entities: GameEntity[]) => {
      if (user.id === Client.userEntity.id) {
        setSelectedEntities(entities);
      }
    };

    Client.events.on("entity-select", handleEntitySelect);

    return () => {
      Client.events.off("entity-select", handleEntitySelect);
    };
  }, []);

  const isEntitySelected = (entityId: string) => {
    return selectedEntities.some((entity) => entity.id === entityId);
  };

  return { selectedEntities, isEntitySelected };
}
