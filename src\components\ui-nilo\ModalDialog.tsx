import { type ReactNode } from "react";

import { Typography } from "@/components/ui-nilo/Typography";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface ModalDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: ReactNode | string;
  description?: ReactNode | string;
  descriptionHidden?: boolean;
  footer?: ReactNode;
  children: ReactNode;
  className?: string;
  showCloseButton?: boolean;
}

export function ModalDialog({
  open,
  onOpenChange,
  title,
  description,
  descriptionHidden = false,
  footer,
  children,
  className,
  showCloseButton = true,
}: ModalDialogProps) {
  const renderTitle = () => {
    if (typeof title === "string") {
      return (
        <Typography.Heading level={1} className="text-xl">
          {title}
        </Typography.Heading>
      );
    }
    return title;
  };

  const renderDescription = () => {
    if (!description) return null;

    if (typeof description === "string") {
      return <Typography.Label level={2}>{description}</Typography.Label>;
    }
    return description;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={cn("flex flex-col", className)}
        showCloseButton={showCloseButton}
        onPointerDown={(e) => e.stopPropagation()}
        onMouseDown={(e) => e.stopPropagation()}
        onTouchStart={(e) => e.stopPropagation()}
      >
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>{renderTitle()}</DialogTitle>

          {description && (
            <>
              {!descriptionHidden && <Separator className="my-4" />}
              <DialogDescription className={cn({ hidden: descriptionHidden })}>
                {renderDescription()}
              </DialogDescription>
            </>
          )}
        </DialogHeader>

        <div className="flex-1 min-h-0">{children}</div>

        {footer && (
          <DialogFooter className="flex-shrink-0">{footer}</DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
