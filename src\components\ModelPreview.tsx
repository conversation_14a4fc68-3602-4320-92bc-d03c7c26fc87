import React, { useEffect, useRef, useState } from "react";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
import {
  AmbientLight,
  AnimationAction,
  AnimationMixer,
  Box3,
  Clock,
  Color,
  DirectionalLight,
  LoopRepeat,
  Mesh,
  MeshStandardMaterial,
  Object3D,
  PCFSoftShadowMap,
  PerspectiveCamera,
  PlaneGeometry,
  Scene,
  Vector3,
  WebGLRenderer,
} from "three";
import { nanoid } from "nanoid/non-secure";
import { getFileExtensionFromURL } from "@/utils/getLoader";
import { Importer } from "@/core/util/import";
import { Button } from "@/components/ui/button";

interface ModelPreviewProps {
  modelUrl: string;
  modelName: string;
  isAnimation?: boolean;
  autoPlay?: boolean;
}

export function ModelPreview({
  modelUrl,
  modelName: _modelName,
  isAnimation = false,
  autoPlay = false,
}: ModelPreviewProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [modelLoaded, setModelLoaded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [animationProgress, setAnimationProgress] = useState(0);

  const sceneRef = useRef<Scene | null>(null);
  const cameraRef = useRef<PerspectiveCamera | null>(null);
  const rendererRef = useRef<WebGLRenderer | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  const modelRef = useRef<Object3D | null>(null);
  const animationIdRef = useRef<number | null>(null);
  const mixerRef = useRef<AnimationMixer | null>(null);
  const currentActionRef = useRef<AnimationAction | null>(null);
  const clockRef = useRef<Clock>(new Clock());

  const initializeScene = () => {
    if (!containerRef.current) {
      return;
    }

    // Scene
    const scene = new Scene();
    scene.background = new Color(0xf0f0f0);
    sceneRef.current = scene;

    // Camera
    const camera = new PerspectiveCamera(
      75,
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(2, 2, 2);
    cameraRef.current = camera;

    // Renderer
    const renderer = new WebGLRenderer({ antialias: true });
    renderer.setSize(
      containerRef.current.clientWidth,
      containerRef.current.clientHeight
    );
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = PCFSoftShadowMap;
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.screenSpacePanning = false;
    controls.minDistance = 1;
    controls.maxDistance = 10;
    controls.maxPolarAngle = Math.PI / 2;
    controlsRef.current = controls;

    // Lights
    const ambientLight = new AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 5, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.camera.zoom = 2;
    scene.add(directionalLight);

    // Ground plane
    const groundGeometry = new PlaneGeometry(10, 10);
    const groundMaterial = new MeshStandardMaterial({
      color: 0xcccccc,
      transparent: true,
      opacity: 0.3,
    });
    const ground = new Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    scene.add(ground);

    // Animation loop
    const animate = () => {
      animationIdRef.current = requestAnimationFrame(animate);

      // Update animation mixer if it exists
      if (mixerRef.current) {
        const delta = clockRef.current.getDelta();
        mixerRef.current.update(delta);

        // Update progress for UI
        if (currentActionRef.current && currentActionRef.current.isRunning()) {
          const time = currentActionRef.current.time;
          const duration = currentActionRef.current.getClip().duration;
          setAnimationProgress((time / duration) * 100);
        }
      }

      controls.update();
      renderer.render(scene, camera);
    };
    animate();
  };

  const loadModel = async () => {
    if (!sceneRef.current || !modelUrl) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.debug("Loading model from URL:", modelUrl);

      const extension = getFileExtensionFromURL(modelUrl);
      const data = await fetch(modelUrl);
      const file = new File([await data.blob()], `${nanoid()}.${extension}`);
      const importer = new Importer();
      const model = await importer.loadModel(file, [file]);

      console.debug("Model loaded successfully:", model);

      // Center and scale the model
      const box = new Box3().setFromObject(model);
      const center = box.getCenter(new Vector3());
      const size = box.getSize(new Vector3());

      // Scale to fit in a reasonable size
      const maxDim = Math.max(size.x, size.y, size.z);
      const scale = 2 / maxDim;
      model.scale.setScalar(scale);

      // Center the model
      model.position.sub(center.multiplyScalar(scale));

      // Enable shadows
      model.traverse((child) => {
        if (child instanceof Mesh) {
          child.castShadow = true;
          child.receiveShadow = true;
        }
      });

      // Remove previous model if exists
      if (modelRef.current) {
        sceneRef.current.remove(modelRef.current);
      }

      sceneRef.current.add(model);
      modelRef.current = model;

      // Handle animations if this is an animation asset
      if (isAnimation && "animations" in model && model.animations.length > 0) {
        console.debug(
          "Setting up animation mixer with",
          model.animations.length,
          "animations"
        );

        // Create animation mixer
        const mixer = new AnimationMixer(model);
        mixerRef.current = mixer;

        // Get the first animation clip
        const clip = model.animations[0];
        console.debug(
          "Using animation clip:",
          clip.name,
          "duration:",
          clip.duration
        );

        // Create and configure the action
        const action = mixer.clipAction(clip);
        action.setLoop(LoopRepeat, Infinity); // Loop infinitely
        currentActionRef.current = action;

        // Auto-play if requested
        if (autoPlay) {
          action.play();
          setIsPlaying(true);
        }
      } else if (isAnimation) {
        console.warn("Expected animations, but no animations found in model");
      }

      setModelLoaded(true);
    } catch (err) {
      console.error("Error loading model:", err);
      setError(
        `Failed to load model: ${err instanceof Error ? err.message : "Unknown error"}`
      );
    } finally {
      setIsLoading(false);
    }
  };

  const toggleAnimation = () => {
    if (!currentActionRef.current) {
      console.error("No current action ref");
      return;
    }
    currentActionRef.current.paused = isPlaying;
    setIsPlaying(!isPlaying);
  };

  const resetAnimation = () => {
    if (!currentActionRef.current) {
      return;
    }

    currentActionRef.current.reset();
    currentActionRef.current.play();
    setIsPlaying(true);
  };

  const cleanup = () => {
    if (animationIdRef.current) {
      cancelAnimationFrame(animationIdRef.current);
    }

    if (mixerRef.current) {
      mixerRef.current.stopAllAction();
    }

    if (rendererRef.current && containerRef.current) {
      containerRef.current.removeChild(rendererRef.current.domElement);
      rendererRef.current.dispose();
    }

    if (controlsRef.current) {
      controlsRef.current.dispose();
    }

    sceneRef.current = null;
    cameraRef.current = null;
    rendererRef.current = null;
    controlsRef.current = null;
    modelRef.current = null;
    mixerRef.current = null;
    currentActionRef.current = null;
  };

  const handleTogglePreview = () => {
    console.debug("Toggle preview clicked, current state:", isVisible);
    if (isVisible) {
      console.debug("Hiding preview");
      setIsVisible(false);
      cleanup();
    } else {
      console.debug("Showing preview");
      setIsVisible(true);
      // Use setTimeout to ensure the DOM is updated before initializing
      setTimeout(() => {
        console.debug("Initializing scene and loading model");
        initializeScene();
        loadModel();
      }, 0);
    }
  };

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current && cameraRef.current && rendererRef.current) {
        const width = containerRef.current.clientWidth;
        const height = containerRef.current.clientHeight;

        cameraRef.current.aspect = width / height;
        cameraRef.current.updateProjectionMatrix();
        rendererRef.current.setSize(width, height);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [isVisible]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, []);

  return (
    <div className="mt-4">
      {!modelLoaded && (
        <Button
          onClick={handleTogglePreview}
          className="px-4 py-2"
          disabled={isLoading}
        >
          {isLoading
            ? "Loading..."
            : isVisible
              ? "Hide Preview"
              : "Show Preview"}
        </Button>
      )}

      {error && <div className="mt-2 text-red-600 text-sm">{error}</div>}

      {isVisible && (
        <div className="mt-4">
          {/* Animation controls for animation assets */}
          {isAnimation && modelLoaded && (
            <div className="mb-3 flex items-center gap-2">
              <Button onClick={toggleAnimation} className="px-3 py-1">
                {isPlaying ? "Pause" : "Play"}
              </Button>
              <Button onClick={resetAnimation} className="px-3 py-1">
                Reset
              </Button>
              {isPlaying && (
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-100"
                    style={{ width: `${animationProgress}%` }}
                  />
                </div>
              )}
            </div>
          )}

          <div
            ref={containerRef}
            className="w-full border border-gray-200 rounded-lg overflow-hidden"
            style={{ aspectRatio: "1 / 1" }}
          />
        </div>
      )}
    </div>
  );
}
