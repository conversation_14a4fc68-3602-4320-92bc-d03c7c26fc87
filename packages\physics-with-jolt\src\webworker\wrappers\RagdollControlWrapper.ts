import * as Comlink from "comlink";
import {
  RagdollSettings,
  RagdollPose,
  RagdollPoseWithPositionAndRotation,
} from "../../types/Ragdoll";
import { BodyControlApi, RagdollControlApi } from "../../types/webworker";
import { BodyControlWrapper } from "./BodyControlWrapper";
import { GenericWrapper } from "./GenericWrapper";

export class RagdollControlWrapper extends GenericWrapper {
  private _ragdollGroupID: number = -1;

  public get ragdollGroupID(): number {
    return this._ragdollGroupID;
  }

  constructor(
    public readonly ragdollControlApi: Comlink.Remote<RagdollControlApi>,
    private readonly ragdollSettings: RagdollSettings
  ) {
    super(ragdollControlApi);
    this.ragdollControlApi.ragdollGroupID.then((id) => {
      this._ragdollGroupID = id;
    });
  }

  public changeBoneCollision(boneName: string, isActive: boolean): void {
    this.ragdollControlApi.changeBoneCollision(boneName, isActive);
  }

  public updatePoseWithOriginal(pose: Ragdoll<PERSON>ose, deltaTime: number): void {
    this.ragdollControlApi.updatePoseWithOriginal(pose, deltaTime);
  }

  public async getPose(): Promise<RagdollPoseWithPositionAndRotation> {
    return this.ragdollControlApi.getPose();
  }

  public async getBodyControls(): Promise<{
    [key: string]: BodyControlWrapper;
  }> {
    const bodyControlsApi = await this.ragdollControlApi.getBodyControls();
    const bodyControls = {} as { [key: string]: BodyControlWrapper };
    for (const [key, value] of Object.entries(bodyControlsApi)) {
      bodyControls[key] = new BodyControlWrapper(
        value as Comlink.Remote<BodyControlApi>,
        await value.physicsBody
      );
    }
    return bodyControls;
  }

  public async setKinematic(): Promise<void> {
    return this.ragdollControlApi.setKinematic();
  }

  public async setDynamic(): Promise<void> {
    return this.ragdollControlApi.setDynamic();
  }

  public async removeFromPhysicsSystem(): Promise<void> {
    return this.ragdollControlApi.removeFromPhysicsSystem();
  }
}
