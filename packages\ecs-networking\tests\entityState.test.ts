import { describe, test, expect } from "@jest/globals";
import { ComponentKey } from "@nilo/ecs/src/archetype";
import { EntitySerializationState } from "../src/index";

describe("EntitySerializationState", () => {
  test("setComponent - sets new component data", () => {
    const entityState = new EntitySerializationState();
    const componentKey: ComponentKey = 123;
    const data = new Uint8Array([1, 2, 3, 4]);

    const result = entityState.setComponent(componentKey, data);
    expect(result).toBe(true);

    expect(entityState.components.get(componentKey)).toEqual({
      dirty: true,
      data,
    });
  });

  test("setComponent - updates existing component with different data", () => {
    const entityState = new EntitySerializationState();
    const componentKey: ComponentKey = 123;
    const initialData = new Uint8Array([1, 2, 3]);
    const newData = new Uint8Array([4, 5, 6]);

    // Set initial data
    entityState.setComponent(componentKey, initialData);

    // Update with new data
    const result = entityState.setComponent(componentKey, newData);
    expect(result).toBe(true);

    // Assert new data is dirty and has new data
    expect(entityState.components.get(componentKey)).toEqual({
      dirty: true,
      data: newData,
    });
  });

  test("setComponent - returns false when data is identical", () => {
    const entityState = new EntitySerializationState();
    const componentKey: ComponentKey = 123;
    const data = new Uint8Array([1, 2, 3, 4]);

    // Set initial data
    entityState.setComponent(componentKey, data);

    // Try to set the same data again
    const result = entityState.setComponent(
      componentKey,
      new Uint8Array([1, 2, 3, 4])
    );

    expect(result).toBe(false);
  });

  test("setComponent - returns true when data length differs", () => {
    const entityState = new EntitySerializationState();
    const componentKey: ComponentKey = 123;
    const initialData = new Uint8Array([1, 2, 3]);
    const newData = new Uint8Array([1, 2, 3, 4]); // Different length

    entityState.setComponent(componentKey, initialData);

    const result = entityState.setComponent(componentKey, newData);
    expect(result).toBe(true);
    const componentState = entityState.components.get(componentKey);
    expect(componentState!.data).toEqual(newData);
  });

  test("setComponent - returns true when data bytes differ", () => {
    const entityState = new EntitySerializationState();
    const componentKey: ComponentKey = 123;
    const initialData = new Uint8Array([1, 2, 3]);
    const newData = new Uint8Array([1, 2, 4]); // Same length, different bytes

    entityState.setComponent(componentKey, initialData);

    const result = entityState.setComponent(componentKey, newData);
    expect(result).toBe(true);

    const componentState = entityState.components.get(componentKey);
    expect(componentState!.data).toEqual(newData);
  });

  test("setComponent - handles empty data", () => {
    const entityState = new EntitySerializationState();
    const componentKey: ComponentKey = 123;
    const emptyData = new Uint8Array([]);

    const result = entityState.setComponent(componentKey, emptyData);

    expect(result).toBe(true);
    const componentState = entityState.components.get(componentKey);
    expect(componentState!.data).toEqual(emptyData);
    expect(componentState!.dirty).toBe(true);
  });

  test("removeComponent - removes existing component with data", () => {
    const entityState = new EntitySerializationState();
    const componentKey: ComponentKey = 123;
    const data = new Uint8Array([1, 2, 3]);

    // Set initial data
    entityState.setComponent(componentKey, data);
    // Remove component
    const result = entityState.removeComponent(componentKey);

    expect(result).toBe(true);
    const componentState = entityState.components.get(componentKey);
    expect(componentState!.dirty).toBe(true);
    expect(componentState!.data).toBeUndefined();
  });

  test("removeComponent - returns false when component already has no data", () => {
    const entityState = new EntitySerializationState();
    const componentKey: ComponentKey = 123;

    // Set component to removed state first
    entityState.components.set(componentKey, { dirty: false, data: undefined });
    // Try to remove again
    const result = entityState.removeComponent(componentKey);

    expect(result).toBe(false);
  });

  test("removeComponent - removes component that doesn't exist yet", () => {
    const entityState = new EntitySerializationState();
    const componentKey: ComponentKey = 123;

    const result = entityState.removeComponent(componentKey);

    expect(result).toBe(true);
    const componentState = entityState.components.get(componentKey);
    expect(componentState!.dirty).toBe(true);
    expect(componentState!.data).toBeUndefined();
  });

  test("removeComponent - handles component that was previously set and removed", () => {
    const entityState = new EntitySerializationState();
    const componentKey: ComponentKey = 123;
    const data = new Uint8Array([1, 2, 3]);

    // Set, remove, then try to remove again
    entityState.setComponent(componentKey, data);
    entityState.removeComponent(componentKey);
    const result = entityState.removeComponent(componentKey);

    expect(result).toBe(false);
  });

  test("multiple components can be managed independently", () => {
    const entityState = new EntitySerializationState();
    const componentKey1: ComponentKey = 123;
    const componentKey2: ComponentKey = 456;
    const data1 = new Uint8Array([1, 2, 3]);
    const data2 = new Uint8Array([4, 5, 6]);

    entityState.setComponent(componentKey1, data1);
    entityState.setComponent(componentKey2, data2);

    expect(entityState.components.size).toBe(2);
    expect(entityState.components.get(componentKey1)!.data).toEqual(data1);
    expect(entityState.components.get(componentKey2)!.data).toEqual(data2);

    // Remove one component
    entityState.removeComponent(componentKey1);
    expect(entityState.components.get(componentKey1)!.data).toBeUndefined();
    expect(entityState.components.get(componentKey2)!.data).toEqual(data2);
  });
});
