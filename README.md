# Nilo

Nilo is a powerful game development tool that offers AI-powered asset generation
and real-time collaboration.

## Installation

### Prerequisites

Before you begin working with the source code, ensure you have the following
installed:

- Go to the [Node.js download page](https://nodejs.org/en/download) and download
  the recommended version, including `fnm` and `npm`, by following the provided
  instructions.
  - You must add the Node.js installation folder to the `PATH` environment
    variable:
    1. Run `fnm list` to view the available installed versions of Node.js.
    2. Use `fnm exec --using v22.13.1 where node` to locate the Node.js
       installation folder path.
    3. Add the path to the `PATH` environment variable.

### Environment variables

You must have all the configuration files set, one of the configuration file you
need is an Environment variables file.

We store env variables on Vercel, so you first need to make sure you are a member
of the nilo team in Vercel.

If you are a team member of Vercel already, then you can pull environment
configuration file using the following steps:

1. Install Vercel CLI: `pnpm i -g vercel`
2. Pull latest env file by calling `npx vercel env pull`

By default `npx vercel env pull` will pull from the _development_ environment.
If you need to pull from a different environment, you pass it as a param of the
cli. E.g. `npx vercel env pull --environment=production`. Full documentation is
[here](https://vercel.com/docs/cli/env).

Possible values are:

- production: Served for our current production environment (main)
- preview: Server for the PRs environment
- development: Developer local machines

In a case if you edited env variables or added your own and want other users to
be able to use updates vars, you can do it from the vercel
[dashboard](https://vercel.com/nilo1/nilo/settings/environment-variables)

### Client

1. To start working on the client, make sure you have `pnpm` installed.

   ```bash
   npm install -g pnpm
   ```

2. Then, go to the client project folder and install the dependencies:

   ```bash
   pnpm install
   ```

3. Pull env variables as described in the previous section.

4. Start a client dev server with:

   ```bash
   pnpm dev
   ```

5. Go to http://localhost:3173, login and choose a room to join. Clicking on
   'create room' will send you to one with a random id.

### Firebase Serverless Functions and Storage (optional)

By default the client uses the production serverless functions. If you want to
use the local emulator for development of serverless functions, you need to:

1. In .env.local, set `USE_FIREBASE_EMULATOR = true`
2. Run emulator and watch for changes, with:

   ```bash
   pnpm dev:functions
   ```

Note: We have some strict rules on authentication so the emulator might complain
and suggest you run `firebase login --reauth` to fix it. We install
`firebase-tools` as dev dependency, so you can just run:

```bash
pnpm exec firebase login --reauth
```

If you want to debug serverless functions you can run:

```bash
pnpm dev:functions-inspect
```

This will start the emulator with the `--inspect-functions` flag, which will allow you to [debug the functions in Chrome](https://nodejs.org/en/learn/getting-started/debugging#inspector-clients). Note that this will cause the functions to execute in sequence rather than in parallel.

### Server development (optional)

By default the client uses the temporary server running on GKE. If you want to
use the local server for development, you need to:

1. In `.env.local`, set `USE_SERVER_URL = http://127.0.0.1:20041`
2. (One time only) Install WebTransport dependencies:

   ```bash
   pnpm --filter server install:webtransport
   ```

3. Run the server locally, with:

   ```bash
   pnpm dev:server
   ```

### Debugging

To debug the application:

1. Use VSCode's debugger to launch and attach to Chrome (see launch configuration in `.vscode/launch.json`).

2. Since Chrome blocks sign-in during debugging, add the following to your `.env.local`:

```bash
BYPASS_AUTHENTICATION_ON_LOCAL_NETWORK=true
```

## Development Features

### Isolated Staging Environments (ISEs)

We use isolated staging environments (ISEs) to develop and test changes to the
codebase.

We have a setup in place that automatically creates a new ISE for each pull
request. And clean up the ISE after the pull request is closed.

Things to keep in mind:

- Do not change Firebase initialization and only use exported variables from
  `src/firebase.ts` file instead.
- Do not use Firestore hooks (`onDocumentCreated`, `onDocumentUpdated`,
  `onDocumentWritten`, `onDocumentDeleted`) directly, use wrappers from
  `serverless/functions/src/functions/utils.ts` instead.

More info about ISEs can be found in [our docs](https://www.notion.so/Isolated-Staging-Environments-ISEs-22bc41fe242980bdb2a7c9a49e3449e5?source=copy_link).

### URL Parameters

The application supports various URL parameters that can be useful during
development. These parameters can be added to the URL to enable specific
features or behaviors.

#### Running in allowMultitab Mode

To run the application with multiple tabs per user allowed:

1. Start the development server as usual with `pnpm dev`.
2. Open the application in your browser.
3. Append `?allowMultitab=true` to the URL.

For example: `http://localhost:3173?allowMultitab=true`

This will bypass the multi-tab guard, allowing you to open the application in
multiple tabs simultaneously.

#### Enabling Networking

By default, the next-gen networking features are disabled, only Liveblocks is
run.

To enable the networking feature, use flag `useNet`.

For example: `http://localhost:3173?useNet=true`

#### Force WebSocket over WebTransport

Network system tries to start with WebTransport, if it's not supported then it
switches to WebSocket fallback.

Sometimes you want to force network system to start with WebSocket by default
for debugging purposes, then you should use `forceWs` URL parameter set to
`true`

This has no effect if `useNet` is not set.

For example: `http://localhost:3173?useNet=true&forceWs=true`

#### Adding Custom URL Parameters

You can easily add custom URL parameters for testing or debugging purposes.
Here's how to implement and use new URL parameters:

1. Open `src/utils/urlParams.ts`.
2. Add your new parameter to the `defaults` object with a default value:

   ```typescript
   const defaults = {
     allowMultitab: false,
     allowPromptCache: true,
     tripoVersion: "v1.4-20240625",
   };
   ```

3. Use the parameter in your code by accessing it through the urlParams object:

   ```typescript
   import { urlParams } from "@/utils/urlParams";

   if (urlParams.yourNewParam) {
     // Your code here
   }
   ```

   The urlParams utility will automatically parse boolean, numeric, and JSON
   values from the URL.

## Project Structure

```md
# Vite Client

Source files for the client are located in `./src`.

- /app - React application root.
- /components - React components. May be deleted if we move away from React
  - entity - React UI components for each type of entity (see comments below)
  - LiveBlocksRoom - Liveblocks root
  - ThreeCanvas - Three.js canvas component. Creates Nilo client
- /primitives - React primitives. may be deleted if we move away from react
- /utils - Potentially useful stuff that LiveBlocks bootstrapped
- /core - Any code that we want to take with us in the event of moving away from
  React
  - client - Nilo client
  - server - Nilo server (future)
  - entity - Entities (see comments)
  - util - Misc helpers

# Firebase Serverless Functions

A sister folder beside './vite-project', './vite-project-firebase-backend' holds
the serverless functions for the project, which get deployed to Firebase.

Currently these are used to:

- Authenticate users with Liveblocks
- Create rooms with Liveblocks
```

## Entities

The entity system is pretty simple right now, but will likely evolve into a
fully capable client/server based ECS (Entity component system) in the future.

Entities are purely client side at the moment and are networked via LiveBlocks
(in lieu of a full client/server setup). We should be able to switch to full
server/client later with minimal code changes.

Entities hold and manage their own local Three.js node graph.

There are multiple types of Entity:

```
Entity - Entity base class
UserEntity - Represents a collaborative user in the world
PromptEntity - Represents a users prompt in the world
MeshEntity - Represents a mesh generated via a prompt
```

Which is mainly because we don't have an ECS yet. Once we have one we'll
probably only have a single Entity class and a bunch of components (Mesh,
Camera, Light, Rigidbody, Collision, Script etc). Or possibly even allow editing
/ synchronization of an entities local Three.js node graph directly.

Any changes to entity properties are automatically replicated via LiveBlocks to
the React UI and other connected clients.

Any changes in the React UI are automatically replicated via LiveBlocks to the
entities and any connected clients.

It is preferable to place any logic (e.g. generation api calls, mesh
simplification etc) in the entities themselves as opposed to the UI.

We should use LiveBlocks directly to synchronize things that aren't related to
3d entities in the world.

Once we have an ECS we'll likely stop building the entity UIs by hand and
generate then programmatically from the component attributes.

Creating a new entity type is a bit of a pain at the moment, but we probably
won't need many for the prototype and the problem will go away once we have an
ECS.

- Add new entity class to core/entities
- Add new entity UI to components/entity
- Add a reference to the new entity UI to components/ThreeCanvas
- Add a new case to the updateEntityFromLive method in core/client.ts
- Add a data definition to liveblocks.config.js

## Release Process

While `main` is our default branch used for continuous development, `prod` is our production branch.
You can't push directly into `prod`, you can only merge a PR into `prod`. Even hotfixes need a PR.
`release/*` and `hotfix/*` naming conventions are the only ones allowed to merge into `prod`.
Rules are enforced both on the GitHub branch (`prod`) settings, and by the [prod-merge.yml](.github/workflows/prod-merge.yml) file.

### Staging

`release/*` are our staging and QA branches, and should be created out of `main`. They are meant to be short-living and their purpose is testing features and cumulative bug fixes coming from `main`. Once a PR, made out of a `release/*` branch, is merged into `prod`, it will trigger a new production build.

Release branches (`release/*`) are automatically created from `prod` and kept in sync with `main` using the GitHub workflow [`create-release.yml`](.github/workflows/create-release.yml) or manually via [`scripts/release-workflow.sh`](scripts/release-workflow.sh). This process handles reverting `prod` to the point where it diverged from `main` and then merging `main` into the `release/*` branch, ensuring a clean, linear commit history.

### Hotfixes

`hotfix/*` branches, and PR against `prod`, are meant to quickly fix, or improve, a production build (not adding new features). They too trigger a new production build, but, differently from `release/*` branches, they are meant to be directly branched out of `prod`, and merged back into (or cherry picked from) `main` as soon as verified.

### Versioning

We follow semantic versioning (MAJOR.MINOR.PATCH), prefixed with a 'v'.
Every new build is tagged with a version, e.g: `v0.6.2` and it will bump the previous one.

Merging a `release/*` branch will bump up the previous MINOR version, while merging a `hotfix/*` branch will bump the PATCH.
E.g. If the latest version tag was `v0.5.6`, merging a new `release/*` branch will create a new `v0.6.0` tag. While, if a new `hotfix/*` branch was merged, the version tag will be bumped to `v0.5.7`.

The MAJOR part will be taken care of manually, will never bump up automatically.

On merge to `prod`, the GitHub workflow [prod-merge.yml](.github/workflows/prod-merge.yml) will:

- create an annotated git tag (`vMAJOR.MINOR.PATCH`) on the `prod` branch
- bump MINOR for `release/*` and PATCH for `hotfix/*` (MAJOR is manual)
- create or update a GitHub Release with PR title/body as release notes

Notes:

- Tag creation is idempotent (it skips if the tag already exists)
- [prod-merge.yml](.github/workflows/prod-merge.yml) uses the built-in `GITHUB_TOKEN` with `contents: write`. The [`create-release.yml`](.github/workflows/create-release.yml) workflow uses a separate `GH_RELEASE_PAT` for creating pull requests.
- We need to make sure [prod-merge.yml](.github/workflows/prod-merge.yml) handles manual MAJOR bumps gracefully, cause at the time of writing it doesn't.

## Shortcuts

#### General shortcuts

- `Space or Right-Click` - Open radial menu
- `cmd + Q` - Open visual settings UI
- `cmd + Left-Click(hold)` - Selection window
- `cmd + Z` - Undo Action
- `cmd + Y` - Redo Action
- `cmd + M` - Network statistics
- `cmd + P` - Save room preview
- `alt + C` - Enable/Disable collaborative code editor

#### Object selected - shortcuts

- `Space or Right-Click` - Open radial menu for object
- `F` - Focus on selected entity
- `G` - Switch between global/local gizmos
- `Delete` - Delete selected object
- `Escape [Esc]` - Deselect object
- `cmd + D` - Duplicated selected object

#### Useful Collaborative Editor prompts

- 🚗 `add car behavior` - Adds WASD movement, shift turbo and a camera to the
  selected entity
