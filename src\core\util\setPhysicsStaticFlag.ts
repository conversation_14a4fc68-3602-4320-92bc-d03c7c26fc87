import { Client } from "@/core/client";
import { PhysicsBodyInfoComponent } from "@/core/ecs/behaviors/physics";
import { GameEntity, MeshEntity, PrimitiveEntity } from "@/core/entity";
import { PhysicsOptions } from "@/liveblocks.config";
import { addPhysicsToEntity } from "@/physics/PhysicsSimulation";

/**
 * Sets the physics static flag for the given entity
 * @param entity - Entity to update
 * @param staticFlag - Whether to make the entity static or dynamic
 * @returns boolean indicating if the operation was successful
 */
export function setPhysicsStaticFlag(
  entity: GameEntity,
  staticFlag: boolean,
  collidersFlag?: boolean,
  _dirty = true
): boolean {
  if (!isEntityWithPhysics(entity)) {
    return false;
  }

  const physics: Partial<PhysicsOptions> = {
    isStatic: staticFlag,
  };

  if (collidersFlag !== undefined) {
    physics.isDisabled = !collidersFlag;
  }
  // Mark entity as updated
  entity.setOriginType(entity.getOriginType());

  // Update physics data
  entity.setPhysicsUserData(
    {
      physics,
    },
    false
  );

  const bodyControl = entity.getPhysicsBodyControl();
  // Update physics body control
  bodyControl?.updatePhysicsOptions({ isStatic: staticFlag });
  if (bodyControl && collidersFlag === false) {
    const physicsBodyInfo = entity
      .data()
      .getComponent(PhysicsBodyInfoComponent);
    if (!physicsBodyInfo) {
      return false;
    }
    Client.physics.unregisterObjectFromPhysics(physicsBodyInfo.id);
  }
  if (collidersFlag === true) {
    addPhysicsToEntity(entity);
  }
  // Update transform controls space
  // entity.setTransformControlsSpace(
  //   staticFlag ? GIZMOSPACE.LOCAL : GIZMOSPACE.GLOBAL
  // );

  if (_dirty) {
    Client.markEntityAsDirty(entity, true);
  }

  return true;
}

export const isEntityWithPhysics = (
  entity: GameEntity
): entity is MeshEntity | PrimitiveEntity => {
  return "setPhysicsUserData" in entity;
};
