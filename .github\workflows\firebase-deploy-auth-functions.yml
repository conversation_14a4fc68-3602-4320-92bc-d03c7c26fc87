# This is run for main branch only since we only have one instance of the auth functions.
# It deploys the auth functions if they have changed

name: Deploy Firebase Auth Functions
on:
  workflow_dispatch: {}
  push:
    branches: [main]
    paths:
      - .github/workflows/firebase-deploy-auth-functions.yml
      - serverless/functions/src/auth.ts

concurrency:
  group: firebase-auth-functions
  cancel-in-progress: true

jobs:
  deploy_functions:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-gcp-auth
      - uses: ./.github/actions/setup-node
      - uses: ./.github/actions/get-isolated-environment
        id: isolated-env
        with:
          branch: main # we only deploy auth functions for main
          emitEnvCodeTo: serverless/functions/src/isolatedEnv.ts
      - name: Deploy auth functions
        uses: ./.github/actions/run-single-command-with-retry
        with:
          command: pnpm exec firebase deploy --non-interactive --force --debug --only functions:auth
