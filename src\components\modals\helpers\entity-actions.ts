import { Client } from "@/core/client";

/**
 * Global function to toggle lock state for selected entities
 * Used by UI components to control entity lock state
 */
export function toggleSelectedEntitiesLock() {
  const selectedEntities = Client.userEntity.getSelectedEntities();
  if (selectedEntities.length > 0) {
    // Toggle lock state for all selected entities
    const firstEntity = selectedEntities[0];
    const shouldLock = !firstEntity.getLocked();
    selectedEntities.forEach((entity) => {
      entity.setLocked(shouldLock);
    });
    console.debug(
      `${shouldLock ? "🔒" : "🔓"} ${
        selectedEntities.length
      } entities ${shouldLock ? "locked" : "unlocked"}`
    );
  }
}
