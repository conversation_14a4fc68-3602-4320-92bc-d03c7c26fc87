import { Fragment, useCallback, useEffect, useMemo, useState } from "react";
import {
  ACESFilmicToneMapping,
  AgXToneMapping,
  CineonToneMapping,
  LinearToneMapping,
  NeutralToneMapping,
  NoToneMapping,
  ReinhardToneMapping,
  ToneMapping,
  Vector3,
} from "three";

import { PhysicsShapeType } from "@nilo/physics-with-jolt/src/types/PhysicsOptions";

import { SingleSlider } from "@/components/common/SingleSlider";
import { Vector3Slider } from "@/components/common/Vector3Slider";
import { BasePanel } from "@/components/modals/BasePanel";
import { AdvancedSlider } from "@/components/ui-nilo/AdvancedSlider";
import { PanelCollapsible } from "@/components/ui-nilo/PanelCollapsible";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import {
  useEditorUIActions,
  useEditorUIState,
} from "@/contexts/EditorUIContext";
import { Client } from "@/core/client";
import {
  AntialiasingType,
  ShadowResolutionType,
  ShadowsType,
} from "@/core/composer";
import {
  DEFAULT_DAMPING,
  DEFAULT_DISTANCE,
  DEFAULT_OFFSET,
  DEFAULT_SENSITIVITY,
} from "@/core/controls/camera/thirdPersonCameraSettings";

const ToneMapOptions: Record<string, ToneMapping> = {
  None: NoToneMapping,
  Linear: LinearToneMapping,
  Reinhard: ReinhardToneMapping,
  Cineon: CineonToneMapping,
  ACESFilmic: ACESFilmicToneMapping,
  AgX: AgXToneMapping,
  Neutral: NeutralToneMapping,
};

// Custom throttle function
function throttle<T extends (value: number) => void>(
  func: T,
  delay: number
): T {
  let lastCall = 0;
  return ((value: number) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(value);
    }
  }) as T;
}

const sections = [
  {
    title: "Properties",
    component: () => (
      <>
        <StatsControl />
        <AntialiasControl />
      </>
    ),
    defaultOpen: true,
    enabled: true,
  },

  {
    title: "Shadows",
    component: () => <ShadowsControl />,
    defaultOpen: true,
    enabled: true,
  },

  {
    title: "Vignette",
    component: () => <VignetteControl />,
    defaultOpen: true,
    enabled: true,
  },

  {
    title: "Tone Mapping",
    component: () => <ToneMappingControl />,
    defaultOpen: true,
    enabled: true,
  },

  {
    title: "Color Grading",
    component: () => <ColorGradingControl />,
    defaultOpen: true,
    enabled: true,
  },

  {
    title: "Physics",
    component: () => <PhysicsControl />,
    defaultOpen: true,
    enabled: true,
  },

  {
    title: "Third Person Camera",
    component: () => <ThirdPersonCameraControl />,
    defaultOpen: true,
    enabled: false,
  },

  {
    title: "Selection",
    component: () => <SelectionControl />,
    defaultOpen: true,
    enabled: false,
  },

  {
    title: "User",
    component: () => <UserControl />,
    defaultOpen: true,
    enabled: true,
  },

  {
    title: "Multiplayer",
    component: () => <MultiplayerControl />,
    defaultOpen: true,
    enabled: true,
  },

  {
    title: "Actions",
    component: () => <ActionsControl />,
    defaultOpen: true,
    enabled: true,
  },
];

// Fine-tuning flags for advanced controls
const ADVANCED_FLAGS = {
  advancedShadowsControl: false, // Shows all shadow types vs just None/Soft/Hard
} as const;

export function DevSettingsPanel() {
  const { toggleDevSettingsPanel, toggleLeftPanelPinned } =
    useEditorUIActions();
  const { isLeftPanelPinned } = useEditorUIState();

  const enabledSections = useMemo(() => {
    return sections.filter((section) => section.enabled);
  }, []);

  return (
    <BasePanel
      title="Developer Menu"
      onClose={() => toggleDevSettingsPanel(false)}
      onPin={() => toggleLeftPanelPinned()}
      isPinned={isLeftPanelPinned}
    >
      {enabledSections.map((section, index) => (
        <Fragment key={section.title}>
          <PanelCollapsible
            title={section.title}
            defaultOpen={section.defaultOpen}
          >
            <section.component />
          </PanelCollapsible>
          {index < enabledSections.length - 1 && <Separator />}
        </Fragment>
      ))}
    </BasePanel>
  );
}

// Stats Control
function StatsControl() {
  const [enabled, setEnabled] = useState<boolean>(
    Client.composer.options.stats.enabled
  );

  const handleToggle = useCallback((checked: boolean) => {
    setEnabled(checked);
    Client.composer.options.stats.enabled = checked;
    Client.composer.updateOptions();
  }, []);

  return (
    <div className="flex items-center justify-between">
      <Label>Stats</Label>
      <Switch checked={enabled} onCheckedChange={handleToggle} />
    </div>
  );
}

// Antialias Control
function AntialiasControl() {
  const [value, setValue] = useState<AntialiasingType>(
    Client.composer.options.antialias
  );

  const handleChange = useCallback((newValue: string) => {
    const type = newValue as AntialiasingType;
    setValue(type);
    Client.composer.options.antialias = type;
    Client.composer.updateOptions();
  }, []);

  return (
    <div className="space-y-2">
      <Label>Antialiasing</Label>
      <ToggleGroup
        type="single"
        value={value}
        onValueChange={handleChange}
        className="w-full"
      >
        <ToggleGroupItem value={AntialiasingType.None} className="flex-1">
          None
        </ToggleGroupItem>
        <ToggleGroupItem value={AntialiasingType.FXAA} className="flex-1">
          FXAA
        </ToggleGroupItem>
        <ToggleGroupItem value={AntialiasingType.SMAA} className="flex-1">
          SMAA
        </ToggleGroupItem>
      </ToggleGroup>
    </div>
  );
}

// Shadows Control
function ShadowsControl() {
  const [type, setType] = useState<ShadowsType>(
    Client.composer.options.shadows.type
  );
  const [resolution, setResolution] = useState<ShadowResolutionType>(
    Client.composer.options.shadows.resolution
  );

  const handleTypeChange = useCallback((newValue: string) => {
    const shadowType = newValue as ShadowsType;
    setType(shadowType);
    Client.composer.options.shadows.type = shadowType;
    Client.composer.updateOptions();
  }, []);

  const handleQualityChange = useCallback((value: number[]) => {
    const resolutions = Object.values(ShadowResolutionType);
    const newResolution = resolutions[value[0]];
    setResolution(newResolution);
    Client.composer.options.shadows.resolution = newResolution;
    Client.composer.updateOptions();
  }, []);

  const qualityValue = Object.values(ShadowResolutionType).indexOf(resolution);

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label>Type</Label>
        {ADVANCED_FLAGS.advancedShadowsControl ? (
          <ToggleGroup
            type="single"
            value={type}
            onValueChange={handleTypeChange}
            className="w-full"
          >
            <ToggleGroupItem value={ShadowsType.None} className="flex-1">
              None
            </ToggleGroupItem>
            <ToggleGroupItem value={ShadowsType.Hard} className="flex-1">
              Hard
            </ToggleGroupItem>
            <ToggleGroupItem value={ShadowsType.PCF} className="flex-1">
              PCF
            </ToggleGroupItem>
            <ToggleGroupItem value={ShadowsType.PCFSoft} className="flex-1">
              PCF Soft
            </ToggleGroupItem>
            <ToggleGroupItem value={ShadowsType.VSM} className="flex-1">
              VSM
            </ToggleGroupItem>
          </ToggleGroup>
        ) : (
          <ToggleGroup
            type="single"
            value={type}
            onValueChange={handleTypeChange}
          >
            <ToggleGroupItem value={ShadowsType.None} className="flex-1">
              None
            </ToggleGroupItem>
            <ToggleGroupItem value={ShadowsType.Hard} className="flex-1">
              Hard
            </ToggleGroupItem>
            <ToggleGroupItem value={ShadowsType.PCFSoft} className="flex-1">
              Soft
            </ToggleGroupItem>
          </ToggleGroup>
        )}
      </div>

      <div className="space-y-2">
        <Label>Quality</Label>
        <AdvancedSlider
          value={[qualityValue]}
          min={0}
          max={Object.values(ShadowResolutionType).length - 1}
          step={1}
          ticks={true}
          showTickValues={true}
          showNumericInput={false}
          tickLabels={Object.values(ShadowResolutionType)}
          className="w-full"
          onValueChange={handleQualityChange}
        />
      </div>
    </div>
  );
}

// Vignette Control
function VignetteControl() {
  const [enabled, setEnabled] = useState<boolean>(
    Client.composer.options.vignette.enabled
  );
  const [darkness, setDarkness] = useState<number>(
    Client.composer.options.vignette.darkness
  );
  const [offset, setOffset] = useState<number>(
    Client.composer.options.vignette.offset
  );

  // Throttled update functions to limit expensive calls
  const throttledDarknessUpdate = useMemo(
    () =>
      throttle((value: number) => {
        Client.composer.options.vignette.darkness = value;
        Client.composer.updateOptions();
      }, 50),
    []
  );

  const throttledOffsetUpdate = useMemo(
    () =>
      throttle((value: number) => {
        Client.composer.options.vignette.offset = value;
        Client.composer.updateOptions();
      }, 50),
    []
  );

  const handleToggle = useCallback((checked: boolean) => {
    setEnabled(checked);
    Client.composer.options.vignette.enabled = checked;
    Client.composer.updateOptions();
  }, []);

  const handleDarknessChange = useCallback(
    (value: number[]) => {
      const newValue = value[0];
      setDarkness(newValue);
      throttledDarknessUpdate(newValue);
    },
    [throttledDarknessUpdate]
  );

  const handleOffsetChange = useCallback(
    (value: number[]) => {
      const newValue = value[0];
      setOffset(newValue);
      throttledOffsetUpdate(newValue);
    },
    [throttledOffsetUpdate]
  );

  // Final update functions to ensure no values are missed
  const handleDarknessChangeEnd = useCallback((value: number[]) => {
    const finalValue = value[0];
    setDarkness(finalValue);
    Client.composer.options.vignette.darkness = finalValue;
    Client.composer.updateOptions();
  }, []);

  const handleOffsetChangeEnd = useCallback((value: number[]) => {
    const finalValue = value[0];
    setOffset(finalValue);
    Client.composer.options.vignette.offset = finalValue;
    Client.composer.updateOptions();
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label>Enabled</Label>
        <Switch checked={enabled} onCheckedChange={handleToggle} />
      </div>

      {enabled && (
        <>
          <div className="space-y-2">
            <Label>Strength</Label>
            <Slider
              value={[darkness]}
              min={0}
              max={1}
              step={0.001}
              onValueChange={handleDarknessChange}
              onValueCommit={handleDarknessChangeEnd}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label>Offset</Label>
            <Slider
              value={[offset]}
              min={0}
              max={1}
              step={0.001}
              onValueChange={handleOffsetChange}
              onValueCommit={handleOffsetChangeEnd}
              className="w-full"
            />
          </div>
        </>
      )}
    </div>
  );
}

function ToneMappingControl() {
  const [enabled, setEnabled] = useState<boolean>(
    Client.composer.options.toneMapping.type !== NoToneMapping
  );
  const [type, setType] = useState<ToneMapping>(
    Client.composer.options.toneMapping.type
  );
  const [exposure, setExposure] = useState<number>(
    Client.composer.options.toneMapping.exposure
  );

  const handleToggle = useCallback(
    (checked: boolean) => {
      setEnabled(checked);
      if (checked) {
        // If enabling, use the current type or default to ACESFilmic
        const newType = type === NoToneMapping ? ACESFilmicToneMapping : type;
        setType(newType);
        Client.composer.options.toneMapping.type = newType;
      } else {
        // If disabling, set to NoToneMapping
        setType(NoToneMapping);
        Client.composer.options.toneMapping.type = NoToneMapping;
      }
      Client.composer.updateOptions();
    },
    [type]
  );

  const handleTypeChange = useCallback((newValue: string) => {
    const toneMappingType =
      ToneMapOptions[newValue as keyof typeof ToneMapOptions];
    setType(toneMappingType);
    Client.composer.options.toneMapping.type = toneMappingType;
    Client.composer.updateOptions();
  }, []);

  const handleExposureChange = useCallback((value: number[]) => {
    const newExposure = value[0];
    setExposure(newExposure);
    Client.composer.options.toneMapping.exposure = newExposure;
    Client.composer.updateOptions();
  }, []);

  // Get the current type name for display
  const currentTypeName =
    Object.keys(ToneMapOptions).find(
      (key) => ToneMapOptions[key as keyof typeof ToneMapOptions] === type
    ) || "ACESFilmic";

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label>Enabled</Label>
        <Switch checked={enabled} onCheckedChange={handleToggle} />
      </div>

      {enabled && (
        <>
          <div className="space-y-2">
            <Label>Type</Label>
            <Select value={currentTypeName} onValueChange={handleTypeChange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.keys(ToneMapOptions).map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Exposure</Label>
            <Slider
              value={[exposure]}
              min={0}
              max={2}
              step={0.01}
              className="w-full"
              onValueChange={handleExposureChange}
            />
          </div>
        </>
      )}
    </div>
  );
}

function ColorGradingControl() {
  const [enabled, setEnabled] = useState<boolean>(
    Client.composer.options.lut.target !== null
  );
  const [lut, setLut] = useState<string | null>(
    Client.composer.options.lut.target
  );
  const [intensity, setIntensity] = useState<number>(
    Client.composer.options.lut.intensity
  );

  const handleToggle = useCallback(
    (checked: boolean) => {
      setEnabled(checked);
      if (checked) {
        // If enabling, use the current LUT or default to the first one
        const newLut = lut || "Cinematic-9.cube";
        setLut(newLut);
        Client.composer.options.lut.target = newLut;
      } else {
        // If disabling, set to null
        setLut(null);
        Client.composer.options.lut.target = null;
      }
      Client.composer.updateOptions();
    },
    [lut]
  );

  const handleLutChange = useCallback((newValue: string) => {
    const newLut = newValue === "none" ? null : newValue;
    setLut(newLut);
    Client.composer.options.lut.target = newLut;
    Client.composer.updateOptions();
  }, []);

  // Throttled update function to limit expensive calls
  const throttledIntensityUpdate = useMemo(
    () =>
      throttle((value: number) => {
        Client.composer.options.lut.intensity = value;
        Client.composer.updateOptions();
      }, 50),
    []
  );

  const handleIntensityChange = useCallback(
    (value: number[]) => {
      const newIntensity = value[0];
      setIntensity(newIntensity);
      throttledIntensityUpdate(newIntensity);
    },
    [throttledIntensityUpdate]
  );

  // Final update function to ensure no values are missed
  const handleIntensityChangeEnd = useCallback((value: number[]) => {
    const finalIntensity = value[0];
    setIntensity(finalIntensity);
    Client.composer.options.lut.intensity = finalIntensity;
    Client.composer.updateOptions();
  }, []);

  // LUT options from the original component
  const LUTs = [
    "Bourbon 64.CUBE",
    "Chemical 168.CUBE",
    "Clayton 33.CUBE",
    "Cubicle 99.CUBE",
    "Presetpro-Cinematic.3dl",
    "Remy 24.CUBE",
    "Cinematic-1.cube",
    "Cinematic-2.cube",
    "Cinematic-3.cube",
    "Cinematic-4.cube",
    "Cinematic-5.cube",
    "Cinematic-6.cube",
    "Cinematic-7.cube",
    "Cinematic-8.cube",
    "Cinematic-9.cube",
    "Cinematic-10.cube",
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label>Enabled</Label>
        <Switch checked={enabled} onCheckedChange={handleToggle} />
      </div>

      {enabled && (
        <>
          <div className="space-y-2">
            <Label>LUT</Label>
            <Select value={lut || "none"} onValueChange={handleLutChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select a LUT" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {LUTs.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Intensity</Label>
            <Slider
              value={[intensity]}
              min={0}
              max={1}
              step={0.01}
              className="w-full"
              onValueChange={handleIntensityChange}
              onValueCommit={handleIntensityChangeEnd}
            />
          </div>
        </>
      )}
    </div>
  );
}

// Selection Control
function SelectionControl() {
  const [hideSelectionBox, setHideSelectionBox] = useState<boolean>(
    Client.composer.options.hideSelectionBox
  );

  const handleToggle = useCallback((value: boolean) => {
    setHideSelectionBox(value);
    Client.composer.options.hideSelectionBox = value;
    Client.composer.updateOptions();
  }, []);

  return (
    <div className="flex items-center justify-between">
      <Label>Hide Selection Box</Label>
      <Switch checked={hideSelectionBox} onCheckedChange={handleToggle} />
    </div>
  );
}

// User Control
function UserControl() {
  const [unchangedPosition, setUnchangedPosition] = useState<boolean>(
    Client.composer.options.unchangedPositionOnCreate
  );

  const handleToggle = useCallback((value: boolean) => {
    setUnchangedPosition(value);
    Client.composer.options.unchangedPositionOnCreate = value;
    Client.composer.updateOptions();
  }, []);

  return (
    <div className="flex items-center justify-between">
      <Label>Unchanged Position On Create</Label>
      <Switch checked={unchangedPosition} onCheckedChange={handleToggle} />
    </div>
  );
}

// Actions Control
function ActionsControl() {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Button
          variant="destructive"
          onClick={Client.clearEntities}
          className="w-full"
        >
          Clear All Entities
        </Button>
      </div>
    </div>
  );
}

// Third Person Camera Control
function ThirdPersonCameraControl() {
  const [offset, setOffset] = useState<Vector3>(DEFAULT_OFFSET);
  const [distance, setDistance] = useState<number>(DEFAULT_DISTANCE);
  const [damping, setDamping] = useState<number>(DEFAULT_DAMPING);
  const [sensitivity, setSensitivity] = useState<number>(DEFAULT_SENSITIVITY);

  const handleOffsetChange = useCallback(
    (x: number, y: number, z: number) => {
      const newOffset = new Vector3(x, y, z);
      setOffset(newOffset);
      const thirdPersonCameraSettings = Client.userEntity
        ?.getCameraControls()
        ?.getThirdPersonCameraSettings();
      thirdPersonCameraSettings?.changeSettings(newOffset, distance);
    },
    [distance]
  );

  const handleDistanceChange = useCallback(
    (value: number) => {
      setDistance(value);
      const thirdPersonCameraSettings = Client.userEntity
        ?.getCameraControls()
        ?.getThirdPersonCameraSettings();
      thirdPersonCameraSettings?.changeSettings(offset, value);
    },
    [offset]
  );

  const handleDampingChange = useCallback((value: number) => {
    setDamping(value);
    const thirdPersonCameraSettings = Client.userEntity
      ?.getCameraControls()
      ?.getThirdPersonCameraSettings();
    thirdPersonCameraSettings?.setTargetDamping(value);
    thirdPersonCameraSettings?.setDamping(value);
  }, []);

  const handleSensitivityChange = useCallback((value: number) => {
    setSensitivity(value);
    Client.userEntity
      ?.getCameraControls()
      ?.getThirdPersonCameraSettings()
      ?.setSensitivity(value);
  }, []);

  useEffect(() => {
    const cameraControls = Client.userEntity?.getCameraControls();
    if (!cameraControls) return;
    const thirdPersonCameraSettings =
      cameraControls.getThirdPersonCameraSettings();
    if (thirdPersonCameraSettings) {
      setOffset(thirdPersonCameraSettings.getOffset());
      setDistance(thirdPersonCameraSettings.getDistance());
      setDamping(thirdPersonCameraSettings.getDamping());
      setSensitivity(thirdPersonCameraSettings.getSensitivity());
    }
  }, []);

  return (
    <div className="space-y-6">
      <Vector3Slider
        label="Camera Offset"
        value={offset}
        onChange={({ x, y, z }) => handleOffsetChange(x, y, z)}
        min={-1}
        max={1}
        step={0.05}
      />

      <SingleSlider
        label="Distance"
        value={distance}
        min={1}
        max={20}
        step={0.1}
        onChange={handleDistanceChange}
      />

      <SingleSlider
        label="Damping"
        value={damping}
        min={10}
        max={40}
        step={0.01}
        onChange={handleDampingChange}
      />

      <SingleSlider
        label="Sensitivity"
        value={sensitivity}
        min={0.1}
        max={2}
        step={0.1}
        onChange={handleSensitivityChange}
      />
    </div>
  );
}

function MultiplayerControl() {
  const [visible, setVisible] = useState<boolean>(
    Client.composer.options.locationConeVolumetricVisible
  );
  const [opacity, setOpacity] = useState<number>(
    Client.composer.options.locationConeVolumetricOpacity
  );

  // Throttled update function to limit expensive calls
  const throttledOpacityUpdate = useMemo(
    () =>
      throttle((value: number) => {
        Client.composer.options.locationConeVolumetricOpacity = value;
        Client.composer.updateOptions();
      }, 50),
    []
  );

  const handleVisibleToggle = useCallback((checked: boolean) => {
    setVisible(checked);
    Client.composer.options.locationConeVolumetricVisible = checked;
    Client.composer.updateOptions();
  }, []);

  const handleOpacityChange = useCallback(
    (value: number[]) => {
      const newOpacity = value[0];
      setOpacity(newOpacity);
      throttledOpacityUpdate(newOpacity);
    },
    [throttledOpacityUpdate]
  );

  // Final update function to ensure no values are missed
  const handleOpacityChangeEnd = useCallback((value: number[]) => {
    const finalOpacity = value[0];
    setOpacity(finalOpacity);
    Client.composer.options.locationConeVolumetricOpacity = finalOpacity;
    Client.composer.updateOptions();
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label>Show Cones</Label>
        <Switch checked={visible} onCheckedChange={handleVisibleToggle} />
      </div>
      <div className="space-y-2">
        <Label>Cone Opacity</Label>
        <Slider
          value={[opacity]}
          min={0}
          max={1}
          step={0.01}
          className="w-full"
          onValueChange={handleOpacityChange}
          onValueCommit={handleOpacityChangeEnd}
        />
      </div>
    </div>
  );
}

// Physics Control
function PhysicsControl() {
  const [enabled, setEnabled] = useState<boolean>(
    Client.composer.options.enablePhysicsOnCreate
  );
  const [eachMeshSeparate, setEachMeshSeparate] = useState<boolean>(
    Client.composer.options.eachMeshSeparateOnCreate
  );
  const [checkModelCustomProperties, setCheckModelCustomProperties] =
    useState<boolean>(Client.composer.options.checkModelCustomProperties);
  const [enabledColliders, setEnabledColliders] = useState<boolean>(
    Client.composer.options.enableCollidersOnCreate
  );
  const [gridVisible, setGridVisible] = useState<boolean>(
    Client.composer.options.gridVisible
  );
  const [shapeTypeOnCreate, setShapeTypeOnCreate] = useState<PhysicsShapeType>(
    Client.composer.options.shapeTypeOnCreate
  );
  const [smartShapeOnCreate, setSmartShapeOnCreate] = useState<boolean>(
    Client.composer.options.smartShapeOnCreate
  );

  const handleTogglePhysics = useCallback((value: boolean) => {
    setEnabled(value);
    Client.composer.options.enablePhysicsOnCreate = value;
    Client.composer.updateOptions();
  }, []);

  const handleToggleSeparate = useCallback((value: boolean) => {
    setEachMeshSeparate(value);
    Client.composer.options.eachMeshSeparateOnCreate = value;
    Client.composer.updateOptions();
  }, []);

  const handleToggleCustomProperties = useCallback((value: boolean) => {
    setCheckModelCustomProperties(value);
    Client.composer.options.checkModelCustomProperties = value;
    Client.composer.updateOptions();
  }, []);

  const handleToggleColliders = useCallback((value: boolean) => {
    setEnabledColliders(value);
    Client.composer.options.enableCollidersOnCreate = value;
    Client.composer.updateOptions();
  }, []);

  const handleToggleGridVisible = useCallback((value: boolean) => {
    setGridVisible(value);
    Client.composer.options.gridVisible = value;
    Client.composer.updateOptions();
  }, []);

  const handleShapeTypeChange = useCallback((value: PhysicsShapeType) => {
    setShapeTypeOnCreate(value);
    Client.composer.options.shapeTypeOnCreate = value;
    Client.composer.updateOptions();
  }, []);

  const handleSmartShapeChange = useCallback((value: boolean) => {
    setSmartShapeOnCreate(value);
    Client.composer.options.smartShapeOnCreate = value;
    Client.composer.updateOptions();
  }, []);

  const handleTogglePhysicsWithColliders = useCallback(
    (value: boolean) => {
      handleTogglePhysics(value);
      if (value) {
        handleToggleColliders(true);
      }
    },
    [handleTogglePhysics, handleToggleColliders]
  );

  const handleToggleCollidersWithPhysics = useCallback(
    (value: boolean) => {
      if (!value) {
        handleTogglePhysics(false);
      }
      handleToggleColliders(value);
    },
    [handleTogglePhysics, handleToggleColliders]
  );

  const handleToggleSeparateWithCustomProperties = useCallback(
    (value: boolean) => {
      handleToggleSeparate(value);
      if (!value) {
        handleToggleCustomProperties(false);
      }
    },
    [handleToggleSeparate, handleToggleCustomProperties]
  );

  const handleToggleCustomPropertiesWithSeparate = useCallback(
    (value: boolean) => {
      if (value) {
        handleToggleSeparate(true);
      }
      handleToggleCustomProperties(value);
    },
    [handleToggleSeparate, handleToggleCustomProperties]
  );

  const handleAsisShapeType = useCallback(() => {
    handleSmartShapeChange(false);
    handleShapeTypeChange("asis");
    handleTogglePhysics(false);
    handleToggleColliders(true);
  }, [
    handleSmartShapeChange,
    handleShapeTypeChange,
    handleTogglePhysics,
    handleToggleColliders,
  ]);

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label>Physics On Create</Label>
        <ToggleGroup
          type="single"
          value={enabled ? "on" : "off"}
          onValueChange={(value) =>
            handleTogglePhysicsWithColliders(value === "on")
          }
          className="w-full"
        >
          <ToggleGroupItem value="off" className="flex-1">
            Off
          </ToggleGroupItem>
          <ToggleGroupItem value="on" className="flex-1">
            On
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div className="space-y-2">
        <Label>Colliders On Create</Label>
        <ToggleGroup
          type="single"
          value={enabledColliders ? "on" : "off"}
          onValueChange={(value) =>
            handleToggleCollidersWithPhysics(value === "on")
          }
          className="w-full"
        >
          <ToggleGroupItem value="off" className="flex-1">
            Off
          </ToggleGroupItem>
          <ToggleGroupItem value="on" className="flex-1">
            On
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div className="space-y-2">
        <Label>Separate Meshes On Create</Label>
        <ToggleGroup
          type="single"
          value={eachMeshSeparate ? "on" : "off"}
          onValueChange={(value) =>
            handleToggleSeparateWithCustomProperties(value === "on")
          }
          className="w-full"
        >
          <ToggleGroupItem value="off" className="flex-1">
            Off
          </ToggleGroupItem>
          <ToggleGroupItem value="on" className="flex-1">
            On
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div className="space-y-2">
        <Label>Check model custom properties</Label>
        <ToggleGroup
          type="single"
          value={checkModelCustomProperties ? "on" : "off"}
          onValueChange={(value) =>
            handleToggleCustomPropertiesWithSeparate(value === "on")
          }
          className="w-full"
        >
          <ToggleGroupItem value="off" className="flex-1">
            Off
          </ToggleGroupItem>
          <ToggleGroupItem value="on" className="flex-1">
            On
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div className="space-y-2">
        <Label>Collider Type On Create</Label>
        <ToggleGroup
          type="single"
          value={
            smartShapeOnCreate
              ? "smart"
              : shapeTypeOnCreate === "convexHull"
                ? "convexHull"
                : shapeTypeOnCreate === "decomp"
                  ? "decomp"
                  : "asis"
          }
          onValueChange={(value) => {
            if (value === "smart") {
              handleSmartShapeChange(true);
            } else {
              handleSmartShapeChange(false);
              if (value === "asis") {
                handleAsisShapeType();
              } else {
                handleShapeTypeChange(value as PhysicsShapeType);
              }
            }
          }}
          className="grid w-full grid-cols-3"
        >
          <ToggleGroupItem value="smart" className="col-span-3">
            Smart Shape
          </ToggleGroupItem>
          <ToggleGroupItem value="convexHull">Convex Hull</ToggleGroupItem>
          <ToggleGroupItem value="decomp">Decomp</ToggleGroupItem>
          <ToggleGroupItem value="asis">As Is</ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div className="space-y-2">
        <Label>Grid visible</Label>
        <ToggleGroup
          type="single"
          value={gridVisible ? "on" : "off"}
          onValueChange={(value) => handleToggleGridVisible(value === "on")}
          className="w-full"
        >
          <ToggleGroupItem value="off" className="flex-1">
            Off
          </ToggleGroupItem>
          <ToggleGroupItem value="on" className="flex-1">
            On
          </ToggleGroupItem>
        </ToggleGroup>
      </div>
    </div>
  );
}
