import { RagdollComponent } from "@/core/components";
import { EntityData, Query, System, SystemContext, World } from "@nilo/ecs";

export class RagdollUpdateSystem implements System {
  name = "RagdollUpdateSystem";
  entityFilter = [RagdollComponent];

  query = new Query({ ragdoll: RagdollComponent });

  run(world: World, systemContext: SystemContext): void {
    this.query.forEach(world, (_entity, { ragdoll }) => {
      ragdoll.update(systemContext.deltaTime);
    });
  }

  onEntityRemoved(
    world: World,
    _entity: EntityData,
    _systemContext: SystemContext
  ): void {
    _entity.getComponent(RagdollComponent)?.dispose();
  }
}
