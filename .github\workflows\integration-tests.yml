name: Integration Tests

on:
  # Manual trigger for PRs or dispatched PR comments
  workflow_dispatch:
    inputs:
      pr_number:
        description: "PR number (optional, for tracking)"
        required: false
        type: string

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          # github.ref will point to the PR branch when triggered by PR comments
          ref: ${{ github.ref }}

      - uses: ./.github/actions/setup-node

      - name: Get Playwright version
        id: playwright-version
        run: echo "version=$(pnpm list @playwright/test --depth=0 --json | jq -r '.[] | .dependencies["@playwright/test"].version')" >> $GITHUB_OUTPUT

      - name: Cache Playwright browsers
        id: cache-playwright
        uses: actions/cache@v4
        with:
          path: ~/.cache/ms-playwright
          key: playwright-browsers-${{ runner.os }}-${{ steps.playwright-version.outputs.version }}
          restore-keys: |
            playwright-browsers-${{ runner.os }}-

      - name: Install Playwright Browsers
        if: steps.cache-playwright.outputs.cache-hit != 'true'
        run: pnpm exec playwright install --with-deps chromium

      - name: Run Integration Tests
        run: pnpm test:integration
        env:
          CI: true

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30

      - name: Upload test videos
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-videos
          path: tests/integration/results/
          retention-days: 7
