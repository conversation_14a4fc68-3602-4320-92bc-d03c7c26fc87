{"name": "nilo", "version": "0.0.0", "type": "module", "packageManager": "pnpm@10.14.0", "scripts": {"fetch-indexes": "firebase firestore:indexes > firestore.indexes.json", "dev:functions": "concurrently -n emulator,schema,functions -c \"blue.bold,green.bold,yellow.bold\" \"firebase emulators:start\" \"pnpm --filter @nilo/firebase-schema build:watch\" \"pnpm --filter functions build:watch\"", "dev:functions-inspect": "concurrently -n emulator,schema,functions -c \"blue.bold,green.bold,yellow.bold\" \"firebase emulators:start --inspect-functions\" \"pnpm --filter @nilo/firebase-schema build:watch\" \"pnpm --filter functions build:watch\"", "dev:server": "pnpm --filter server dev", "dev": "vite", "devs": "cross-env https=true pnpm dev", "devs-no-cors": "cross-env https=true enableCrossOriginIsolation=true pnpm dev", "build": "cross-env NODE_OPTIONS=\"--max-old-space-size=6144\" vite build", "preview": "vite preview", "update-tailwind-css": "node scripts/figma-tokens-to-tailwind-css.js", "typecheck": "tsc --project tsconfig.json --noEmit", "lint": "pnpm eslint --cache .", "lint:fix": "pnpm eslint --cache --fix .", "test": "jest", "test:coverage": "jest --coverage", "update:ui-theme": "pnpm --filter @nilo/ui-theme-importer write-files", "test:integration": "cross-env BYPASS_AUTHENTICATION=true playwright test", "test:integration:with-auth": "cross-env BYPASS_AUTHENTICATION=false playwright test", "test:integration:setup": "cross-env BYPASS_AUTHENTICATION=false playwright test --project=setup", "test:integration:report": "playwright show-report", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\""}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/cerebras": "^0.2.14", "@ai-sdk/fireworks": "^0.2.14", "@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/xai": "^1.2.16", "@eslint/js": "^9.24.0", "@evenstar/byteform": "^1.3.0", "@fal-ai/client": "^1.5.0", "@heroicons/react": "^2.2.0", "@liveblocks/client": "^2.7.2", "@liveblocks/node": "^2.7.2", "@liveblocks/react": "^2.7.2", "@liveblocks/yjs": "^2.7.2", "@livekit/components-react": "^2.6.0", "@livekit/components-styles": "^1.1.2", "@modyfi/vite-plugin-yaml": "^1.1.1", "@monaco-editor/react": "^4.6.0", "@nilo/ecs": "workspace:*", "@nilo/ecs-networking": "workspace:*", "@nilo/experiment-behaviors": "workspace:*", "@nilo/feature-flags": "workspace:*", "@nilo/firebase-schema": "workspace:*", "@nilo/isolated-environments": "workspace:*", "@nilo/network": "workspace:*", "@nilo/physics-with-jolt": "workspace:*", "@nilo/utilities": "workspace:*", "@openreplay/tracker": "^14.0.6", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@runware/sdk-js": "^1.1.38", "@sentry/react": "^10.11.0", "@sentry/vite-plugin": "^4.3.0", "@tailwindcss/vite": "^4.0.8", "@tanstack/react-query": "^5.76.1", "@tiptap/extension-character-count": "^2.7.2", "@tiptap/extension-collaboration": "^2.7.2", "@tiptap/extension-collaboration-cursor": "^2.7.2", "@tiptap/extension-highlight": "^2.7.2", "@tiptap/extension-image": "^2.7.2", "@tiptap/extension-link": "^2.7.2", "@tiptap/extension-placeholder": "^2.7.2", "@tiptap/extension-task-item": "^2.7.2", "@tiptap/extension-task-list": "^2.7.2", "@tiptap/extension-text-align": "^2.7.2", "@tiptap/extension-typography": "^2.7.2", "@tiptap/extension-youtube": "^2.7.2", "@tiptap/pm": "^2.7.2", "@tiptap/react": "^2.7.2", "@tiptap/starter-kit": "^2.7.2", "@types/three": "^0.167.2", "@vercel/analytics": "^1.3.1", "@vercel/node": "^3.2.15", "@vercel/speed-insights": "^1.0.12", "ai": "^4.3.16", "base-64": "^1.0.0", "cbor-x": "^1.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "eventemitter3": "^5.0.1", "fast-deep-equal": "^3.1.3", "firebase": "^10.13.2", "firebase-admin": "^12.5.0", "framer-motion": "^12.4.7", "gsap": "^3.12.5", "jolt-physics": "^0.36.0", "livekit-client": "^2.5.2", "livekit-server-sdk": "^2.6.2", "lodash.debounce": "^4.0.8", "lucide-react": "^0.544.0", "meshoptimizer": "^0.21.0", "monaco-editor": "^0.51.0", "nanoid": "^5.0.7", "nipplejs": "^0.10.2", "openai": "^4.82.0", "posthog-js": "^1.167.0", "re-resizable": "^6.10.0", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-dropzone": "^14.3.8", "react-error-boundary": "^6.0.0", "react-firebase-hooks": "^5.1.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-responsive": "^10.0.0", "react-router-dom": "^6.26.2", "stats.js": "^0.17.0", "swr": "^2.2.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "three": "^0.167.1", "three-mesh-bvh": "^0.8.2", "three-stdlib": "^2.33.0", "unique-names-generator": "4.7.1", "valtio": "^2.0.0", "xxhash-wasm": "^1.1.0", "y-monaco": "^0.1.6", "y-prosemirror": "1.2.12", "y-protocols": "1.0.6", "yarn": "^1.22.22", "yjs": "^13.6.19"}, "devDependencies": {"@jest/globals": "^29.7.0", "@playwright/test": "^1.54.1", "@types/base-64": "^1.0.2", "@types/jest": "^29.5.12", "@types/lodash.debounce": "^4.0.9", "@types/node": "^18.19.50", "@types/react": "^18.3.8", "@types/react-dom": "^18.3.0", "@types/stats.js": "^0.17.3", "@typescript-eslint/eslint-plugin": "^8.6.0", "@typescript-eslint/parser": "^8.6.0", "@vitejs/plugin-react-swc": "^3.7.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "deployment-tools": "workspace:*", "vercel-deployment": "workspace:*", "eslint": "^8.57.1", "eslint-config-next": "^14.2.13", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.30.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.11", "eslint-plugin-unused-imports": "^3.2.0", "firebase-tools": "^13.15.3", "globals": "^15.9.0", "jest": "^29.7.0", "playwright": "^1.54.1", "prettier": "^3.3.3", "ts-jest": "^29.4.0", "typescript": "^5.6.2", "vercel": "^44.2.13", "vite": "^5.4.7", "vite-plugin-checker": "^0.7.2", "vite-plugin-glsl": "^1.5.0", "vite-plugin-live-reload": "^3.0.3", "vite-plugin-mkcert": "^1.17.6", "vite-plugin-svgr": "^4.3.0", "worker-loader": "^3.0.8"}}