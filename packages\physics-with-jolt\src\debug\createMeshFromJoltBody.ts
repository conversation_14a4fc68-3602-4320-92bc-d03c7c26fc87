import {
  BoxGeometry,
  CapsuleGeometry,
  CylinderGeometry,
  Mesh,
  MeshBasicMaterial,
  SphereGeometry,
  BufferGeometry,
  BufferAttribute,
} from "three";
import {
  JoltBody,
  BoxShape,
  SphereShape,
  CapsuleShape,
  CylinderShape,
  ConvexHullShape,
  MeshShape,
} from "../types/debug";
import { BodyTransform } from "../types/Body";

export function createMeshFromJoltBody(joltBody: JoltBody): Mesh {
  if (!joltBody.shape) {
    throw new Error(
      "Jolt body has no shape attached, required for creating a mesh"
    );
  }
  let object3D: Mesh;
  switch (joltBody.shape.type) {
    case "box":
      object3D = createBoxMeshFromJoltBody(
        joltBody as JoltBody & { shape: BoxShape }
      );
      break;
    case "sphere":
      object3D = createSphereMeshFromJoltBody(
        joltBody as JoltBody & { shape: SphereShape }
      );
      break;
    case "capsule":
      object3D = createCapsuleMeshFromJoltBody(
        joltBody as JoltBody & { shape: CapsuleShape }
      );
      break;
    case "cylinder":
      object3D = createCylinderMeshFromJoltBody(
        joltBody as JoltBody & { shape: CylinderShape }
      );
      break;
    case "convex":
    case "static_compound":
    case "mutable_compound":
      object3D = createConvexMeshFromJoltBody(
        joltBody as JoltBody & { shape: ConvexHullShape }
      );
      break;
    case "mesh":
      object3D = createConvexMeshFromJoltBody(
        joltBody as JoltBody & { shape: MeshShape }
      );
      break;
    default:
      throw new Error(`Unsupported shape type: ${joltBody.shape.type}`);
  }

  applyBodyTransform(object3D, joltBody.transform);
  if (joltBody.shape.offset) {
    object3D.geometry.translate(
      joltBody.shape.offset.x,
      joltBody.shape.offset.y,
      joltBody.shape.offset.z
    );
  }
  object3D.name = `PHYSICS_DEBUG_MESH_${joltBody.id}`;
  object3D.updateMatrix();

  return object3D;
}

function createBoxMeshFromJoltBody(joltBody: JoltBody & { shape: BoxShape }) {
  const { size } = joltBody.shape;
  const geometry = new BoxGeometry(size.x, size.y, size.z);
  const material = new MeshBasicMaterial({
    color: 0x00ff00,
    wireframe: true,
    transparent: true,
    opacity: 0.5,
  });
  const mesh = new Mesh(geometry, material);

  return mesh;
}

function createSphereMeshFromJoltBody(
  joltBody: JoltBody & { shape: SphereShape }
) {
  const { radius } = joltBody.shape;
  const geometry = new SphereGeometry(radius, 32, 32);
  const material = new MeshBasicMaterial({
    color: 0x0000ff,
    wireframe: true,
    transparent: true,
    opacity: 0.5,
  });
  const mesh = new Mesh(geometry, material);

  return mesh;
}

function createCapsuleMeshFromJoltBody(
  joltBody: JoltBody & { shape: CapsuleShape }
) {
  const { radius, height } = joltBody.shape;
  const geometry = new CapsuleGeometry(radius, height, 20, 10);
  const material = new MeshBasicMaterial({
    color: 0xff00ff,
    wireframe: true,
    transparent: true,
    opacity: 0.5,
  });
  const mesh = new Mesh(geometry, material);

  return mesh;
}

function createCylinderMeshFromJoltBody(
  joltBody: JoltBody & { shape: CylinderShape }
) {
  const { radius, height } = joltBody.shape;
  const geometry = new CylinderGeometry(radius, radius, height, 20, 1);
  geometry.translate(0, height / 2, 0);
  const material = new MeshBasicMaterial({
    color: 0xffff00,
    wireframe: true,
    transparent: true,
    opacity: 0.5,
  });
  const mesh = new Mesh(geometry, material);

  return mesh;
}

/*
ConvexHullShape and MeshShape are treated the same way, in regards to the geometry.
*/
function createConvexMeshFromJoltBody(
  joltBody: JoltBody & { shape: ConvexHullShape | MeshShape }
) {
  const { geometry: bodyGeometry } = joltBody.shape;

  // Create geometry from the body geometry data
  const geometry = new BufferGeometry();

  if (bodyGeometry.vertices && bodyGeometry.indices) {
    // Convert ArrayBuffer to Float32Array for vertices
    const vertices = new Float32Array(bodyGeometry.vertices);
    geometry.setAttribute("position", new BufferAttribute(vertices, 3));

    // Convert ArrayBuffer to Uint32Array for indices
    if (bodyGeometry.indices.byteLength > 0) {
      const indices = new Uint32Array(bodyGeometry.indices);
      geometry.setIndex(Array.from(indices));
    }

    // Compute normals for proper lighting
    geometry.computeVertexNormals();
  } else {
    // Fallback to a simple box if no geometry data
    const fallbackGeometry = new BoxGeometry(1, 1, 1);
    geometry.copy(fallbackGeometry);
  }

  const material = new MeshBasicMaterial({
    color: 0xff8000,
    wireframe: true,
    transparent: true,
    opacity: 0.5,
  });
  const mesh = new Mesh(geometry, material);

  return mesh;
}

function applyBodyTransform(mesh: Mesh, transform: BodyTransform) {
  // Convert the matrix array to Three.js Matrix4
  mesh.position.copy(transform.position);
  mesh.quaternion.copy(transform.rotation);
  if (transform.scale) {
    mesh.scale.copy(transform.scale);
  }
}
