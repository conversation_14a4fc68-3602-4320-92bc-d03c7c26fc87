import Emittery from "emittery";
import { WebTransportSession } from "@fails-components/webtransport";
import { getLogger } from "@nilo/logger";
import {
  ConnectionCloseInfo,
  Connection,
  ConnectionEvents,
  NetworkStream,
  ProviderType,
  ChannelType,
  Channel,
  Channels,
  MessageCallback,
  CleanupCallback,
} from "../../types";

import { StreamMessageReader, StreamMessageWriter, uuid } from "../../utils";
import { ChannelStats } from "../../core/stats";

const logger = getLogger({ module: "network:webtransport" });

interface ChannelDescriptor {
  reader: StreamMessageReader;
  writer: StreamMessageWriter;
}

class UnreliableUnorderedChannel implements Channel {
  private _messageReader: StreamMessageReader;
  private _messageWriter: StreamMessageWriter;

  constructor(descriptor: ChannelDescriptor) {
    this._messageReader = descriptor.reader;
    this._messageWriter = descriptor.writer;
  }

  public async send(data: Uint8Array): Promise<void> {
    try {
      await this._messageWriter.write(data);
    } catch (error) {
      logger.error("WebTransport server send failed", {
        dataSize: data.length,
        error,
        /**
         * The session can be closed while we are writing
         *
         * TODO: Handle this more gracefully
         * One way to handle this is to close connection on the higher level immediately,
         * because if it throws, session most likely going to be closed internally with
         * the delay after all the "write" promises are resolved which make take up to the few seconds
         *
         * The solution above is not ideal, would be glad to hear any suggestions
         *
         * As for the reliable channels, we should handle missed packets and retransmit them if connection is still open
         */
      });
    }
  }

  public onReceive(cb: MessageCallback): CleanupCallback {
    return this._messageReader.subscribe(cb);
  }

  public get type(): ChannelType {
    return ChannelType.UNRELIABLE_UNORDERED;
  }

  public get stats(): ChannelStats {
    return {} as ChannelStats;
  }
}

class ReliableOrderedChannel extends UnreliableUnorderedChannel {
  public override get type(): ChannelType {
    return ChannelType.RELIABLE_ORDERED;
  }
}

export class WebTransportConnection
  extends Emittery<ConnectionEvents>
  implements Connection
{
  private _id = uuid();
  private _session: WebTransportSession;

  private _datagramsReader: StreamMessageReader;
  private _datagramsWriter: StreamMessageWriter;
  private _streamReader: StreamMessageReader;
  private _streamWriter: StreamMessageWriter;

  // private _providers: ConnectionProvider[] = [];

  public readonly channels: Readonly<Channels>;

  constructor(session: WebTransportSession, stream: NetworkStream) {
    super();

    this._session = session;

    this._datagramsReader = new StreamMessageReader(session.datagrams.readable);
    this._datagramsWriter = new StreamMessageWriter(
      session.datagrams.createWritable()
    );
    this._streamReader = new StreamMessageReader(stream.readable);
    this._streamWriter = new StreamMessageWriter(stream.writable);

    const reliableOrderedChannel = new ReliableOrderedChannel({
      reader: this._streamReader,
      writer: this._streamWriter,
    });

    const unreliableUnorderedChannel = new UnreliableUnorderedChannel({
      reader: this._datagramsReader,
      writer: this._datagramsWriter,
    });

    this.channels = {
      reliableOrdered: reliableOrderedChannel,
      unreliableUnordered: unreliableUnorderedChannel,
    };

    this._session.closed
      .then((info) => {
        this.emit("close", info);

        this._datagramsReader.dispose();
        this._streamReader.dispose();

        // for (const provider of this._providers) {
        //   provider.onClose(info, this.id);
        // }
      })
      .catch((error) => {
        logger.error("Error closing session", error);
      });
  }

  public get maxDatagramSize(): number {
    /**
     * There smust be session.datagrams.maxDatagramSize, but it is commented out and unavailable
     * in the current version of the @fails-components/webtransport package.
     *
     * Should be investigated and fixed.
     */
    return 1024;
  }

  public get id(): string {
    return this._id;
  }

  public get provider(): ProviderType {
    return ProviderType.WebTransport;
  }

  public get ready(): Promise<void> {
    return this._session.ready;
  }

  public get closed(): Promise<ConnectionCloseInfo> {
    return this._session.closed;
  }

  public async close(info?: ConnectionCloseInfo): Promise<void> {
    // Gracefully release streams
    await this._datagramsWriter.close();
    await this._streamWriter.close();

    // Close the session
    this._session.close(info);
  }

  // public addProvider(provider: ConnectionProvider) {
  //   this._providers.push(provider);
  //   provider.onInit(this.id);
  // }
}
