import {
  limit,
  startAfter,
  getDocs,
  QueryDocumentSnapshot,
  DocumentData,
  query,
  where,
  orderBy,
} from "firebase/firestore";
import { useCallback, useEffect, useRef, useState } from "react";
import type React from "react";

import { PlatformHeader } from "@/components/platform/PlatformHeader";
import { WorldThumbnail } from "@/components/platform/WorldThumbnail";
import { WorldThumbnailSkeleton } from "@/components/platform/WorldThumbnailSkeleton";
import { GradientText } from "@/components/ui/GradientText";

import {
  worldsCollection,
  worldScoresCollection,
} from "@/utils/firestoreCollections";
import { NewWorldThumbnail } from "@/components/platform/NewWorldThumbnail";
import { queryT, whereT, orderByT } from "@/hooks/firebaseHooks";
import { DbWorld } from "packages/firebase-schema/src";

const INITIAL_DISPLAYED_FEATURED_WORLDS_MAX = 14;

export const ExplorePage = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  return (
    <div className="h-screen flex flex-col">
      <div className="flex-1 overflow-y-auto" ref={scrollContainerRef}>
        <PlatformHeader />

        <div className="max-w-7xl mx-auto px-4 py-8 min-h-full select-none">
          <img
            src="/images/nilo-logo.svg"
            alt="Nilo Logo"
            className="w-32 sm:w-40 md:w-48 mb-4 mx-auto"
          />

          <h1 className="text-center text-xl sm:text-2xl md:text-3xl font-bold font-nilo-component-title-1 leading-nilo-component-title-1 tracking-nilo-component-title-1 mb-4 sm:mb-6 md:mb-8">
            <GradientText>Explore 3D worlds and games</GradientText>
          </h1>

          <div className="mb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <NewWorldThumbnail />
              <FeaturedWorlds />
              <PublishedWorldsFeed scrollContainerRef={scrollContainerRef} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const FeaturedWorlds = () => {
  const { featuredWorldIds, isLoading } = useFeaturedWorlds();
  const [showAllEnabled, setShowAllEnabled] = useState(false);

  if (isLoading) {
    return null;
  }

  const featuredWorldsCount = featuredWorldIds.length;
  if (featuredWorldsCount === 0) {
    return null;
  }

  const shouldShowAllFeaturedWorlds =
    showAllEnabled ||
    featuredWorldsCount <= INITIAL_DISPLAYED_FEATURED_WORLDS_MAX;
  const featuredWorldsToDisplay = shouldShowAllFeaturedWorlds
    ? featuredWorldIds
    : // -1 because the "See more" button will "replace" the last featured world
      featuredWorldIds.slice(0, INITIAL_DISPLAYED_FEATURED_WORLDS_MAX - 1);
  const hiddenFeaturedWorldsCount =
    featuredWorldsCount - featuredWorldsToDisplay.length;
  const hasHiddenFeaturedWorlds = hiddenFeaturedWorldsCount > 0;

  return (
    <>
      {featuredWorldsToDisplay.map((worldId) => (
        <WorldThumbnail key={worldId} worldId={worldId} />
      ))}

      {hasHiddenFeaturedWorlds && (
        <div className="group relative aspect-[4/3] rounded-2xl overflow-hidden hover:shadow-lg transition-all duration-200 bg-zinc-900 opacity-60 hover:opacity-100 hover:bg-zinc-800">
          <button
            onClick={() => setShowAllEnabled(true)}
            className="absolute inset-0 w-full h-full cursor-pointer flex items-center justify-center"
          >
            <div className="text-center">
              <div className="text-white/70 font-medium text-base mb-2 group-hover:text-white group-hover:font-semibold transition-all duration-200">
                See {hiddenFeaturedWorldsCount} more
              </div>
              <div className="text-white/80 text-lg text-bolder group-hover:text-white transition-all duration-200">
                Featured World
                {hiddenFeaturedWorldsCount > 1 ? "s" : ""}
              </div>
            </div>
          </button>
        </div>
      )}
    </>
  );
};

const PublishedWorldsFeed = ({
  scrollContainerRef,
}: {
  scrollContainerRef: React.RefObject<HTMLDivElement>;
}) => {
  const { stableWorldIds, isInitialLoading, isLoadingMore, hasMoreWorlds } =
    usePublishedWorlds(scrollContainerRef);
  const { featuredWorldIds } = useFeaturedWorlds();

  if (isInitialLoading) {
    //// Initial loading state
    return (
      <>
        {Array.from({ length: 7 }).map((_, i) => (
          <WorldThumbnailSkeleton key={i} />
        ))}
      </>
    );
  }

  if (stableWorldIds.length === 0) {
    //// No worlds found
    return (
      <>
        {Array.from({ length: 7 }).map((_, i) => (
          <WorldThumbnailSkeleton key={i} animated={false} />
        ))}
      </>
    );
  }

  const nonFeaturedWorldIds = stableWorldIds.filter(
    (worldId) => !featuredWorldIds.includes(worldId)
  );

  //// Worlds grid
  return (
    <>
      {nonFeaturedWorldIds.map((worldId) => (
        <WorldThumbnail key={worldId} worldId={worldId} />
      ))}

      {/* Muted spinner while waiting to start lazy load */}
      {hasMoreWorlds && !isLoadingMore && (
        <div className="mt-8 text-center py-[10vh]">
          <div className="inline-block w-8 h-8 border-2 border-gray-300 border-t-gray-500 rounded-full animate-spin opacity-50"></div>
        </div>
      )}

      {/* Loading more indicator */}
      {isLoadingMore && (
        <>
          {Array.from({ length: 6 }).map((_, i) => (
            <WorldThumbnailSkeleton key={`loading-more-${i}`} />
          ))}
        </>
      )}

      {/* End of worlds indicator */}
      {!hasMoreWorlds && !isLoadingMore && (
        <div className="text-center text-gray-400 py-12 text-sm">
          All worlds loaded. High-five!
        </div>
      )}
    </>
  );
};

function useFeaturedWorlds() {
  const [isLoading, setIsLoading] = useState(true);
  const [featuredWorldIds, setFeaturedWorldIds] = useState<string[]>([]);
  const [featuredWorlds, setFeaturedWorlds] = useState<DbWorld[]>([]);

  // Load all featured worlds
  const loadFeaturedWorlds = useCallback(async () => {
    try {
      setIsLoading(true);

      const featuredQuery = queryT(
        worldsCollection,
        whereT("isPublic", "==", true),
        whereT("isFeatured", "==", true)
      );

      const snapshot = await getDocs(featuredQuery);
      const worlds = snapshot.docs.map((doc) => doc.data());
      const worldIds = snapshot.docs.map((doc) => doc.id);
      setFeaturedWorlds(worlds);
      setFeaturedWorldIds(worldIds);
    } catch (error) {
      console.error("❌ Error loading featured worlds:", error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadFeaturedWorlds();
  }, [loadFeaturedWorlds]);

  return { featuredWorlds, featuredWorldIds, isLoading };
}

function usePublishedWorlds(
  scrollContainerRef: React.RefObject<HTMLDivElement>
) {
  const [stableWorldIds, setStableWorldIds] = useState<string[]>([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [lastDoc, setLastDoc] =
    useState<QueryDocumentSnapshot<DocumentData> | null>(null);
  const [hasMoreWorlds, setHasMoreWorlds] = useState(true);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const hasInitialized = useRef(false);
  const loadMoreTimeoutRef = useRef<number | null>(null);

  const WORLDS_PER_PAGE = 25;
  const LOAD_MORE_DELAY_MS = 500;

  // Load initial worlds
  const loadInitialWorlds = useCallback(async () => {
    try {
      setIsInitialLoading(true);

      const initialQuery = queryT(
        worldScoresCollection,
        whereT("isPublic", "==", true),
        orderByT("score", "desc"),
        limit(WORLDS_PER_PAGE)
      );

      const snapshot = await getDocs(initialQuery);
      const worldIds = snapshot.docs.map((doc) => doc.id);

      setStableWorldIds(worldIds);
      setLastDoc(snapshot.docs[snapshot.docs.length - 1] || null);
      setHasMoreWorlds(snapshot.docs.length === WORLDS_PER_PAGE);
      hasInitialized.current = true;
    } catch (error) {
      console.error("❌ Error loading initial worlds:", error);
    } finally {
      setIsInitialLoading(false);
    }
  }, []);

  // Load more worlds for infinite scroll
  const loadMoreWorlds = useCallback(async () => {
    if (!hasMoreWorlds || isLoadingMore || !lastDoc) return;

    try {
      setIsLoadingMore(true);

      const nextQuery = query(
        worldScoresCollection,
        where("isPublic", "==", true),
        orderBy("score", "desc"),
        startAfter(lastDoc),
        limit(WORLDS_PER_PAGE)
      );

      const snapshot = await getDocs(nextQuery);
      const newWorldIds = snapshot.docs.map((doc) => doc.id);

      if (newWorldIds.length > 0) {
        setStableWorldIds((prev) => [...prev, ...newWorldIds]);
        setLastDoc(snapshot.docs[snapshot.docs.length - 1]);
        setHasMoreWorlds(snapshot.docs.length === WORLDS_PER_PAGE);
      } else {
        setHasMoreWorlds(false);
      }
    } catch (error) {
      console.error("❌ Error loading more worlds:", error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [hasMoreWorlds, isLoadingMore, lastDoc]);

  // Scroll detection with tolerance delay for infinite scroll
  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container || isLoadingMore || !hasMoreWorlds) return;

    // Clear any existing timeout
    if (loadMoreTimeoutRef.current) {
      window.clearTimeout(loadMoreTimeoutRef.current);
      loadMoreTimeoutRef.current = null;
    }

    const { scrollTop, scrollHeight, clientHeight } = container;
    const threshold = 1000; // Load more when 1000px from bottom

    // Check if user is near the bottom
    if (scrollHeight - scrollTop - clientHeight < threshold) {
      // Start tolerance timer - only load more if user stays here for 500ms
      loadMoreTimeoutRef.current = window.setTimeout(() => {
        loadMoreWorlds();
      }, LOAD_MORE_DELAY_MS);
    }
  }, [loadMoreWorlds, isLoadingMore, hasMoreWorlds, scrollContainerRef]);

  // Initialize data on component mount
  useEffect(() => {
    if (!hasInitialized.current) {
      loadInitialWorlds();
    }
  }, [loadInitialWorlds]);

  // Set up scroll listener
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    container.addEventListener("scroll", handleScroll);
    return () => {
      container.removeEventListener("scroll", handleScroll);
      // Cleanup timeout on unmount
      if (loadMoreTimeoutRef.current) {
        window.clearTimeout(loadMoreTimeoutRef.current);
        loadMoreTimeoutRef.current = null;
      }
    };
  }, [handleScroll, scrollContainerRef]);

  return {
    stableWorldIds,
    isInitialLoading,
    isLoadingMore,
    hasMoreWorlds,
  };
}
