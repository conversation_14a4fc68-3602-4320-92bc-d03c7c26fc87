export type WorldThumbnailOptions = {
  renderOnlineEyecatch: boolean;
  renderOnlineStat: boolean;
  renderViewsEyecatch: boolean;
  renderViewsStat: boolean;
  renderFeaturedEyecatch: boolean;
  renderCreatorAboveTitle: boolean;
  renderCreatorByTitle: boolean;
  renderCreatorStat: boolean;
  renderLikesStat: boolean;
  renderSavesStat: boolean;
  renderRemixesStat: boolean;
  thumbnailAspectRatio: string;
  horizontalStatsLayout: boolean;
  thumbnailClickAction: "remix" | "visit" | "infoModal";
};

export const worldThumbnailOptionsV0: WorldThumbnailOptions = {
  renderOnlineEyecatch: true,
  renderOnlineStat: false,
  renderViewsEyecatch: true,
  renderViewsStat: false,
  renderFeaturedEyecatch: true,
  renderCreatorAboveTitle: true,
  renderCreatorByTitle: false,
  renderCreatorStat: false,
  renderLikesStat: false,
  renderSavesStat: true,
  renderRemixesStat: false,
  thumbnailAspectRatio: "aspect-[4/3]",
  horizontalStatsLayout: false,
  thumbnailClickAction: "remix",
};

export const worldThumbnailOptionsV1: WorldThumbnailOptions = {
  renderOnlineEyecatch: true,
  renderOnlineStat: false,
  renderViewsEyecatch: true,
  renderViewsStat: true,
  renderFeaturedEyecatch: true,
  renderCreatorAboveTitle: true,
  renderCreatorByTitle: false,
  renderCreatorStat: false,
  renderLikesStat: false,
  renderSavesStat: true,
  renderRemixesStat: false,
  thumbnailAspectRatio: "aspect-[4/3]",
  horizontalStatsLayout: false,
  thumbnailClickAction: "remix",
};

export const worldThumbnailOptionsV2: WorldThumbnailOptions = {
  // Former commented variant 1
  renderOnlineEyecatch: true,
  renderOnlineStat: false,
  renderViewsEyecatch: true,
  renderViewsStat: true,
  renderFeaturedEyecatch: true,
  renderCreatorAboveTitle: false,
  renderCreatorByTitle: false,
  renderCreatorStat: true,
  renderLikesStat: true,
  renderSavesStat: false,
  renderRemixesStat: true,
  thumbnailAspectRatio: "aspect-[5/4]",
  horizontalStatsLayout: false,
  thumbnailClickAction: "remix",
};

export const worldThumbnailOptionsV3: WorldThumbnailOptions = {
  // Former commented variant 2
  renderOnlineEyecatch: true,
  renderOnlineStat: false,
  renderViewsEyecatch: true,
  renderViewsStat: true,
  renderFeaturedEyecatch: true,
  renderCreatorAboveTitle: true,
  renderCreatorByTitle: false,
  renderCreatorStat: false,
  renderLikesStat: false,
  renderSavesStat: true,
  renderRemixesStat: false,
  thumbnailAspectRatio: "aspect-[16/9]",
  horizontalStatsLayout: true,
  thumbnailClickAction: "remix",
};

export const getDefaultWorldThumbnailOptions = (): WorldThumbnailOptions =>
  worldThumbnailOptionsV0;
