# Prerequisites

pnpm/npm/yarn do not call `install` script during installing a package if its
registry is npmjs (only works for git packages)

So, `@fails-components` WebTransport prebuilt binaries should be downloaded
manually:

```bash
cd node_modules/@fails-components/webtransport-transport-http3-quiche
pnpm run install
```

Only then, you can start a server with `pnpm start`

# Running server

To run development server run `pnpm run dev`
