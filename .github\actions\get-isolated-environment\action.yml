name: "Get Isolated Environment component names"
description: "Get the component names for an isolated environment"
inputs:
  branch:
    description: "The branch to get the component names for"
    required: true
  emitEnvCodeTo:
    description: "The file to emit the environment code to"
    required: false
    default: ""
  updateFirebaseJson:
    description: "Whether to update the firebase.json file"
    required: false
    default: "false"
outputs:
  name:
    description: "The name of the isolated environment"
    value: ${{ steps.isolated-env.outputs.name }}
  bucket:
    description: "The bucket name"
    value: ${{ steps.isolated-env.outputs.bucket }}
  database:
    description: "The database name"
    value: ${{ steps.isolated-env.outputs.database }}
  databaseLocation:
    description: "The database location"
    value: ${{ steps.isolated-env.outputs.databaseLocation }}
  functions:
    description: "The functions group name"
    value: ${{ steps.isolated-env.outputs.functions }}
  fleet:
    description: "The fleet name"
    value: ${{ steps.isolated-env.outputs.fleet }}
  json:
    description: "The JSON object of the isolated environment"
    value: ${{ steps.isolated-env.outputs.json }}
runs:
  using: "composite"
  steps:
    - shell: bash
      id: isolated-env
      run: |
        pnpm exec isolated-environments emitGithubActionVars "${{ inputs.branch }}" | tee $GITHUB_OUTPUT
    - if: inputs.emitEnvCodeTo != ''
      shell: bash
      run: |
        pnpm exec isolated-environments emitEnvCode "${{ steps.isolated-env.outputs.name }}" > ${{ inputs.emitEnvCodeTo }}
    - if: inputs.updateFirebaseJson == 'true'
      shell: bash
      run: |
        jq \
          --arg database "${{ steps.isolated-env.outputs.database }}" \
          --arg target "${{ steps.isolated-env.outputs.name }}" \
          --arg bucket "${{ steps.isolated-env.outputs.bucket }}" \
          '.firestore.database = $database | .storage[0].target = $target | .storage[0].bucket = $bucket' \
          firebase.json > firebase.json.new
        mv firebase.json.new firebase.json
        cat firebase.json
