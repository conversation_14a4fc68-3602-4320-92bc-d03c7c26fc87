/**
 * Maintenance related function types
 */

/* eslint-disable no-redeclare */
import { z } from "zod";

export const ScheduleMaintenanceRequest = z.object({
  delaySeconds: z.number(),
  commit: z.string().optional(),
  tag: z.string().optional(),
});
export type ScheduleMaintenanceRequest = z.infer<
  typeof ScheduleMaintenanceRequest
>;

export const ScheduleMaintenanceResponse = z.object({
  maintenanceId: z.string(),
  status: z.literal("success"),
});
export type ScheduleMaintenanceResponse = z.infer<
  typeof ScheduleMaintenanceResponse
>;

export const StopMaintenanceRequest = z.object({
  maintenanceId: z.string(),
  reason: z.enum(["completed", "cancelled", "failed"]).optional(),
  message: z.string().optional(),
});
export type StopMaintenanceRequest = z.infer<typeof StopMaintenanceRequest>;

export const StopMaintenanceResponse = z.object({
  maintenanceId: z.string(),
  status: z.literal("success"),
  reason: z.enum(["completed", "cancelled", "failed"]),
});
export type StopMaintenanceResponse = z.infer<typeof StopMaintenanceResponse>;

export const StartMaintenanceRequest = z.object({
  maintenanceId: z.string(),
});
export type StartMaintenanceRequest = z.infer<typeof StartMaintenanceRequest>;

export const StartMaintenanceResponse = z.union([
  z.object({
    maintenanceId: z.string(),
    status: z.literal("success"),
    message: z.literal("Maintenance started successfully"),
  }),
  z.object({
    maintenanceId: z.string(),
    status: z.literal("already_running"),
    message: z.literal("Maintenance is already running"),
  }),
  z.object({
    maintenanceId: z.string(),
    status: z.literal("error"),
    message: z.string(),
  }),
]);
export type StartMaintenanceResponse = z.infer<typeof StartMaintenanceResponse>;
