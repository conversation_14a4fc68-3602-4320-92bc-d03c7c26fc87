# Vercel Deployment Tools

CLI utility for managing Vercel deployments in the Nilo project.

## Commands

### `set-tag-and-redeploy`

Sets the `NILO_GIT_TAG` environment variable in Vercel and triggers a production redeploy.

**Usage:**

```bash
# From the root of the repository
node tools/vercel-deployment/dist/index.js set-tag-and-redeploy --tag <git-tag>

# Or from within the vercel-deployment directory
pnpm deploy --tag <git-tag>
# or
node dist/index.js set-tag-and-redeploy --tag <git-tag>
```

**Required Options:**

- `--tag` - Git tag to set as NILO_GIT_TAG environment variable

**Optional Options (will use environment variables if not provided):**

- `--project-id` - Vercel project ID (defaults to `VERCEL_PROJECT_ID` env var)
- `--token` - Vercel authentication token (defaults to `VERCEL_TOKEN` env var)
- `--repo-id` - GitHub repository ID (defaults to `GITHUB_REPOSITORY_ID` env var)

**Environment Variables:**

The tool automatically loads environment variables from `.env.local` and `.env` files in the current directory. This makes local development much easier!

Create a `.env.local` file:

```bash
# .env.local
VERCEL_TOKEN=your-token
VERCEL_PROJECT_ID=prj_abc123
GITHUB_REPOSITORY_ID=123456789
```

Then simply run (from the vercel-deployment directory):

```bash
pnpm deploy --tag "v1.2.3"
```

Or from the root:

```bash
node tools/vercel-deployment/dist/index.js set-tag-and-redeploy --tag "v1.2.3"
```

**Example with shell environment variables:**

```bash
export VERCEL_TOKEN="your-token"
export VERCEL_PROJECT_ID="prj_abc123"
export GITHUB_REPOSITORY_ID="123456789"

# From vercel-deployment directory
pnpm deploy --tag "v1.2.3"

# Or from root
node tools/vercel-deployment/dist/index.js set-tag-and-redeploy --tag "v1.2.3"
```

**Example with explicit options (from vercel-deployment directory):**

```bash
pnpm deploy \
  --tag "v1.2.3" \
  --project-id "prj_abc123" \
  --token "$VERCEL_TOKEN" \
  --repo-id "123456789"
```

## Development

Build the tool:

```bash
pnpm --filter vercel-deployment build
```

Watch mode for development:

```bash
pnpm --filter vercel-deployment build:watch
```
