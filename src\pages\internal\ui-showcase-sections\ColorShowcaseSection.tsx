import React, { useState } from "react";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";

const ColorSwatch = ({ colorClass }: { colorClass: string }) => {
  const [copied, setCopied] = useState(false);

  // Strip 'bg-' prefix for display and clipboard
  const displayName = colorClass.replace("bg-", "");
  const fullClassName = colorClass; // Keep full class name for styling

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(fullClassName);
      setCopied(true);
      setTimeout(() => setCopied(false), 1000);
      console.debug("📋 Copied to clipboard:", fullClassName);
    } catch (err) {
      console.debug("❌ Failed to copy:", err);
    }
  };

  return (
    <div className="flex flex-col">
      <div
        className={`w-full h-36 rounded-md border border-nilo-border-secondary ${fullClassName} relative overflow-hidden cursor-pointer transition ring-0 hover:ring-2 hover:ring-nilo-neutral-800`}
        onClick={handleCopy}
        title={`Copy ${fullClassName} to clipboard`}
        tabIndex={0}
        role="button"
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") handleCopy();
        }}
        aria-label={`Copy ${fullClassName} to clipboard`}
      >
        <div className="absolute bottom-0 left-0 right-0 bg-black/40 bg-opacity-50 px-2 py-0.5 m-1 rounded-sm">
          <p className="text-[10px] text-center font-bold text-white truncate">
            {copied ? "Copied!" : displayName}
          </p>
        </div>
      </div>
    </div>
  );
};

const ColorSection = ({
  title,
  colors,
  gridCols = "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
}: {
  title: string;
  colors: string[];
  gridCols?: string;
}) => (
  <Card>
    <CardHeader>
      <CardTitle className="text-white">{title}</CardTitle>
    </CardHeader>
    <CardContent>
      <div className={`grid ${gridCols} gap-1`}>
        {colors.map((colorClass) => (
          <ColorSwatch key={colorClass} colorClass={colorClass} />
        ))}
      </div>
    </CardContent>
  </Card>
);

export default function ColorShowcaseSection() {
  return (
    <div className="w-full max-w-6xl mx-auto space-y-8">
      <ColorSection
        title="Primitive Colors"
        colors={primitiveColors}
        gridCols="grid-cols-3 md:grid-cols-9"
      />
      <ColorSection
        title="Semantic Colors"
        colors={semanticColors}
        gridCols="grid-cols-5"
      />
    </div>
  );
}

const primitiveColors: string[] = [
  // Primary colors
  "bg-nilo-primary-100",
  "bg-nilo-primary-200",
  "bg-nilo-primary-300",
  "bg-nilo-primary-400",
  "bg-nilo-primary-500",
  "bg-nilo-primary-600",
  "bg-nilo-primary-700",
  "bg-nilo-primary-800",
  "bg-nilo-primary-900",

  // Accent colors
  "bg-nilo-accent-100",
  "bg-nilo-accent-200",
  "bg-nilo-accent-300",
  "bg-nilo-accent-400",
  "bg-nilo-accent-500",
  "bg-nilo-accent-600",
  "bg-nilo-accent-700",
  "bg-nilo-accent-800",
  "bg-nilo-accent-900",

  // Warning colors
  "bg-nilo-warning-100",
  "bg-nilo-warning-200",
  "bg-nilo-warning-300",
  "bg-nilo-warning-400",
  "bg-nilo-warning-500",
  "bg-nilo-warning-600",
  "bg-nilo-warning-700",
  "bg-nilo-warning-800",
  "bg-nilo-warning-900",

  // Danger colors
  "bg-nilo-danger-100",
  "bg-nilo-danger-200",
  "bg-nilo-danger-300",
  "bg-nilo-danger-400",
  "bg-nilo-danger-500",
  "bg-nilo-danger-600",
  "bg-nilo-danger-700",
  "bg-nilo-danger-800",
  "bg-nilo-danger-900",

  // Neutral colors
  "bg-nilo-neutral-100",
  "bg-nilo-neutral-200",
  "bg-nilo-neutral-300",
  "bg-nilo-neutral-400",
  "bg-nilo-neutral-500",
  "bg-nilo-neutral-600",
  "bg-nilo-neutral-700",
  "bg-nilo-neutral-800",
  "bg-nilo-neutral-900",
  "bg-nilo-neutral-black",
  "bg-nilo-neutral-white",
];

const semanticColors: string[] = [
  // Background colors
  "bg-nilo-fill-disabled",
  "bg-nilo-fill-error",
  "bg-nilo-fill-error-hover",
  "bg-nilo-fill-error-pressed",
  "bg-nilo-fill-primary",
  "bg-nilo-fill-primary-hover",
  "bg-nilo-fill-primary-pressed",
  "bg-nilo-fill-primary-dark",
  "bg-nilo-fill-primary-dark-hover",
  "bg-nilo-fill-primary-dark-pressed",
  "bg-nilo-fill-secondary",
  "bg-nilo-fill-secondary-hover",
  "bg-nilo-fill-secondary-pressed",
  "bg-nilo-fill-tertiary",
  "bg-nilo-fill-tertiary-hover",
  "bg-nilo-fill-tertiary-pressed",
  "bg-nilo-fill-warning",
  "bg-nilo-fill-warning-hover",
  "bg-nilo-fill-warning-pressed",

  // Border colors
  "bg-nilo-border-disabled",
  "bg-nilo-border-primary",
  "bg-nilo-border-secondary",
  "bg-nilo-border-tertiary",

  // Icon colors
  "bg-nilo-icon-disabled",
  "bg-nilo-icon-error",
  "bg-nilo-icon-placeholder",
  "bg-nilo-icon-primary",
  "bg-nilo-icon-quaternary",
  "bg-nilo-icon-secondary",
  "bg-nilo-icon-tertiary",
  "bg-nilo-icon-warning",

  // Text colors
  "bg-nilo-text-disabled",
  "bg-nilo-text-error",
  "bg-nilo-text-error-hover",
  "bg-nilo-text-placeholder",
  "bg-nilo-text-primary",
  "bg-nilo-text-primary-hover",
  "bg-nilo-text-quaternary",
  "bg-nilo-text-quaternary-hover",
  "bg-nilo-text-secondary",
  "bg-nilo-text-secondary-hover",
  "bg-nilo-text-tertiary",
  "bg-nilo-text-tertiary-hover",
  "bg-nilo-text-warning",
  "bg-nilo-text-warning-hover",
];
