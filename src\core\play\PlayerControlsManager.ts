import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "three";
import { EventEmitter } from "eventemitter3";
import toast from "react-hot-toast";
import { allLayers } from "../util/RaycastUtils";
import { CameraMode } from "../util/controls/CameraControls";
import {
  PlayerControlsFactory,
  PlayerControlsType,
} from "./PlayerControlsFactory";
import { PlayerControlsControl } from "./types/PlayerControlsControl";
import { Client } from "@/core/client";
import { GameEntity, MeshEntity, UserEntity } from "@/core/entity";
import { urlParams } from "@/utils/urlParams";

export const CUSTOM_PLAYER_CONTROL_TYPE = "custom";

interface PlayerControlsManagerEvents {
  "controls-activated": (
    controlType: PlayerControlsType | typeof CUSTOM_PLAYER_CONTROL_TYPE
  ) => void;
  "controls-deactivated": () => void;
}

/**
 * Manages player controls in the game, handling initialization, switching, and cleanup
 * of different control schemes (like character controls or hovercar controls).
 *
 * This class ensures only one control scheme is active at a time and provides
 * a clean interface for switching between different control types
 * by key ("character", "hovercar"), but also a supports custom controls, so long as
 * it has the PlayerControlsControl shape.
 */
export class PlayerControlsManager extends EventEmitter<PlayerControlsManagerEvents> {
  private currentControl: PlayerControlsControl | null = null;
  private currentControlType:
    | PlayerControlsType
    | typeof CUSTOM_PLAYER_CONTROL_TYPE
    | null = null;

  constructor() {
    super();
  }

  /**
   * Checks if an entity is already being controlled by any user.
   * @param targetEntity - The entity to check
   * @returns true if the entity is already being controlled, false otherwise
   */
  public isEntityAlreadyControlled(targetEntity: GameEntity): boolean {
    if (!(targetEntity instanceof MeshEntity)) {
      return false;
    }

    const controlledBy = targetEntity.state.getData<number>("controlledBy");
    if (!controlledBy) {
      return false;
    }

    //Check if user is online
    return Client.isUserOnline(controlledBy);
  }

  /**
   * Creates and activates a built-in player control type for the given entity.
   * @param type - The type of controls to create (e.g. 'character', 'hovercar')
   * @param targetEntity - The entity to attach the controls to
   * @returns The newly created and initialized control instance or null if the entity is already controlled
   */
  setActivePlayerControlsTypeTo(
    type: PlayerControlsType,
    targetEntity: GameEntity
  ) {
    if (!(targetEntity instanceof MeshEntity)) {
      throw new Error("Target entity must be a MeshEntity");
    }
    Client.getAllEntities()
      .filter((e) => e instanceof UserEntity)
      .forEach((e) => {
        e.setLocationConeVolumetricControlsType(type);
      });

    // Check if the entity is already being controlled
    if (this.isEntityAlreadyControlled(targetEntity)) {
      console.warn("Entity is already being controlled by a user");
      toast.error("Entity is already being controlled by another user");
      return null;
    }
    const createPlayerControls = PlayerControlsFactory[type];
    const control = createPlayerControls(targetEntity);

    this.setActivePlayerControls(control);
    this.currentControlType = type;
    Client.userEntity.setTargetEntity(targetEntity);
    Client.authority.addAuthorizedEntity(targetEntity);
    this.emit("controls-activated", type);
    return control;
  }

  /**
   * Activates a custom player control instance. This method handles cleanup of any
   * existing controls before initializing the new ones.
   *
   * @param control - The custom control instance to activate
   * @returns The activated control instance or null if the entity is already controlled
   * @throws Will throw if control initialization fails
   */
  setActivePlayerControls(control: PlayerControlsControl) {
    if (this.currentControl) {
      this.destroyActivePlayerControls();
    }

    console.debug("🎮 Setting new active player controls");
    this.currentControl = control;

    try {
      control.initialize();
      this.currentControlType = CUSTOM_PLAYER_CONTROL_TYPE;
      this.emit("controls-activated", CUSTOM_PLAYER_CONTROL_TYPE);

      toast.success("Controls activated! Press ESC to exit", {
        duration: 2000,
      });

      // Add ESC key handler
      const removeEscKeyHandler = addEscKeyHandler(() => {
        console.debug("⌨️ ESC pressed - destroying active controls");
        this.destroyActivePlayerControls();
      });

      // Clean up the ESC handler when control is disposed
      control.onDispose.on(removeEscKeyHandler);
    } catch (error) {
      console.error("🔥 Error initializing player controls", error);
      this.destroyActivePlayerControls();
      throw error; //We need to throw this error, so that entity scripts stop running if this fails.
    }

    if (urlParams.debugPlayerInput) {
      /* initDebugPlayerInput()
        ?.then((box) => {
          if (this.currentControl === control) {
            control.onDispose.on(() => box.destroy());
          } else {
            box.destroy();
          }
        })
        .catch(console.warn); */
    }

    return this.currentControl;
  }

  /**
   * Returns the type of the currently active player controls.
   * @returns The active control type:
   * - Built-in types from PlayerControlsFactory ("character", "hovercar")
   * - "custom" for user-created controls
   * - null if no controls are active
   */
  getActivePlayerControlsType() {
    return this.currentControlType;
  }

  /**
   * Returns the currently active player controls instance.
   * @returns The active PlayerControlsControl instance or null if none is active
   */
  getActivePlayerControls() {
    return this.currentControl;
  }

  /**
   * Checks if there are any active player controls.
   * @returns true if there are active controls, false otherwise
   */
  hasActiveControls() {
    return this.currentControl !== null;
  }

  /**
   * Returns the entity that is currently being controlled.
   * @returns The controlled entity or null if no controls are active
   */
  getCurrentControlledEntity() {
    return this.currentControl?.entity ?? null;
  }

  /**
   * Cleans up and removes the currently active player controls.
   * This ensures proper disposal of resources and event listeners.
   */
  destroyActivePlayerControls() {
    if (this.currentControl) {
      console.debug("🧹 Cleaning up previous controls");
      this.currentControl.dispose();
      Client.userEntity
        .getCameraControls()
        ?.setCameraMode(CameraMode.FREE_LOOK);
      (Client.userEntity.getCamera() as PerspectiveCamera).layers = allLayers;
      Client.authority.removeAuthorizedEntity(this.currentControl.entity);
      this.currentControl = null;
      this.currentControlType = null;
      this.emit("controls-deactivated");
    } else {
      console.debug("🧹 No active controls to clean up");
    }
    Client.userEntity.setTargetEntity(null);
    Client.getAllEntities()
      .filter((e) => e instanceof UserEntity)
      .forEach((e) => {
        e.setLocationConeVolumetricControlsType(null);
      });
  }
}

/**
 * Initializes debug visualization for player input when enabled via URL parameters.
 * This is a one-time initialization that creates a watch box to display input state.
 */
/*let debugInputAlreadyInitialized = false;
 function initDebugPlayerInput() {
  if (debugInputAlreadyInitialized) return;
  debugInputAlreadyInitialized = true;

  return Promise.all([
    import("@/debug/inspecting/WatchBox"),
    import("@/core/input/PlayerInput"),
  ]).then(([{ createWatchBox }, { PlayerInput }]) => {
    return createWatchBox(() => PlayerInput.toDebugString());
  });
} */

/**
 * Adds an ESC key event handler and returns a cleanup function.
 * @param callback Function to call when ESC is pressed
 * @returns Cleanup function to remove the event listener
 */
function addEscKeyHandler(callback: () => void) {
  const handleKeyDown = (event: Event) => {
    const keyEvent = event as KeyboardEvent;
    if (keyEvent.key === "Escape") {
      // Stop the event from propagating to prevent character input system from interfering
      keyEvent.stopImmediatePropagation();
      keyEvent.preventDefault();
      callback();
    }
  };

  // Use the same event target as the character input system for consistency
  const canvas = document.querySelector("canvas");
  const eventTarget = canvas || window;

  // Use capture phase to ensure we get the event before other handlers
  eventTarget.addEventListener("keydown", handleKeyDown, {
    capture: true,
  });

  return () => {
    eventTarget.removeEventListener("keydown", handleKeyDown, {
      capture: true,
    });
  };
}
