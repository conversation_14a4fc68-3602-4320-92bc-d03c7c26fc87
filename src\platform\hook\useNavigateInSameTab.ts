import { useNavigate } from "react-router-dom";

/**
 * Custom hook for same-tab navigation with URL param preservation
 *
 * Benefits over useNavigate:
 * - Set url params via object
 * - Optionally preserve existing url params
 */
export const useNavigateInSameTab = () => {
  const navigate = useNavigate();

  return (
    path: string,
    options: {
      urlParams?: Record<string, string>;
      preserveUrlParams?: boolean;
    } = {}
  ) => {
    const { urlParams = {}, preserveUrlParams = false } = options;
    let finalUrl = path;

    if (preserveUrlParams || Object.keys(urlParams).length > 0) {
      const currentParams = new URLSearchParams(window.location.search);
      const newParams = new URLSearchParams();

      // If preserving URL params, copy current params first
      if (preserveUrlParams) {
        currentParams.forEach((value, key) => {
          newParams.set(key, value);
        });
      }

      // Override with new params
      Object.entries(urlParams).forEach(([key, value]) => {
        newParams.set(key, value);
      });

      // Add params to URL
      const paramString = newParams.toString();
      if (paramString) {
        finalUrl += `?${paramString}`;
      }
    }

    navigate(finalUrl);
  };
};
