export type PhysicsStepFunction = (
  deltaTime: number,
  numberOfSteps: number,
  stepNumber: number
) => void;

// We limit the number of steps per frame to 3 to avoid the spiral of death
// There was no real testing if this was the best value, so feel free to find a better value and improve it
const MAX_STEPS_PER_FRAME = 3;
export class PhysicsStepController {
  private readonly stepFunction: PhysicsStepFunction;
  private readonly fixedDeltaTime: number;
  private readonly fixedDeltaTimeMilliseconds: number;
  public readonly desiredFPS: number;
  public readonly numberOfSteps: number;

  // Web worker compatible loop state
  private timeoutId: ReturnType<typeof setTimeout> | null = null;
  private lastTime: number = 0;
  private accumulator: number = 0;
  private isRunning: boolean = false;
  private targetTime: number = 0;
  private stepNumber: number = 0;

  constructor(stepFunction: PhysicsStepFunction, desiredFPS: number) {
    this.stepFunction = stepFunction;
    this.desiredFPS = desiredFPS;
    this.fixedDeltaTime = 1 / desiredFPS;
    this.numberOfSteps = 1;
    this.fixedDeltaTimeMilliseconds = 1000 / desiredFPS;
  }

  public startLoop(): void {
    if (this.isRunning) {
      console.warn("⚠️ Physics loop is already running");
      return;
    }

    console.debug(`🏃‍♂️ Physics loop started at ${this.desiredFPS} FPS`);
    this.isRunning = true;
    this.lastTime = performance.now();
    this.targetTime = this.lastTime;
    this.accumulator = 0;
    this.stepNumber = 0;

    this.scheduleNextStep();
  }

  public stopLoop(): void {
    if (!this.isRunning) {
      console.warn("⚠️ Physics loop is not running");
      return;
    }

    this.isRunning = false;

    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    console.debug("🛑 Physics loop stopped");
  }

  private scheduleNextStep(): void {
    if (!this.isRunning) return;
    this.targetTime += this.fixedDeltaTimeMilliseconds;
    const currentTime = performance.now();
    const delay = Math.max(1, this.targetTime - currentTime);
    //1ms is the minimum delay in order to have time to answer messages to the webworker

    this.timeoutId = setTimeout(() => {
      this.executeStep();
    }, delay);
  }

  private executeStep(): void {
    if (!this.isRunning) return;

    const currentTime = performance.now();
    const deltaTime = (currentTime - this.lastTime) / 1000; // Convert to seconds
    this.lastTime = currentTime;

    // Add delta time to accumulator
    this.accumulator += deltaTime;

    let steps = 0;

    while (
      this.accumulator >= this.fixedDeltaTime &&
      steps < MAX_STEPS_PER_FRAME
    ) {
      //Count time taken to execute the step
      const startTime = performance.now();
      this.stepFunction(
        this.fixedDeltaTime,
        this.numberOfSteps,
        this.stepNumber
      );
      const endTime = performance.now();
      const timeTaken = endTime - startTime;

      this.accumulator -= this.fixedDeltaTime;
      steps++;
      this.stepNumber++;

      //If the step took too long, break out of the loop to give time to the webworker to answer messages
      if (timeTaken > this.fixedDeltaTimeMilliseconds) {
        //console.warn("🏀 Physics simulation took too long: ", timeTaken, "ms");
        break;
      }
    }

    // If we are too far behind, reset the accumulator to avoid the spiral of death
    if (this.accumulator > this.fixedDeltaTime * 2) {
      this.accumulator = this.fixedDeltaTime;
    }

    // Schedule the next step
    this.scheduleNextStep();
  }
}
