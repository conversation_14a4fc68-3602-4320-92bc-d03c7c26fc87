import { PhysicsUserDataComponent } from "../behaviors/physics";
import { Query, System, SystemContext, World } from "@nilo/ecs";
import { GameEntityComponent } from "@/core/components";

/** Updates the Jolt physics body from the entity's physicsData */
export class PhysicsUserDataUpdateSystem implements System {
  prefix = "⚡";
  query = new Query({ physicsData: PhysicsUserDataComponent }).trackModified();

  run(world: World, _systemContext: SystemContext): void {
    this.query.forEach(world, (entity, { physicsData }) => {
      const gameEntity = entity.getComponent(GameEntityComponent)!;

      const bodyControl = gameEntity.getPhysicsBodyControl();
      if (bodyControl) {
        bodyControl.updatePhysicsOptions(physicsData.physics || {});
      }
    });
  }
}
