import * as Sentry from "@sentry/react";
import { FormEvent, useEffect, useRef, useState } from "react";
import { toast } from "react-hot-toast";

import { ModalDialog } from "@/components/ui-nilo/ModalDialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { isSentryEnabled } from "@/config/sentry";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import {
  useEditorUIState,
  useEditorUIActions,
} from "@/contexts/EditorUIContext";

const sendSentryFeedback = (feedback: string, email: string, name: string) => {
  Sentry.captureFeedback({ message: feedback, email: email, name: name });
  toast.success(
    "Thanks for sharing! Your feedback will help making <PERSON><PERSON> even better 🌟"
  );
};

const sendMockFeedback = () => {
  toast.success(
    "Thanks for sharing! (Mock feedback - Sentry not available in development) 🌟"
  );
};

export function FeedbackModal() {
  const [feedback, setFeedback] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);
  const { isFeedbackOpen } = useEditorUIState();
  const { toggleFeedback } = useEditorUIActions();
  const { isAuthenticated, user } = useCurrentUser();

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!feedback.trim()) return;

    setIsSubmitting(true);
    try {
      if (isAuthenticated && isSentryEnabled) {
        sendSentryFeedback(
          feedback,
          user?.email ?? "n/a",
          user?.displayName ?? "n/a"
        );
      } else {
        // Fallback for when Sentry is not available
        sendMockFeedback();
      }
      setFeedback("");
    } catch (error) {
      console.error("Failed to submit feedback:", error);
      toast.error("Failed to submit feedback. Please try again.");
    } finally {
      toggleFeedback(false);
      setIsSubmitting(false);
    }
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    if (!isFeedbackOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Handle Cmd+Enter or Ctrl+Enter to submit
      if ((event.metaKey || event.ctrlKey) && event.key === "Enter") {
        event.preventDefault();
        event.stopPropagation();
        if (feedback.trim() && !isSubmitting) {
          // Trigger form submission
          if (formRef.current) {
            formRef.current.requestSubmit();
          }
        }
        return;
      }

      // Handle Escape to cancel
      if (event.key === "Escape") {
        event.preventDefault();
        event.stopPropagation();
        toggleFeedback(false);
        return;
      }
    };

    document.addEventListener("keydown", handleKeyDown, { capture: true });

    return () => {
      document.removeEventListener("keydown", handleKeyDown, { capture: true });
    };
  }, [isFeedbackOpen, feedback, isSubmitting, toggleFeedback]);

  return (
    <ModalDialog
      open={isFeedbackOpen}
      onOpenChange={toggleFeedback}
      title="Report an Issue"
      description="Help us improve by reporting bugs or issues you've encountered."
      className="w-full max-w-sm sm:max-w-md"
      footer={
        <div className="flex justify-end gap-2">
          <Button
            variant="secondary"
            type="button"
            onClick={() => toggleFeedback(false)}
          >
            Cancel
            <span className="ml-2 text-xs text-muted-foreground">Esc</span>
          </Button>
          <Button
            variant="primary"
            type="submit"
            form="feedback-form"
            disabled={isSubmitting || !feedback.trim()}
          >
            {isSubmitting ? "Sending..." : "Submit"}
          </Button>
        </div>
      }
    >
      <form
        ref={formRef}
        id="feedback-form"
        onSubmit={handleSubmit}
        className="space-y-4"
      >
        <div>
          <label htmlFor="feedback" className="sr-only">
            Describe the issue
          </label>
          <Textarea
            id="feedback"
            value={feedback}
            onChange={(e) => setFeedback(e.target.value)}
            onKeyDown={(e) => e.stopPropagation()}
            onKeyUp={(e) => e.stopPropagation()}
            placeholder="Describe what you expected and what happened instead..."
            className="min-h-[150px] resize-none"
            required
          />
        </div>
      </form>
    </ModalDialog>
  );
}
