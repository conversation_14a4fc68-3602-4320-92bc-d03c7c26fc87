import { Vector3 } from "three";
import { CompoundModelBuilder } from "@/core/util/compoundModel/CompoundModelBuilder";
import { CompoundModelTaskService } from "@nilo/firebase-schema";
import { GameEntity } from "@/core/entity/Entity";
import { ClientAuthority } from "@/networking/ClientAuthority";

export interface ModelBuilderOptions {
  prompt: string;
  maxShapes?: number;
  position?: Vector3;
  groupTag?: string;
  service?: CompoundModelTaskService;
  glue?: boolean;
}

export interface ModelBuilderCallbacks {
  onStart?: () => void | Promise<void>;
  onShapeAdded?: (entity: GameEntity) => void | Promise<void>;
  onProgress?: (
    progress: number,
    completedShapes: number,
    totalShapes: number
  ) => void | Promise<void>;
  onCompleted?: (entities: GameEntity[]) => void | Promise<void>;
  onError?: (error: {
    message: string;
    suggestion: string;
  }) => void | Promise<void>;
  onFinally?: () => void | Promise<void>;
}

export class ApiModelBuilder {
  private _authority: ClientAuthority;
  private _builder: CompoundModelBuilder;

  constructor(authority: ClientAuthority) {
    this._authority = authority;
    this._builder = new CompoundModelBuilder(authority);
  }

  /**
   * Generate a compound model from a text prompt using AI
   * @param options Configuration options for model generation
   * @param callbacks Optional callbacks for tracking generation progress
   * @returns Promise that resolves to an array of generated game entities
   */
  public async generateModel(
    options: ModelBuilderOptions,
    callbacks?: ModelBuilderCallbacks
  ): Promise<GameEntity[]> {
    if (!this._authority.hasAuthority()) {
      throw new Error("No authority to generate models");
    }

    const {
      prompt,
      maxShapes = 20,
      position = new Vector3(0, 0, 0),
      groupTag = "ai-generated",
      service = undefined,
      glue = false,
    } = options;

    return this._builder.generate({
      prompt,
      maxShapes,
      style: "realistic", // Default style, not exposed in API
      position,
      groupTag,
      service,
      glue,
      onStart: callbacks?.onStart,
      onShapeAdded: callbacks?.onShapeAdded
        ? async (_shape, entity) => {
            await callbacks.onShapeAdded?.(entity);
          }
        : undefined,
      onProgress: callbacks?.onProgress,
      onCompleted: callbacks?.onCompleted
        ? async (result) => {
            await callbacks.onCompleted?.(result.entities);
          }
        : undefined,
      onError: callbacks?.onError,
      onFinally: callbacks?.onFinally,
    });
  }

  /**
   * Clean up resources and cancel any ongoing generation
   */
  public cleanup(): void {
    this._builder.cleanup();
  }
}
