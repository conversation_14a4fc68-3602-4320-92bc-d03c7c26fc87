import React from "react";
import { ChevronDown } from "lucide-react";
import { ErrorBoundary } from "react-error-boundary";
import { Button } from "../ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../ui/collapsible";

interface PanelCollapsibleProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  className?: string;
  dontRenderOnEmpty?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
}

export function PanelCollapsible({
  title,
  children,
  defaultOpen = true,
  className = "space-y-4",
  dontRenderOnEmpty = true,
  onOpenChange,
}: PanelCollapsibleProps) {
  if (dontRenderOnEmpty && !children) {
    return null;
  }

  return (
    <Collapsible
      defaultOpen={defaultOpen}
      className={`group ${className}`}
      onOpenChange={onOpenChange}
    >
      <CollapsibleTrigger asChild className="w-full p-0 m-0 h-auto">
        <Button
          variant="label"
          className="w-full justify-between !px-0 py-nilo-size-component-header-h2 h-auto data-[state=closed]:py-1"
        >
          <span className="text-nilo-component-label-2 text-base font-semibold">
            {title}
          </span>
          <ChevronDown className="h-4 w-4 transition-transform group-data-[state=open]:rotate-180" />
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="space-y-4 pb-nilo-size-component-header-h2">
        <ErrorBoundary
          fallback={
            <div className="flex flex-col items-center justify-center p-8 text-center text-white/60 space-y-4">
              Something went wrong
            </div>
          }
        >
          {children}
        </ErrorBoundary>
      </CollapsibleContent>
    </Collapsible>
  );
}
