import { Quaternion, Q<PERSON>rn<PERSON><PERSON><PERSON> } from "three";
import { DynamicProperty, DynamicPropertyKey } from "../types/webworker";

//This function is necessary because comlink fails to serialize Quaternion as {x:0;y:0;z:0;w:0}
//It just serializes as {_x:0;_y:0;_z:0;_w:0} because it uses private properties.
export function ensureQuaternionLike(
  quaternion: Quaternion | QuaternionLike
): QuaternionLike {
  return {
    x: quaternion.x,
    y: quaternion.y,
    z: quaternion.z,
    w: quaternion.w,
  };
}

export function getDefaultValueForDynamicProperty(
  dynamicProperty: DynamicPropertyKey
): DynamicProperty {
  switch (dynamicProperty) {
    case "linearVelocity":
      return {
        x: 0,
        y: 0,
        z: 0,
      };
    case "angularVelocity":
      return {
        x: 0,
        y: 0,
        z: 0,
      };
    case "contactCount":
      return 0;
    case "isActive":
      return false;
    default:
      throw new Error(
        `Default value for Dynamic property ${dynamicProperty} not implemented`
      );
  }
}
