import { ComponentId, EntityId } from "@nilo/network";

/**
 * An opaque but dumb container for an ECS world's entities.
 */
export class EntityStore {
  private _entities: Map<EntityId, EntityNetState> = new Map();

  /** Spawn an entity with a given ID in this store. */
  public spawn(id: EntityId): EntityNetState {
    // fetch an existing entity if one is there
    const old = this._entities.get(id);
    if (old) {
      return old;
    }

    // otherwise create a default entity
    const entity = new EntityNetState();
    this._entities.set(id, entity);
    return entity;
  }

  /** Ensures that an entity with a given ID is spawned.
   * @returns True if the entity was not already spawned.
   */
  public ensureSpawned(id: EntityId): boolean {
    if (this._entities.has(id)) {
      return false;
    } else {
      this.spawn(id);
      return true;
    }
  }

  /** Kills an entity by ID. Returns it, if there was one. */
  public kill(id: EntityId): EntityNetState | undefined {
    const old = this._entities.get(id);
    this._entities.delete(id);
    return old;
  }

  /** Checks if an entity is alive in this store. */
  public has(id: EntityId): boolean {
    return this._entities.has(id);
  }

  /** Looks up an entity by ID. */
  public get(id: EntityId): EntityNetState | undefined {
    return this._entities.get(id);
  }

  /** Returns a map of all live entities. */
  public alive(): Map<EntityId, EntityNetState> {
    return new Map(this._entities);
  }
}

/**
 * Stores instantaneous information about a networked entity as bags of opaque serialized data.
 */
export class EntityNetState {
  /** Each component's serialized data */
  private _components: Map<ComponentId, Uint8Array> = new Map();

  /**
   * Inserts a component into this entity.
   * @param component The ID of the component to insert.
   * @param data The serialized data of this component.
   * @returns True if the component's value has been changed.
   */
  public insert(component: ComponentId, data: Uint8Array): boolean {
    const existing = this._components.get(component);
    if (
      existing &&
      existing.length === data.length &&
      existing.every((byte, index) => byte === data[index])
    ) {
      return false;
    }

    this._components.set(component, data);
    return true;
  }

  /**
   * Removes a component from this entity.
   * @param component The ID of the component to remove.
   * @returns True if the component has been removed or false if this entity did not have it.
   */
  public remove(component: ComponentId): boolean {
    return this._components.delete(component);
  }

  /** Gets the data for a component. */
  public get(component: ComponentId): Uint8Array | undefined {
    return this._components.get(component);
  }

  /** Returns the set of all components. */
  public all(): Set<ComponentId> {
    return new Set(this._components.keys());
  }
}
