import { getLogger } from "@nilo/logger";
import { Subject, Subscription } from "rxjs";

const logger = getLogger();

interface TimeData {
  delta: number;
  elapsed: number;
}

export class Loop {
  private _ups: number;
  private _interval: number;
  private _prev: number;
  private _startTimestamp: number;
  private _frames: number;
  private _timePassed: number;

  private _subject = new Subject<TimeData>();

  // eslint-disable-next-line no-undef
  private _immediate: NodeJS.Immediate | null = null;

  private _debug: boolean;

  constructor(ups: number, debug = false) {
    if (ups <= 0) {
      throw new Error("UPS must be greater than 0");
    }

    this._ups = ups;
    this._interval = 1000 / this._ups;
    this._prev = performance.now();
    this._startTimestamp = this._prev;
    this._frames = 0;
    this._timePassed = 0;

    this._debug = debug;

    this.cycle = this.cycle.bind(this);
  }

  public subscribe(next: (data: TimeData) => void): Subscription {
    return this._subject.subscribe({
      next,
    });
  }

  private cycle() {
    const now = performance.now();
    const delta = now - this._prev;

    if (delta > this._interval) {
      this._timePassed += delta;

      // update time stuffs
      this._subject.next({
        delta,
        elapsed: now - this._startTimestamp,
      });

      this._prev = now - (delta % this._interval);
      this._frames++;

      if (this._timePassed >= 1000) {
        if (this._debug) {
          logger.debug("FPS", this._frames);
        }
        this._frames = 0;
        this._timePassed = 0;
      }
    }

    this._immediate = setImmediate(this.cycle);
  }

  public start() {
    if (this._immediate) {
      return;
    }

    this._startTimestamp = performance.now();
    this._immediate = setImmediate(this.cycle);
  }

  public stop() {
    if (this._immediate) {
      clearImmediate(this._immediate);
      this._immediate = null;
    }
  }
}
