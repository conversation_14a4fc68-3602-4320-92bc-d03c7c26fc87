import { trackEvent } from "./eventUtils";

/**
 * Menu scope types for radial menu tracking
 */
export type MenuScope = "global" | "primitive" | "entity" | "rigged_entity";

/**
 * Action types for radial menu tracking
 */
export type ActionType = "create" | "edit" | "play";

/**
 * Valid action keys for each menu scope and action type combination
 */
export type ActionKey =
  // Global create actions
  | "cube"
  | "sphere"
  | "cylinder"
  | "cone"
  | "pipe"
  | "plane"
  | "torus"
  | "torus_knot"
  | "ring"
  | "capsule"
  | "wedge"
  | "octahedron"
  | "tetrahedron"
  | "generate_from_text"
  | "generate_from_image"
  | "particles"
  | "live_canvas"
  | "import_3d_objects"
  // Edit/Play actions (shared across primitive, entity, rigged_entity)
  | "orange"
  | "yellow"
  | "green"
  | "cyan"
  | "blue"
  | "purple"
  | "magenta"
  | "pink"
  | "brown"
  | "white"
  | "gray"
  | "black"
  | "red"
  | "default_material"
  | "wireframe"
  | "glass"
  | "distort"
  | "lock"
  | "unlock"
  | "code"
  | "delete"
  | "duplicate"
  | "export"
  | "duplicate_controls"
  | "reset_transform"
  | "setup_character"
  | "controls"
  | "active_ragdoll"
  | "play_animation"
  | "stop_animation"
  | "effect_editor"
  | "set_lod"
  // Physics actions
  | "make_trigger"
  | "remove_trigger"
  | "set_convex_hull"
  | "set_decomposition"
  | "enable_physics"
  | "disable_physics"
  // Origin actions
  | "set_origin_bottom_center"
  | "set_origin_geometry_center"
  | "set_origin_native"
  // Gameplay actions
  | "add_respawn_point"
  | "remove_respawn_point"
  | "add_respawn"
  | "remove_respawn"
  | "add_deal_damage"
  | "remove_deal_damage"
  | "add_health"
  | "remove_health";

/**
 * Parent action keys for nested radial menu options
 */
export type ParentActionKey =
  | "create_primitive"
  | "create_object"
  | "material"
  | "character"
  | "color";

/**
 * Track a core builder action from the radial menu
 * @param menuScope - The scope of the menu (global, primitive, entity, rigged_entity)
 * @param actionType - The type of action (create, edit, play)
 * @param actionKey - The specific action performed
 * @param parentActionKey - Optional parent action if this is a nested option
 */
export function trackCoreBuilderAction(
  menuScope: MenuScope,
  actionType: ActionType,
  actionKey: ActionKey,
  parentActionKey?: ParentActionKey
): void {
  trackEvent("core_builder_action", {
    menu_scope: menuScope,
    action_type: actionType,
    action_key: actionKey,
    ...(parentActionKey && { parent_action_key: parentActionKey }),
  });
}
