import { useState } from "react";
import { toast } from "react-hot-toast";
import { deleteWorld } from "@/platform/actions/world-actions";

interface UseDeleteWorldOptions {
  onSuccess?: () => void;
  onError?: (error: unknown) => void;
}

export function useDeleteWorld(options?: UseDeleteWorldOptions) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleDeleteWorld = async (worldId: string, userId: string) => {
    if (!worldId) return;
    if (!userId) {
      toast.error("You must be logged in to delete a world.");
      return;
    }

    setIsDeleting(true);
    try {
      await deleteWorld(worldId, userId);
      // Don't close the modal here - let the ConfirmationModal handle it
      // This prevents double state updates and potential race conditions
      options?.onSuccess?.();
    } catch (error) {
      toast.error("Failed to delete world. Please try again.");
      options?.onError?.(error);
    } finally {
      setIsDeleting(false);
    }
  };

  const openDeleteConfirm = () => setShowDeleteConfirm(true);
  const closeDeleteConfirm = () => setShowDeleteConfirm(false);

  return {
    isDeleting,
    showDeleteConfirm,
    handleDeleteWorld,
    openDeleteConfirm,
    closeDeleteConfirm,
  };
}
