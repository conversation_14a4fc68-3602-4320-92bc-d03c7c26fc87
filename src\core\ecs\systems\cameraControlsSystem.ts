import { CameraComponent } from "../components/CameraComponent";
import { Query, System, SystemContext, World } from "@nilo/ecs";

/**
 * System responsible for updating camera controls in the ECS loop.
 */
export class CameraControlsSystem implements System {
  entityFilter = { camera: CameraComponent };
  name = "CameraControlsSystem";
  prefix = "📷";

  private query = new Query({ camera: CameraComponent });

  init(_world: World, systemContext: SystemContext): void {
    systemContext.info("Camera controls system initialized");
  }

  run(world: World, systemContext: SystemContext): void {
    // Convert deltaTime from seconds to milliseconds for OrbitControls
    const deltaTimeMs = systemContext.deltaTime * 1000;

    this.query.forEach(world, (_entity, { camera }) => {
      // Update camera controls with delta time in milliseconds
      camera.updateControls(deltaTimeMs);
    });
  }
}
