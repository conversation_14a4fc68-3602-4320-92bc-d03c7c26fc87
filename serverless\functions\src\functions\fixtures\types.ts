import { Range, RunFixtureRequest } from "@nilo/firebase-schema";
import { sfwDictionaries } from "@nilo/utilities";
import { CallableRequestWithAuth } from "../utils";

export type FixtureRequest<T extends RunFixtureRequest["fixture"]> =
  CallableRequestWithAuth<Extract<RunFixtureRequest, { fixture: T }>>;

export function pickRandom(from: Range): number;
// eslint-disable-next-line no-redeclare
export function pickRandom<T>(from: T[]): T;
// eslint-disable-next-line no-redeclare
export function pickRandom<T>(from: Range | T[]): number | T {
  if (Array.isArray(from)) {
    return from[Math.floor(Math.random() * from.length)];
  }
  const range = from;
  return Math.floor(Math.random() * (range.max - range.min + 1)) + range.min;
}

export function randomText(words: number): string {
  return Array.from({ length: words }, () =>
    pickRandom(
      pickRandom([
        sfwDictionaries.adjectives,
        sfwDictionaries.animals,
        sfwDictionaries.colors,
      ])
    )
  ).join(" ");
}
