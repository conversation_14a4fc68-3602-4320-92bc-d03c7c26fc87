import { Quaternion, Vector3 } from "three";
import { TransformBehavior } from "../behaviors";
import { PhysicsDataBehavior } from "@/core/ecs/behaviors/physics";
import { PhysicsUserData } from "@/types";
import { MeshBehavior } from "@/core/ecs/behaviors/mesh";
import { Prefab } from "@nilo/ecs";
import { ModelData } from "@/liveblocks.config";

export function createMeshEntityPrefab(options: {
  position?: Vector3;
  rotation?: Quaternion;
  scale?: Vector3;
  color?: number;
  userId: string;
  generateFromPrompt?: string;
  modelData?: ModelData;
  physicsUserData?: PhysicsUserData;
  tags?: string[];
}) {
  const {
    position,
    rotation,
    scale,
    color = 0xffffff,
    userId,
    generateFromPrompt,
    modelData = null,
    physicsUserData = {},
    tags = [],
  } = options;

  return new Prefab("meshEntity", [
    new TransformBehavior(position?.clone(), rotation?.clone(), scale?.clone()),
    new PhysicsDataBehavior(physicsUserData),
    new MeshBehavior(modelData, color, userId, generateFromPrompt, tags),
  ]);
}
