import { FirebaseError } from "firebase/app";
import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";

import { LoadingScreen } from "@/components/platform/LoadingScreen";
import { PlatformHeader } from "@/components/platform/PlatformHeader";
import { Typography } from "@/components/ui-nilo/Typography";
import { Button } from "@/components/ui/button";
import {
  createPrivateWorld,
  remixWorld,
} from "@/platform/actions/world-actions";
import { useNiloPlatformNavigate } from "@/platform/hook/useNiloPlatformNavigate";

const createWorld = async (remixSourceWorldId: string | null) => {
  if (remixSourceWorldId === null) {
    // Create new world
    return await createPrivateWorld();
  } else {
    // Remix existing world
    return await remixWorld(remixSourceWorldId);
  }
};

export const CreateWorldPage = () => {
  const { navigateToWorld } = useNiloPlatformNavigate();
  const [searchParams] = useSearchParams();
  const [error, setError] = useState<unknown | null>(null);

  const remixSourceWorldId = searchParams.get("remixFrom");

  useEffect(() => {
    const onMounted = async () => {
      try {
        setError(null);

        if (remixSourceWorldId !== null && !remixSourceWorldId) {
          throw new Error("Invalid remixFrom world id");
        }

        const worldId = await createWorld(remixSourceWorldId ?? null);

        navigateToWorld(worldId);
      } catch (err) {
        console.error("❌ Error creating world:", err);
        setError(err);
      }
    };

    onMounted();
  }, [navigateToWorld, remixSourceWorldId]);

  if (error) {
    return <ErrorPage error={error} />;
  }

  return (
    <div className="h-screen flex flex-col bg-black">
      <PlatformHeader showLogo={true} showUser={true} />
      <div className="flex-1 flex items-center justify-center">
        <LoadingScreen
          message={
            remixSourceWorldId ? "Creating your remix" : "Creating your world"
          }
        />
      </div>
    </div>
  );
};

const ErrorPage = ({ error }: { error: unknown }) => {
  const getErrorMessage = (err: unknown): [string | null, string] => {
    // Handle Firebase errors
    if (err instanceof FirebaseError) {
      return [err.code, err.message];
    }

    // Handle standard Error instances
    if (err instanceof Error) {
      return [null, err.message];
    }

    // Handle string errors
    if (typeof err === "string") {
      return [null, err];
    }

    // Handle objects with message property
    if (err && typeof err === "object" && "message" in err) {
      return [null, String(err.message)];
    }

    return [null, "An unexpected error occurred"];
  };

  const [errorCode, errorDetails] = getErrorMessage(error);

  return (
    <div className="h-screen flex flex-col bg-black">
      <PlatformHeader showLogo={true} showUser={true} />
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <Typography.Title
            level={1}
            weight="bold"
            color="light"
            className="mb-6 text-red-400"
          >
            Error
          </Typography.Title>
          <div className="bg-red-900/20 border border-red-800/50 rounded-lg p-6 mb-6">
            {errorCode && (
              <div className="text-red-300 mb-2 font-mono">{errorCode}</div>
            )}
            <Typography.Body level={1} color="light" className="text-red-200">
              {errorDetails}
            </Typography.Body>
          </div>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    </div>
  );
};
