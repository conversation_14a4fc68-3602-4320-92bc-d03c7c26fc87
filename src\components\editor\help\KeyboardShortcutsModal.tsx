import { Fragment, useMemo, useState, type ReactNode } from "react";

import {
  getKeyboardShortcutsInfo,
  KeyboardShortcut,
  parseKeyboardShortcutString,
} from "./getKeyboardShortcutsInfo";
import { Kbd } from "@/components/ui-nilo/Kbd";
import { Typography } from "@/components/ui-nilo/Typography";
import { ModalDialog } from "@/components/ui-nilo/ModalDialog";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Join } from "@/components/utility/Join";
import { keyboardMappingDict } from "@/config/keyboardMapping";
import {
  useEditorUIActions,
  useEditorUIState,
} from "@/contexts/EditorUIContext";
import { fuzzyMatch } from "@/core/util/search/fuzzysearch";

export function KeyboardShortcutsModal() {
  const { isKeyboardShortcutsOpen } = useEditorUIState();
  const { toggleKeyboardShortcuts } = useEditorUIActions();
  const { searchQuery, setSearchQuery, filteredShortcuts } =
    useKeyboardShortcutsSearch();

  return (
    <ModalDialog
      open={isKeyboardShortcutsOpen}
      onOpenChange={toggleKeyboardShortcuts}
      title="Keyboard Shortcuts"
      description="View and learn about available keyboard shortcuts for Nilo."
      descriptionHidden={true}
      className="max-w-7xl max-h-[calc(100vh-2rem)] h-[500px]"
    >
      <div className="flex flex-col h-full gap-0">
        <div className="flex-shrink-0">
          <Input
            type="text"
            placeholder="Search shortcuts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => e.stopPropagation()}
            onKeyUp={(e) => e.stopPropagation()}
            className="w-full h-[2rem]"
          />
        </div>

        <Separator className="mt-4" />

        <div className="flex-1 min-h-0 overflow-auto space-y-6 scrollbar-thin-nilo -mr-3 pt-4">
          {filteredShortcuts.map((section, index) => (
            <Fragment key={index}>
              {index > 0 && <Separator className="my-4" />}

              <Typography.Heading level={1} className="mb-8 text-center">
                {section.title}
              </Typography.Heading>

              <div className="space-y-2">
                {section.shortcuts.map((shortcut) => (
                  <div
                    key={shortcut.label}
                    className="flex justify-between items-start"
                  >
                    <RowPart>
                      <Typography.Label level={2}>
                        {shortcut.label}
                      </Typography.Label>
                    </RowPart>
                    <KeyboardShortcutDisplay shortcut={shortcut} />
                  </div>
                ))}
              </div>
            </Fragment>
          ))}
        </div>
      </div>
    </ModalDialog>
  );
}

function RowPart({ children }: { children: ReactNode }) {
  return <div className="h-8 flex items-center">{children}</div>;
}

/**
 * Renders a single keyboard combination (array of keys)
 */
function renderKeyboardCombination(keys: string[], separator: string) {
  return (
    <div className="flex items-center gap-1">
      <Join
        separator={
          <span className="text-nilo-text-secondary/40 text-sm">
            {separator}
          </span>
        }
      >
        {keys.map((key) => (
          <div key={key} className="flex items-center gap-1">
            <Kbd>{key}</Kbd>
          </div>
        ))}
      </Join>
    </div>
  );
}

/**
 * Unified component for displaying keyboard shortcuts
 * Handles both action-based and direct keys-based shortcuts
 */
function KeyboardShortcutDisplay({ shortcut }: { shortcut: KeyboardShortcut }) {
  let combinations: string[][] = [];

  // If shortcut has an action, get combinations from keyboard mapping
  if (shortcut.action) {
    const shortcutString = keyboardMappingDict[shortcut.action];
    if (shortcutString) {
      combinations = parseKeyboardShortcutString(shortcutString);
    }
  }
  // If shortcut has direct keys, parse them using the helper
  else if (shortcut.keys) {
    combinations = parseKeyboardShortcutString(shortcut.keys);
  }

  if (combinations.length === 0) {
    return null;
  }

  // Render all combinations
  return (
    <div className="flex flex-col items-end gap-1">
      {combinations.map((keys, index) => (
        <RowPart key={index}>
          {renderKeyboardCombination(keys, shortcut.separator ?? "+")}
        </RowPart>
      ))}
    </div>
  );
}

/**
 * Custom hook for keyboard shortcuts search functionality
 */
function useKeyboardShortcutsSearch() {
  const [searchQuery, setSearchQuery] = useState("");

  // Filter shortcuts based on search query
  const filteredShortcuts = useMemo(() => {
    if (!searchQuery.trim()) {
      return getKeyboardShortcutsInfo();
    }

    const query = searchQuery.toLowerCase();
    return getKeyboardShortcutsInfo()
      .map((section) => ({
        ...section,
        shortcuts: section.shortcuts.filter((shortcut) => {
          // Search in label
          if (fuzzyMatch(query, shortcut.label.toLowerCase())) {
            return true;
          }

          // Search in keys
          let combinations: string[][] = [];
          if (shortcut.action) {
            const shortcutString = keyboardMappingDict[shortcut.action];
            if (shortcutString) {
              combinations = parseKeyboardShortcutString(shortcutString);
            }
          } else if (shortcut.keys) {
            combinations = parseKeyboardShortcutString(shortcut.keys);
          }

          // Check if any key combination matches the search query
          return combinations.some((keys) =>
            keys.some((key) => fuzzyMatch(query, key.toLowerCase()))
          );
        }),
      }))
      .filter((section) => section.shortcuts.length > 0); // Only show sections with matching shortcuts
  }, [searchQuery]);

  return {
    searchQuery,
    setSearchQuery,
    filteredShortcuts,
  };
}
