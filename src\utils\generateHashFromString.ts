/**
 * Generates a stable, collision-resistant hash from a long string.
 * Uses a hash-based approach to avoid issues with long strings or special characters.
 */
export function generateHashFromString(input: string): string {
  if (!input) return "empty";

  // Simple hash function for deterministic key generation
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  return Math.abs(hash).toString(36);
}
