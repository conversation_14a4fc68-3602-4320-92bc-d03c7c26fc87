import keyboardShortcutsData from "@/config/ui-content/keyboard-mappings-info.yaml";
import { KeyboardMappingID } from "@/config/keyboardMapping";

export interface KeyboardShortcut {
  label: string;
  action?: KeyboardMappingID;
  keys?: string;
  separator?: string;
}

export interface KeyboardShortcutsSection {
  title: string;
  shortcuts: KeyboardShortcut[];
}

export type KeyboardShortcutsData = KeyboardShortcutsSection[];

/**
 * Parses a keyboard shortcut string into display format
 * e.g., "Ctrl + KeyQ" -> ["Ctrl", "Q"]
 * e.g., "KeyW, KeyA, KeyS, KeyD" -> [["W"], ["A"], ["S"], ["D"]]
 */
export function parseKeyboardShortcutString(
  shortcutString: string
): string[][] {
  const combinations = shortcutString
    .split(",")
    .map((s) => s.trim())
    .filter((s) => s.length > 0);

  return combinations.map((combination) => {
    const parts = combination.split("+").map((part) => part.trim());

    return parts.map((part) => {
      // Handle keys - remove "Key" prefix and capitalize
      if (part.startsWith("Key")) {
        return part.substring(3); // Remove "Key" prefix
      }

      // Everything else can be capitalized programmatically
      return part.charAt(0).toUpperCase() + part.slice(1).toLowerCase();
    });
  });
}

/**
 * Global function to get keyboard shortcuts information
 * Returns typed keyboard shortcuts data from the YAML configuration
 */
export function getKeyboardShortcutsInfo(): KeyboardShortcutsData {
  return keyboardShortcutsData as KeyboardShortcutsData;
}
