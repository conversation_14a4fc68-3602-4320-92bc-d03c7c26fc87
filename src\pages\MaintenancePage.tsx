import { useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>, Loader2 } from "lucide-react";
import { Typography } from "@/components/ui-nilo/Typography";
import { Card, CardContent } from "@/components/ui/card";

export default function MaintenancePage() {
  useEffect(() => {
    // Prevent scrolling on the maintenance page
    document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = "unset";
    };
  }, []);

  return (
    <div className="h-screen w-screen bg-black flex items-center justify-center px-4">
      <Card className="max-w-md w-full bg-card border-border">
        <CardContent className="pt-12 pb-12 text-center space-y-6">
          {/* Animated Icon */}
          <div className="relative w-24 h-24 mx-auto">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-full animate-pulse" />
            <div className="relative flex items-center justify-center w-full h-full">
              <Wrench className="w-12 h-12 text-icon-primary animate-[spin_4s_linear_infinite]" />
            </div>
          </div>

          {/* Title */}
          <div className="space-y-2">
            <Typography.Title level={2} color="light" className="mb-2">
              Under Maintenance
            </Typography.Title>
            <Typography.Body level={1} color="light" weight="medium">
              We&apos;re currently performing system maintenance
            </Typography.Body>
          </div>

          {/* Message */}
          <div className="space-y-4">
            <Typography.Body
              level={2}
              color="light"
              className="max-w-sm mx-auto opacity-70"
            >
              Our team is working hard to improve your experience. We&apos;ll be
              back online shortly.
            </Typography.Body>

            <div className="flex items-center justify-center gap-2 text-muted-foreground">
              <Clock className="w-4 h-4" />
              <Typography.Body level={3} color="light" className="opacity-60">
                This usually takes just a few minutes
              </Typography.Body>
            </div>
          </div>

          {/* Loading indicator */}
          <div className="flex items-center justify-center gap-2 pt-4">
            <Loader2 className="w-4 h-4 animate-spin text-muted-foreground" />
            <Typography.Body level={3} color="light" className="opacity-60">
              Checking status...
            </Typography.Body>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
