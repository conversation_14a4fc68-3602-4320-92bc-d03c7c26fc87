import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import ColorShowcaseSection from "./ui-showcase-sections/ColorShowcaseSection";
import ShadcnSection from "./ui-showcase-sections/ShadcnSection";
import TypographyShowcaseSection from "./ui-showcase-sections/TypographyShowcaseSection";
import { Typography } from "@/components/ui-nilo/Typography";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";

const UIShowcase = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("shadcn");

  // Extract tab from URL path
  useEffect(() => {
    const pathSegments = location.pathname.split("/");
    const tabFromUrl = pathSegments[pathSegments.length - 1];

    if (["shadcn", "typography", "colors"].includes(tabFromUrl)) {
      setActiveTab(tabFromUrl);
    } else {
      // Default to shadcn if no valid tab in URL
      setActiveTab("shadcn");
      navigate("shadcn", { replace: true });
    }
  }, [location.pathname, navigate]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    navigate(`/internal/uiShowcase/${value}`);
  };

  return (
    <div
      className="h-screen text-icon-primary overflow-y-auto relative p-6 scrollbar-thin-nilo rounded-xl"
      style={{
        backgroundImage: "url(/images/previews/empty.webp)",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      {/* Content */}
      <div className="relative z-10 mx-auto space-y-8 pt-8 pb-36 max-w-6xl">
        {/* Header and Tabs Card */}
        <Card className="mx-auto">
          <CardContent className="space-y-6">
            {/* Header */}
            <header className="text-center">
              <Typography.Title level={1} color="brand" className="mb-8">
                Nilo UI-Kit
              </Typography.Title>
            </header>

            {/* Tabs Navigation */}
            <Tabs
              value={activeTab}
              onValueChange={handleTabChange}
              className="w-full"
            >
              <TabsList className="max-w-md mx-auto">
                <TabsTrigger value="shadcn">Shadcn Components</TabsTrigger>
                <TabsTrigger value="typography">Typography</TabsTrigger>
                <TabsTrigger value="colors">Color Palette</TabsTrigger>
              </TabsList>
            </Tabs>
          </CardContent>
        </Card>

        {/* Tab Content - positioned separately */}
        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="w-full"
        >
          <TabsContent value="shadcn" className="mt-8">
            <ShadcnSection />
          </TabsContent>

          <TabsContent value="typography" className="mt-8">
            <TypographyShowcaseSection />
          </TabsContent>

          <TabsContent value="colors" className="mt-8">
            <ColorShowcaseSection />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default UIShowcase;
