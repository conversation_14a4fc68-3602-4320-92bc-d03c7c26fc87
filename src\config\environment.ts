import { isolatedEnvironment } from "./isolatedEnv";
import { isLocalNetwork } from "@/debug/isLocalNetwork";

export const isProduction =
  process.env.NODE_ENV === "production" &&
  process.env.VERCEL_ENV === "production";

export const isPreview =
  process.env.NODE_ENV === "production" && process.env.VERCEL_ENV === "preview";

export const isDevelopment = process.env.NODE_ENV === "development";

export const isLocal =
  process.env.NODE_ENV === "development" && isLocalNetwork();

/**
 * We use the isolated environment name to determine the environment because
 * they also take care of forcing the environment to a specific branch name
 * using the url params, e.g. ?forceISE=main
 */
export const isMain = isolatedEnvironment.name === "main";
export const isStable = isolatedEnvironment.name === "prod";
export const isRelease = isolatedEnvironment.name.startsWith("release");

export const isStaging = isPreview && (isRelease || isMain);
