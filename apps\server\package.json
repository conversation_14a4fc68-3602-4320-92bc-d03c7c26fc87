{"name": "server", "version": "1.0.0", "description": "", "main": "dist/index.mjs", "type": "module", "scripts": {"build": "rollup -c rollup.config.prod.mjs", "start": "node -r source-map-support/register ./dist/index.mjs", "dev": "cross-env UNSAFE=true SKIP_AGONES=true rollup -c rollup.config.dev.mjs -w", "dev-agones": "cross-env UNSAFE=true rollup -c rollup.config.dev.mjs -w", "install:webtransport": "pushd node_modules/@fails-components/webtransport-transport-http3-quiche; pnpm run install; popd", "test": "jest", "test:coverage": "jest --coverage"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-run": "^3.1.0", "@rollup/plugin-typescript": "^12.1.1", "@types/express": "^5.0.0", "@types/node": "^22.14.0", "@types/node-forge": "^1.3.11", "@types/semver": "^7.5.8", "@types/ssh2": "^1.15.1", "@types/ws": "^8.5.13", "cross-env": "^7.0.3", "rollup": "^4.28.0", "semver": "^7.6.3", "tslib": "^2.8.1", "tsx": "^4.19.2", "@jest/globals": "^29.7.0", "@types/jest": "^29.5.12", "jest": "^29.7.0", "ts-jest": "^29.4.0"}, "dependencies": {"@dotenvx/dotenvx": "^1.32.1", "@evenstar/byteform": "^1.3.0", "@fails-components/webtransport": "^1.4.1", "@fails-components/webtransport-transport-http3-quiche": "^1.4.1", "@google-cloud/agones-sdk": "^1.48.0", "@google-cloud/logging-winston": "^6.0.0", "@nilo/logger": "workspace:^", "@nilo/network": "workspace:^", "@peculiar/x509": "^1.12.3", "acme-client": "^5.4.0", "chalk": "^5.4.1", "emittery": "^1.0.3", "exit-hook": "^4.0.0", "express": "^4.21.2", "node-forge": "^1.3.1", "rollup-plugin-inject-process-env": "^1.3.1", "rxjs": "^7.8.1", "source-map-support": "^0.5.21", "winston": "^3.17.0"}}