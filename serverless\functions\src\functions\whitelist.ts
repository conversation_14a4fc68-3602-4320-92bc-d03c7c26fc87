import { defineSecret } from "firebase-functions/params";
import { DbCollections, DbWhitelistedEmails } from "@nilo/firebase-schema";
import { collection } from "../typedFirebase";
import { Logger } from "../logger";

// Airtable secrets for whitelist checking
const AIRTABLE_API_KEY = defineSecret("AIRTABLE_API_KEY");
const AIRTABLE_BASE_ID = defineSecret("AIRTABLE_BASE_ID");
const AIRTABLE_TABLE_ID = defineSecret("AIRTABLE_TABLE_ID");

/**
 * Check if user email exists in Firestore whitelistedEmails collection
 * Used by both beforeUserCreated and Discord auth functions
 */
async function checkUserInWhitelistedEmailsCollection(
  email: string,
  logger: Logger
): Promise<boolean> {
  try {
    const docRef = collection<DbWhitelistedEmails>(
      DbCollections.whitelistedEmails
    ).doc(email.toLowerCase());
    const doc = await docRef.get();

    if (!doc.exists) {
      logger.info(
        `Email not found in ${DbCollections.whitelistedEmails} collection`,
        {
          email,
        }
      );
      return false;
    }

    const data = doc.data();
    const isWhitelisted = data?.allow === true;

    logger.info(
      `${DbCollections.whitelistedEmails} collection check completed`,
      {
        email,
        isWhitelisted,
        allowValue: data?.allow,
      }
    );

    return isWhitelisted;
  } catch (error) {
    logger.error(
      `Error checking ${DbCollections.whitelistedEmails} collection`,
      {
        email,
        error: error instanceof Error ? error.message : String(error),
      }
    );
    return false;
  }
}

/**
 * Check if user email exists in AirTable whitelist
 * Used by both beforeUserCreated and Discord auth functions
 */
async function checkUserInWhitelistedEmailsAirTable(
  email: string,
  url: string,
  apiKey: string,
  logger: Logger
): Promise<boolean> {
  try {
    if (!apiKey) {
      logger.error("AirTable API key is missing", {
        email,
      });
      return false;
    }

    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
    });

    if (!response.ok) {
      logger.error(
        `AirTable API request failed with status ${response.status}`,
        {
          email,
          status: response.status,
          statusText: response.statusText,
        }
      );
      return false;
    }

    const data = (await response.json()) as {
      records: { fields: unknown }[];
    };

    return data.records.length > 0;
  } catch (error) {
    logger.error(`Error checking AirTable API ${url}`, {
      email,
      error: error instanceof Error ? error.message : String(error),
    });
    return false;
  }
}

/**
 * Unified whitelist checking function that checks both Airtable and Firestore
 * Used by both beforeUserCreated and Discord auth functions
 * Returns true if user is whitelisted in either Airtable or Firestore
 */
export async function checkUserInWhitelisted(
  email: string,
  logger: Logger,
  context?: { userId?: string; discordId?: string }
): Promise<boolean> {
  let isWhitelisted = false;

  const airtableBaseId =
    AIRTABLE_BASE_ID.value() ?? process.env.AIRTABLE_BASE_ID;
  const airtableTableId =
    AIRTABLE_TABLE_ID.value() ?? process.env.AIRTABLE_TABLE_ID;
  const airtableApiKey =
    AIRTABLE_API_KEY.value() ?? process.env.AIRTABLE_API_KEY;

  const encodedEmail = encodeURIComponent(email.trim().toLowerCase());
  const cohort = encodeURIComponent("1 – September"); // TODO: Make this dynamic

  const url = `https://api.airtable.com/v0/${airtableBaseId}/${airtableTableId}?filterByFormula=AND({Email}="${encodedEmail}",{Cohort}="${cohort}")`;

  logger.info("Checking user in AirTable", {
    ...context,
    email,
    url,
  });

  isWhitelisted = await checkUserInWhitelistedEmailsAirTable(
    email,
    url,
    airtableApiKey,
    logger
  );

  if (isWhitelisted) {
    logger.info("User whitelist check passed via AirTable", {
      ...context,
      email,
      source: "airtable",
    });
    return true;
  }

  logger.info(
    `Checking user in ${DbCollections.whitelistedEmails} collection`,
    {
      ...context,
      email,
    }
  );

  isWhitelisted = await checkUserInWhitelistedEmailsCollection(email, logger);

  if (isWhitelisted) {
    logger.info("User whitelist check passed via Firestore", {
      ...context,
      email,
      source: "whitelistedEmails",
    });
    return true;
  }

  logger.warn(
    `User not whitelisted in AirTable or ${DbCollections.whitelistedEmails} collection`,
    {
      ...context,
      email,
    }
  );

  return false;
}
