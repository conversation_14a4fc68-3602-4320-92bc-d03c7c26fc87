import React, { useEffect } from "react";
import { ButtonVariantKey } from "../ui/button";
import { Separator } from "../ui/separator";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Typography } from "@/components/ui-nilo/Typography";
import { Client } from "@/core/client";

interface ConfirmationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void | Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  variant?: ButtonVariantKey;
}

export function ConfirmationModal({
  open,
  onOpenChange,
  title,
  description,
  confirmText = "Confirm",
  cancelText = "Cancel",
  onConfirm,
  onCancel,
  loading = false,
  variant = "destructive",
}: ConfirmationModalProps) {
  // Cleanup function to remove overlays and restore focus
  const cleanup = () => {
    // Force remove any lingering backdrop elements and portals
    const backdrops = document.querySelectorAll(
      '[data-slot="alert-dialog-overlay"]'
    );
    backdrops.forEach((backdrop) => {
      if (backdrop.parentNode) {
        backdrop.parentNode.removeChild(backdrop);
      }
    });

    // Also remove any lingering portal containers
    const portals = document.querySelectorAll("[data-radix-portal]");
    portals.forEach((portal) => {
      // Check if this portal is empty or only contains our overlay
      const hasOnlyOverlay =
        portal.children.length === 0 ||
        (portal.children.length === 1 &&
          portal.querySelector('[data-slot="alert-dialog-overlay"]'));
      if (hasOnlyOverlay && portal.parentNode) {
        portal.parentNode.removeChild(portal);
      }
    });

    // Restore body styles in case they weren't cleaned up
    document.body.style.pointerEvents = "";
    document.body.style.overflow = "";
    // Also check for any data attributes that Radix might have added
    document.body.removeAttribute("data-scroll-locked");

    // Restore focus to the Three.js canvas using the Client's method
    // This ensures proper focus restoration for camera controls
    if (Client.created && Client.renderer?.domElement) {
      // Trigger a pointerdown event to ensure the canvas gets focus
      // This mimics what happens when the user clicks on the canvas
      const event = new Event("pointerdown", { bubbles: true });
      Client.renderer.domElement.dispatchEvent(event);
      Client.renderer.domElement.focus();
    }
  };

  // Ensure cleanup when component unmounts or modal closes
  useEffect(() => {
    if (!open) {
      // Small delay to ensure animations complete before cleanup
      const timeoutId = setTimeout(cleanup, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [open]);

  const handleConfirm = async () => {
    try {
      await onConfirm();
      // Always close the modal after the action completes
      onOpenChange(false);
      // Run cleanup immediately for confirm action
      setTimeout(cleanup, 100);
    } catch (error) {
      console.error("Error in confirmation action:", error);
      // Also close on error to prevent the modal from getting stuck
      onOpenChange(false);
      // Run cleanup immediately for error case
      setTimeout(cleanup, 100);
    }
  };

  const handleCancel = () => {
    onCancel?.();
    onOpenChange(false);
    // Run cleanup immediately for cancel action
    setTimeout(cleanup, 100);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent
        className="w-sm"
        onOpenAutoFocus={(e) => {
          // Prevent Radix from focusing the first focusable element
          e.preventDefault();
        }}
        onCloseAutoFocus={(e) => {
          // Prevent Radix from restoring focus to the trigger
          // We'll handle focus restoration ourselves
          e.preventDefault();

          // Immediately restore focus to the Three.js canvas
          if (Client.created && Client.renderer?.domElement) {
            Client.renderer.domElement.focus();
          }
        }}
      >
        <AlertDialogHeader>
          <AlertDialogTitle className="text-nilo-text-primary">
            <Typography.Heading level={3} color="light" weight="medium">
              {title}
            </Typography.Heading>
          </AlertDialogTitle>
          <Separator className="my-4" />
          <AlertDialogDescription className="text-nilo-text-secondary">
            <Typography.Body level={2} color="light">
              {description}
            </Typography.Body>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel} disabled={loading}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={loading}
            variant={variant as ButtonVariantKey}
          >
            {loading ? "Processing..." : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
