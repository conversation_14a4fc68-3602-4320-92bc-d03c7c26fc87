import { Vercel } from "@vercel/sdk";

interface SetEnvAndRedeployOptions {
  tag: string;
  projectId: string;
  token: string;
  repoId: string;
}

export async function setEnvAndRedeploy(
  options: SetEnvAndRedeployOptions
): Promise<void> {
  const { tag, projectId, token, repoId } = options;

  const vercel = new Vercel({
    bearerToken: token,
  });

  console.info(`🔧 Setting environment variable NILO_GIT_TAG=${tag}`);

  try {
    await vercel.projects.createProjectEnv({
      idOrName: projectId,
      upsert: "true",
      requestBody: {
        key: "NILO_GIT_TAG",
        value: tag,
        type: "encrypted",
        target: ["production"],
      },
    });
  } catch (error) {
    if (error instanceof Error && error.message.includes("Project not found")) {
      throw new Error(
        `Project not found: "${projectId}". Make sure you're using the project ID (format: prj_xxxx), not the project name. Find it in Vercel Dashboard → Settings → General.`
      );
    }
    throw error;
  }

  console.info("✅ Environment variable set successfully");

  console.info("🚀 Triggering production redeploy...");

  await vercel.deployments.createDeployment({
    requestBody: {
      name: projectId,
      gitSource: {
        type: "github",
        ref: "prod",
        repoId: parseInt(repoId, 10),
      },
      target: "production",
    },
  });

  console.info(`✅ Redeploy triggered with tag: ${tag}`);
}
