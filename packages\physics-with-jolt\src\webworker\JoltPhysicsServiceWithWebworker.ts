import * as Comlink from "comlink";
import { createDelegateFunction } from "@nilo/utilities";
import { PhysicsStepInfo } from "../types/PhysicsStep";
import { RagdollSettings } from "../types/Ragdoll";
import { BodyGeometry, PhysicsBody } from "../types/Body";
import { PhysicsShapeType } from "../types/PhysicsOptions";
import { CharacterPhysicsSettings } from "../types/character";
import { JoltBody } from "../types/debug";
import {
  BodyControlApi,
  CharacterControlApi,
  JoltPhysicsServiceApi,
  RagdollControlApi,
} from "../types/webworker";
import { validatePhysicsOptions } from "../util/validatePhysicsOptions";
import { BodyControlWrapper } from "./wrappers/BodyControlWrapper";
import { CharacterControlWrapper } from "./wrappers/CharacterControlWrapper";
import { RagdollControlWrapper } from "./wrappers/RagdollControlWrapper";

export class JoltPhysicsServiceWithWebworker {
  private readonly bodyControlMap = new Map<string, BodyControlWrapper>();

  public readonly onContactsAdded =
    createDelegateFunction<[body1Id: number, body2Id: number]>();
  public readonly onContactsRemoved =
    createDelegateFunction<[body1Id: number, body2Id: number]>();

  public readonly onPhysicsLoopStarted = createDelegateFunction<[]>();
  public readonly onPhysicsLoopStopped = createDelegateFunction<[]>();
  public readonly onPhysicsStateRead = createDelegateFunction<[]>();

  private _dynamicBodiesCount = 0;
  private _staticBodiesCount = 0;

  private _isPhysicsLoopRunning = false;

  constructor(private readonly api: Comlink.Remote<JoltPhysicsServiceApi>) {}

  // Physics loop control
  public startPhysicsLoop(): void {
    this.api
      .startPhysicsLoop()
      .then(() => {
        this._isPhysicsLoopRunning = true;
        this.onPhysicsLoopStarted.invoke();
      })
      .catch((error) => {
        console.error("Error starting physics loop", error);
      });
  }

  public stopPhysicsLoop(): void {
    this.api
      .stopPhysicsLoop()
      .then(() => {
        this._isPhysicsLoopRunning = false;
        this.onPhysicsLoopStopped.invoke();
      })
      .catch((error) => {
        console.error("Error stopping physics loop", error);
      });
  }

  public isPhysicsLoopRunning(): boolean {
    return this._isPhysicsLoopRunning;
  }

  public async syncPhysicsState(): Promise<PhysicsStepInfo> {
    const physicsStepInfo = await this.getLastStepInfo();
    this._dynamicBodiesCount = physicsStepInfo.dynamicBodiesCount;
    this._staticBodiesCount = physicsStepInfo.staticBodiesCount;
    return physicsStepInfo;
  }

  public async getLastStepInfo(): Promise<PhysicsStepInfo> {
    const physicsStepInfo = await this.api.getLastStepInfo();
    this.onPhysicsStateRead.invoke();

    //Get contacts
    physicsStepInfo.contactsAdded.forEach((contact) => {
      this.onContactsAdded.invoke(contact.body1Id, contact.body2Id);
    });
    physicsStepInfo.contactsRemoved.forEach((contact) => {
      this.onContactsRemoved.invoke(contact.body1Id, contact.body2Id);
    });
    return physicsStepInfo;
  }

  public async getPhysicsFPS(): Promise<number> {
    return this.api.getPhysicsFPS();
  }

  // Body management
  public async getObjectBodyControlPairs(): Promise<
    [string, BodyControlWrapper][]
  > {
    return Array.from(this.bodyControlMap.entries());
  }

  public async registerRagdollForPhysics(
    ragdollSettings: RagdollSettings
  ): Promise<RagdollControlWrapper | undefined> {
    const ragdollControl =
      await this.api.registerRagdollForPhysics(ragdollSettings);
    if (!ragdollControl) {
      return undefined;
    }
    const wrapper = new RagdollControlWrapper(
      ragdollControl as Comlink.Remote<RagdollControlApi>,
      ragdollSettings
    );
    return wrapper;
  }

  public async registerObjectForPhysics(
    physicsBody: PhysicsBody,
    geometry: BodyGeometry
  ): Promise<BodyControlWrapper | undefined> {
    const validationResult = validatePhysicsOptions(
      physicsBody.physicsOptions,
      true
    );
    if (!validationResult.isValid) {
      physicsBody.physicsOptions = validationResult.options;
    }
    const bodyControl = await this.api.registerObjectForPhysics(
      physicsBody,
      geometry
    );
    if (!bodyControl) return undefined;
    const wrapper = new BodyControlWrapper(
      bodyControl as Comlink.Remote<BodyControlApi>,
      physicsBody
    );
    this.bodyControlMap.set(physicsBody.id, wrapper);
    return wrapper;
  }

  public async createCharacterPhysics(
    characterSettings: Partial<CharacterPhysicsSettings>
  ): Promise<CharacterControlWrapper> {
    const characterControl =
      await this.api.createCharacterPhysics(characterSettings);

    const json = await characterControl.toJSON();
    if (!json) {
      throw new Error("Character control is invalid");
    }
    const wrapper = new CharacterControlWrapper(
      characterControl as Comlink.Remote<CharacterControlApi>,
      characterSettings,
      json
    );
    return wrapper;
  }

  public unregisterObjectFromPhysics(id: string): void {
    this.bodyControlMap.delete(id);
    this.api.unregisterObjectFromPhysics(id).catch((error) => {
      console.error("Error unregistering object from physics", error);
      this.api.getBodyControlById(id).then((bodyControl) => {
        if (bodyControl) {
          //Couldn't remove objects from physics, add again to bodyControlMap
          const wrapper = new BodyControlWrapper(
            bodyControl as Comlink.Remote<BodyControlApi>,
            bodyControl.physicsBody as PhysicsBody
          );
          this.bodyControlMap.set(id, wrapper);
        }
      });
    });
  }

  public unregisterRagdollFromPhysics(
    ragdollControl: RagdollControlWrapper
  ): void {
    this.api.unregisterRagdollFromPhysics(ragdollControl.ragdollGroupID);
  }

  // Utility methods
  public destroy(): void {
    this.api.destroy();
  }

  public async findBestShape(
    geometry: BodyGeometry
  ): Promise<PhysicsShapeType> {
    return this.api.findBestShape(geometry);
  }

  public setGravityEnabled(enabled: boolean): void {
    this.api.setGravityEnabled(enabled);
  }

  public getStaticBodiesCount(): number {
    return this._staticBodiesCount;
  }

  public getDynamicBodiesCount(): number {
    return this._dynamicBodiesCount;
  }

  public getBodyControlById(id: string): BodyControlWrapper | null {
    return this.bodyControlMap.get(id) ?? null;
  }

  public async getAllBodies(getAllShapes: boolean): Promise<JoltBody[]> {
    return this.api.getAllBodies(getAllShapes);
  }

  public unregisterCharacterFromPhysics(
    characterControl: CharacterControlWrapper
  ): void {
    const id = characterControl.id;
    if (!id) {
      return;
    }
    this.api.unregisterCharacterFromPhysics(id);
  }

  public getGravity(): number {
    //TODO:: Get gravity from the physics service, and store it here.
    return -9.81;
  }
}

export async function createJoltPhysicsServiceWithWebworker(): Promise<JoltPhysicsServiceWithWebworker> {
  const worker = new Worker(new URL("./JoltWebworker.ts", import.meta.url), {
    type: "module",
  });
  const joltWebworker = Comlink.wrap<JoltPhysicsServiceApi>(worker);
  await joltWebworker.getInitPromise();

  return new JoltPhysicsServiceWithWebworker(joltWebworker);
}
