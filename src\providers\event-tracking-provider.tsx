import React, {
  ReactNode,
  createContext,
  useContext,
  useCallback,
  useRef,
  useEffect,
  useMemo,
} from "react";
import { usePostHog } from "posthog-js/react";
import type { PostHog } from "posthog-js";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import { getBrowserName, getDeviceType } from "@/core/util/UserAgent";
import { isLocalNetwork } from "@/debug/isLocalNetwork";

// Global reference to event tracking context for non-React usage
let globalEventTrackingContext: EventTrackingContextValue | null = null;

/**
 * Get the global event tracking context
 * Used by non-React functions to access event tracking
 */
export function getGlobalEventTrackingContext(): EventTrackingContextValue | null {
  return globalEventTrackingContext;
}

// Event tracking context interface
interface EventTrackingContextValue {
  isInitialized: boolean;
  trackEvent: (eventName: string, properties?: Record<string, unknown>) => void;
  identify: (userId: string, properties?: Record<string, unknown>) => void;
  setUserProperties: (properties: Record<string, unknown>) => void;
  alias: (alias: string) => void;
  reset: () => void;
  posthogClient: PostHog | null;
}

const EventTrackingContext = createContext<EventTrackingContextValue | null>(
  null
);

export function useEventTracking(): EventTrackingContextValue {
  const context = useContext(EventTrackingContext);
  if (!context) {
    throw new Error(
      "useEventTracking must be used within EventTrackingProvider"
    );
  }
  return context;
}

interface EventTrackingProviderProps {
  children: ReactNode;
}

/**
 * Event Tracking Provider Component using PostHog
 * Provides centralized event tracking functionality across the app
 */
export function EventTrackingProvider({
  children,
}: EventTrackingProviderProps) {
  const { userId, loading, user } = useCurrentUser();
  const posthog = usePostHog();
  const isIdentifiedRef = useRef<boolean>(false);

  // Auto-identify user when available
  useEffect(() => {
    if (posthog && userId && user && !isIdentifiedRef.current) {
      posthog.identify(userId, {
        name: user.displayName || "",
        email: user.email || "",
        device_type: getDeviceType(),
        browser: getBrowserName(),
      });
      isIdentifiedRef.current = true;
    }
  }, [posthog, userId, user]);

  // Reset identification flag when user changes
  useEffect(() => {
    if (!userId) {
      isIdentifiedRef.current = false;
    }
  }, [userId]);

  // Track event function with automatic context enrichment
  const trackEvent = useCallback(
    (eventName: string, properties: Record<string, unknown> = {}) => {
      if (!posthog || isLocalNetwork()) {
        return;
      }

      // Enrich properties with common context
      const enrichedProperties = {
        ...properties,
        timestamp: new Date().toISOString(),
        user_id: userId || undefined,
        device_type: getDeviceType(),
        browser: getBrowserName(),
        referrer_url: document.referrer || undefined,
      };

      posthog.capture(eventName, enrichedProperties, {
        transport: "sendBeacon",
      });

      console.debug("📊 Event tracked:", eventName, enrichedProperties);
    },
    [posthog, userId]
  );

  // Identify user function
  const identify = useCallback(
    (userId: string, properties: Record<string, unknown> = {}) => {
      if (!posthog || isLocalNetwork()) {
        return;
      }

      posthog.identify(userId, properties);
      isIdentifiedRef.current = true;

      console.debug("📊 User identified:", userId, properties);
    },
    [posthog]
  );

  // Set user properties function
  const setUserProperties = useCallback(
    (properties: Record<string, unknown>) => {
      if (!posthog || isLocalNetwork()) {
        return;
      }

      posthog.setPersonProperties(properties);

      console.debug("📊 User properties set:", properties);
    },
    [posthog]
  );

  // Alias function
  const alias = useCallback(
    (alias: string) => {
      if (!posthog || isLocalNetwork()) {
        return;
      }

      posthog.alias(alias);

      console.debug("📊 User aliased:", alias);
    },
    [posthog]
  );

  // Reset function
  const reset = useCallback(() => {
    if (!posthog || isLocalNetwork()) {
      return;
    }

    posthog.reset();
    isIdentifiedRef.current = false;

    console.debug("📊 Event tracking reset");
  }, [posthog]);

  const contextValue: EventTrackingContextValue = useMemo(
    () => ({
      isInitialized: !!posthog && !loading && !isLocalNetwork(),
      trackEvent,
      identify,
      setUserProperties,
      alias,
      reset,
      posthogClient: posthog,
    }),
    [posthog, loading, trackEvent, identify, setUserProperties, alias, reset]
  );

  // Update global reference for non-React usage
  useEffect(() => {
    globalEventTrackingContext = contextValue;
  }, [contextValue]);

  return (
    <EventTrackingContext.Provider value={contextValue}>
      {children}
    </EventTrackingContext.Provider>
  );
}
