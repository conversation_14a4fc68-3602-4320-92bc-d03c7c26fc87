interface OpenInNewTabOptions {
  urlParams?: Record<string, string>;
  preserveUrlParams?: boolean;
}

export const navigateInNewTab = (
  path: string,
  options: OpenInNewTabOptions = {}
): void => {
  const { urlParams = {}, preserveUrlParams = false } = options;

  let finalUrl = path;

  if (preserveUrlParams || Object.keys(urlParams).length > 0) {
    const currentParams = new URLSearchParams(window.location.search);
    const newParams = new URLSearchParams();

    // If preserving URL params, copy current params first
    if (preserveUrlParams) {
      currentParams.forEach((value, key) => {
        newParams.set(key, value);
      });
    }

    // Override with new params
    Object.entries(urlParams).forEach(([key, value]) => {
      newParams.set(key, value);
    });

    // Add params to URL
    const paramString = newParams.toString();
    if (paramString) {
      finalUrl += `?${paramString}`;
    }
  }

  window.open(finalUrl, "_blank");
};
