import { Component } from "./component";
import { EntityId } from "./entity";
import { EntityData } from "./entityData";
import { ComponentTuple, World } from "./world";

/**
 * Allows carrying an entity's component outside of the world.
 *
 * Useful for offline construction of entities, to subsequent spawning into the world.
 * */

export class EntityBuilder {
  private components: Map<Component<unknown>, unknown> = new Map();

  public addComponent<T>(component: Component<T>, value: T): EntityBuilder {
    this.components.set(component, value);
    return this;
  }

  public addComponents(
    componentValues: ComponentTuple<unknown>[]
  ): EntityBuilder {
    for (const [component, value] of componentValues) {
      this.components.set(component, value);
    }
    return this;
  }

  public getComponent<T>(component: Component<T>): T | null {
    return this.components.get(component) as T | null;
  }

  public getOrAddComponent<T>(
    component: Component<T>,
    defaultValue: () => T
  ): T {
    if (this.components.has(component)) {
      return this.components.get(component) as T;
    }

    const value = defaultValue();
    this.components.set(component, value);
    return value;
  }

  public spawn(world: World): EntityData {
    const values: ComponentTuple<unknown>[] = [];
    for (const [component, value] of this.components) {
      values.push([component, value]);
    }

    return world.spawnEntity(values);
  }

  public addToEntity(entity: EntityId, world: World) {
    const values: ComponentTuple<unknown>[] = [];
    for (const [component, value] of this.components) {
      values.push([component, value]);
    }
    world.addComponents(entity, values);
  }

  public spawnWithId(world: World, id: EntityId): EntityData {
    const values: ComponentTuple<unknown>[] = [];
    for (const [component, value] of this.components) {
      values.push([component, value]);
    }

    return world.spawnEntityWithId(id, values);
  }
}
