import { <PERSON>uatern<PERSON>, Quaternion<PERSON>ike, Vector3, Vector3<PERSON><PERSON> } from "three";
import * as Comlink from "comlink";
import {
  Matrix4Array,
  PhysicsOptions,
  PhysicsShapeType,
} from "../../types/PhysicsOptions";
import { PhysicsBody } from "../../types/Body";
import { BodyControl<PERSON>pi, DynamicProperties } from "../../types/webworker";
import { validatePhysicsOptions } from "../../util/validatePhysicsOptions";
import { GenericWrapper } from "./GenericWrapper";

/*
  This wrapper is used to wrap the BodyControl class and make it compatible with the Comlink library.
  It is also used to call the BodyControl class methods from the webworker thread.
  It stores the most common properties of the bodyControl after each physics step,
  to avoid communicating with the webworker many times
*/
export class BodyControlWrapper extends GenericWrapper {
  public readonly physicsBody: PhysicsBody;
  private _bodyId: number = -1;
  private _contactCount: number = 0;

  private _mass: number = 1;
  private physicsOptions: PhysicsOptions;

  private _position: Vector3 = new Vector3();
  private _rotation: Quaternion = new Quaternion();

  private updatesInProgress: number = 0; //Used to block updates to the position and rotation when the body is being updated from the webworker

  public hasUpdatesInProgress(): boolean {
    return this.updatesInProgress > 0;
  }

  constructor(
    private readonly bodyControl: Comlink.Remote<BodyControlApi>,
    physicsBody: PhysicsBody
  ) {
    super(bodyControl);
    this.physicsBody = physicsBody;
    this.physicsOptions = physicsBody.physicsOptions;
  }

  public async initialize(): Promise<void> {
    this._bodyId = await this.bodyControl.getBodyIDValue();
    this._mass = await this.bodyControl.getBodyMass();
  }

  public updateState(
    position: Vector3Like,
    rotation: QuaternionLike,
    dynamicProperties: DynamicProperties
  ) {
    this.updateDynamicProperties(dynamicProperties);
    this._position.copy(position);
    this._rotation.copy(rotation);
  }

  public get bodyId(): number {
    return this._bodyId;
  }

  public get mass(): number {
    return this._mass;
  }

  public updatePhysicsBodyOffsetMatrix(
    offsetMatrix: Matrix4Array
  ): Promise<void> {
    this.updatesInProgress++;
    return this.bodyControl
      .updatePhysicsBodyOffsetMatrix(offsetMatrix)
      .then(() => {
        this.updatesInProgress--;
      });
  }

  // Physics options
  public async updatePhysicsOptions(options: Partial<PhysicsOptions>) {
    // Update local copy to keep it in sync
    this.physicsOptions = { ...this.physicsOptions, ...options };
    const validationResult = validatePhysicsOptions(this.physicsOptions, true);
    if (!validationResult.isValid) {
      this.physicsOptions = validationResult.options;
    }
    return this.bodyControl.updatePhysicsOptions(this.physicsOptions);
  }

  public getPhysicsOptions(): PhysicsOptions {
    return this.physicsOptions;
  }

  public async getBodyMass(): Promise<number> {
    return this.bodyControl.getBodyMass();
  }

  // Position and rotation
  public getPosition(): Vector3Like {
    return this._position;
  }

  public setPosition(newPosition: Vector3Like): Promise<void> {
    if (
      this._position.x === newPosition.x &&
      this._position.y === newPosition.y &&
      this._position.z === newPosition.z
    ) {
      return Promise.resolve();
    }
    this._position.copy(newPosition);
    this.updatesInProgress++;
    return this.bodyControl.setPhysicsBodyPosition(newPosition).then(() => {
      this.updatesInProgress--;
    });
  }

  public getRotation(): QuaternionLike {
    return this._rotation;
  }

  public setRotation(newRotation: QuaternionLike): Promise<void> {
    if (newRotation instanceof Quaternion) {
      //This is weird, if newRotation is a Quaternion, it will serialize as {_x:, _y:, _z:, _w:}
      newRotation = {
        x: newRotation.x,
        y: newRotation.y,
        z: newRotation.z,
        w: newRotation.w,
      };
    }
    this.updatesInProgress++;
    return this.bodyControl.setPhysicsBodyRotation(newRotation).then(() => {
      this.updatesInProgress--;
    });
  }

  public setScale(newScale: Vector3Like): Promise<void> {
    return this.bodyControl.setPhysicsBodyScale(newScale);
  }

  // Body state
  public isActive(): Promise<boolean> {
    return this.bodyControl.isActive();
  }

  public activate(): Promise<void> {
    return this.bodyControl.activate();
  }

  public getStatic(): boolean {
    return this.physicsOptions.isStatic;
  }

  public setStatic(isStatic: boolean): Promise<void> {
    return this.updatePhysicsOptions({ isStatic });
  }

  // Material properties
  public setRestitution(restitution: number): Promise<void> {
    return this.bodyControl.setRestitution(restitution);
  }

  public setFriction(friction: number): Promise<void> {
    return this.bodyControl.setFriction(friction);
  }

  // Linear velocity
  public getLinearVelocity(): Promise<Vector3Like> {
    return this.bodyControl.getLinearVelocity();
  }

  public setLinearVelocity(velocity: Vector3Like): Promise<void> {
    return this.bodyControl.setLinearVelocity(velocity);
  }

  //Remove or change this later, we don't want to continuously add velocities here.
  //It's better to set the velocity directly only once, to avoid multiple messages being sent to the webworker.
  public addLinearVelocity(velocity: Vector3Like): Promise<void> {
    return this.bodyControl.addLinearVelocity(velocity);
  }

  // Angular velocity
  public getAngularVelocity(): Promise<{ x: number; y: number; z: number }> {
    return this.bodyControl.getAngularVelocity();
  }

  public setAngularVelocity(angularVelocity: Vector3Like): Promise<void> {
    return this.bodyControl.setAngularVelocity(angularVelocity);
  }

  public addAngularVelocity(angularVelocity: Vector3Like): Promise<void> {
    return this.bodyControl.addAngularVelocity(angularVelocity);
  }

  // Forces and impulses
  public applyImpulse(impulse: Vector3Like): Promise<void> {
    return this.bodyControl.applyImpulse(impulse);
  }

  public applyImpulseAtPoint(
    impulse: Vector3Like,
    point: Vector3Like
  ): Promise<void> {
    return this.bodyControl.applyImpulseAtPoint(impulse, point);
  }

  public applyForce(force: Vector3Like): Promise<void> {
    return this.bodyControl.applyForce(force);
  }

  public applyTorque(torque: Vector3Like): Promise<void> {
    return this.bodyControl.applyTorque(torque);
  }

  // Shape management
  public async swapShape(shapeType: PhysicsShapeType): Promise<void> {
    return this.bodyControl.swapShape(shapeType);
  }

  // Body destruction
  public destroyBody(): Promise<void> {
    return this.bodyControl.destroyBody();
  }

  // Contact information
  public getContactCount(): Promise<number> {
    return this.bodyControl.getContactCount();
  }

  public addToWorld(): Promise<void> {
    return this.bodyControl.addToWorld();
  }

  public removeFromWorld(): Promise<void> {
    return this.bodyControl.removeFromWorld();
  }

  public setAllowSleeping(allowSleeping: boolean): Promise<void> {
    return this.bodyControl.setAllowSleeping(allowSleeping);
  }

  public get contactCount(): number {
    return this._contactCount;
  }

  public get position(): Vector3 {
    return this._position;
  }
  public get rotation(): Quaternion {
    return this._rotation;
  }
}
