import path from "path";
import { defineConfig, loadEnv } from "vite";

import ViteYaml from "@modyfi/vite-plugin-yaml";
import { sentryVitePlugin } from "@sentry/vite-plugin";
import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react-swc";
import checker from "vite-plugin-checker";
import glsl from "vite-plugin-glsl";
import liveReload from "vite-plugin-live-reload";
import mkcert from "vite-plugin-mkcert";
import svgr from "vite-plugin-svgr";

const EXPOSED_ENV_VARS = [
  "NODE_ENV",
  "USE_FIREBASE_EMULATOR",
  "BYPASS_AUTHENTICATION",
  "BYPASS_AUTHENTICATION_ON_LOCAL_NETWORK",
  "POSTHOG_API_KEY",
  "USE_SERVER_URL",
  "VERCEL_ENV",
  "VERCEL_GIT_COMMIT_REF",
  "VERCEL_GIT_PULL_REQUEST_ID",
  "VERCEL_GIT_COMMIT_SHA",
  "NILO_GIT_TAG",
  "SENTRY_DSN",
  "DISCORD_CLIENT_ID",
  "PLAY_SESSION_TIMEOUT_SECONDS",
];

export default defineConfig(async ({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  const isDev = mode === "development";

  // const isDev = mode === "development"; // Might be useful
  const isHTTPS = env.https === "true";
  const withCrossOriginIsolation = env.enableCrossOriginIsolation === "true";

  const shouldAllowConsoleLogs = isDev || env.ALLOW_CONSOLE_LOGS === "true";

  const plugins = [
    isHTTPS && mkcert(),
    checker({
      typescript: {
        tsconfigPath: "./tsconfig.json",
      },
    }),
    svgr({
      svgrOptions: {
        icon: true, // Makes each icon size = 1em, so it can be easily resized by setting the font-size on the parent element
      },
    }), // Before react
    react(),
    tailwindcss(),
    ViteYaml(),
    liveReload(["src/**/*.ts", "src/**/*.glsl", "packages/**/*.ts"]),
    sentryVitePlugin({
      authToken: process.env.SENTRY_AUTH_TOKEN,
      org: "nilo-technologies",
      project: "nilo-client",
    }),
    glsl(),
  ].filter(Boolean);

  const define = Object.fromEntries(
    EXPOSED_ENV_VARS.map((key) => [
      `process.env.${key}`,
      JSON.stringify(env[key]),
    ])
  );
  console.debug("🔌 exposed env vars", define);

  return {
    plugins,

    esbuild: {
      drop: shouldAllowConsoleLogs ? [] : ["console", "debugger"],
    },

    build: {
      sourcemap:
        define["process.env.VERCEL_ENV"] === '"production"' &&
        define["process.env.NODE_ENV"] === '"production"'
          ? "hidden"
          : true, // Disable source maps in production to save memory
      rollupOptions: {
        output: {
          manualChunks: {
            // Split large dependencies into separate chunks
            three: ["three"],
            editor: ["@monaco-editor/react", "monaco-editor"],
            physics: ["jolt-physics"],
          },
        },
      },
    },
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        "@nilo/experiment-behaviors": path.resolve(
          __dirname,
          "./packages/experiment-behaviors/src"
        ),
        "@nilo/ecs": path.resolve(__dirname, "./packages/ecs/src"),
        "@nilo/ecs-networking": path.resolve(
          __dirname,
          "./packages/ecs-networking/src"
        ),
      },
    },

    define,

    // Dev server options
    server: {
      port: env.PORT || 3173,
      host: true,
      hmr: true,
      https: isHTTPS,
      headers: {
        ...(withCrossOriginIsolation ? crossOriginIsolationHeaders : {}),
      },
      proxy: {
        "/wieprz/static": {
          target: "https://us-assets.i.posthog.com",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/wieprz\/static/, "/static"),
        },
        "/wieprz": {
          target: "https://us.i.posthog.com",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/wieprz/, ""),
        },
      },
    },

    worker: {
      format: "es",
    },
  };
});

const crossOriginIsolationHeaders = {
  "Cross-Origin-Embedder-Policy": "require-corp",
  "Cross-Origin-Opener-Policy": "same-origin",
};
