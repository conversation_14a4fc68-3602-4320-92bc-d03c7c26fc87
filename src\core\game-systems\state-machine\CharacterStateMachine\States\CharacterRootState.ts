import { CharacterState } from "../CharacterState";
import { CharacterContext } from "../CharacterContext";
import { CharacterStates } from "../CharacterStateMachine";
import { EntityData } from "@nilo/ecs";
import { Client } from "@/core/client";
import {
  DeathEvent,
  OnDeath,
  OnRespawn,
  RespawnEvent,
} from "@/core/ecs/events";
import { MeshEntity } from "@/core/entity";

/**
 * CharacterRootState - Parent state for all character states (PlayableState, DeathState)
 * This is the root state of the character state machine
 */
export class CharacterRootState extends CharacterState {
  private _onPointerLockChangeDispose: undefined | (() => void);
  private _onDeathHandler?: (event: DeathEvent) => void;
  private _onRespawnHandler?: (event: RespawnEvent) => void;

  constructor(context: CharacterContext, states: CharacterStates, id: number) {
    super(context, "CharacterRootState", id, states);
  }

  override onEnter(entityData: EntityData) {
    super.onEnter(entityData);

    // Subscribe to death events
    this._onDeathHandler = (event: DeathEvent) => {
      if (event.entity.id === entityData.id()) {
        this.setSubState(entityData, this._states.DeathState);
      }
    };

    const onDeathComponent = entityData.getComponent(OnDeath)!;
    onDeathComponent.on(this._onDeathHandler);

    // Subscribe to respawn events
    this._onRespawnHandler = (event: RespawnEvent) => {
      if (event.entity.id === entityData.id()) {
        this.setSubState(entityData, this._states.PlayableState);
      }

      // TODO: should GameEntity.setPosition check for CharacterController and update character position instead?
      this.context.character?.teleport(
        event.entity as MeshEntity,
        event.position,
        event.orientation
      );
    };

    const onRespawnComponent = entityData.getComponent(OnRespawn)!;
    onRespawnComponent.on(this._onRespawnHandler);

    // Default to PlayableState when entering the root state
    this.setSubState(entityData, this._states.PlayableState);

    if (this.context.isLocal) {
      const shouldExitControlsOnPointerLockLost = true;
      const onPointerLockChange = () => {
        if (document.pointerLockElement === null) {
          if (shouldExitControlsOnPointerLockLost) {
            Client.playerControls.destroyActivePlayerControls();
          }
        }
      };
      document.body.requestPointerLock();
      document.addEventListener("pointerlockchange", onPointerLockChange);

      this._onPointerLockChangeDispose = () => {
        document.removeEventListener("pointerlockchange", onPointerLockChange);
      };
    }
  }

  override onExit(entityData: EntityData) {
    super.onExit(entityData);

    // Remove event subscriptions
    if (this._onDeathHandler) {
      const onDeathComponent = entityData.getComponent(OnDeath)!;
      onDeathComponent.off(this._onDeathHandler);
      this._onDeathHandler = undefined;
    }

    if (this._onRespawnHandler) {
      const onRespawnComponent = entityData.getComponent(OnRespawn)!;
      onRespawnComponent.off(this._onRespawnHandler);
      this._onRespawnHandler = undefined;
    }

    if (this.context.isLocal) {
      this.context.cameraControls?.dispose();
      this.context.cameraControls = undefined;

      this._onPointerLockChangeDispose?.();
      if (document.pointerLockElement) {
        document.exitPointerLock();
      }
    }
  }
}
