/* eslint-disable no-redeclare */
import { z } from "zod";

const Range = z.object({
  min: z.number(),
  max: z.number(),
});
export type Range = z.infer<typeof Range>;

export const RunFixtureRequest = z.discriminatedUnion("fixture", [
  z.object({
    fixture: z.literal("noop"),
  }),
  z.object({
    fixture: z.literal("createRandomInternalDataJobs"),
    count: Range.optional(),
    logCount: Range.optional(),
    subJobCount: Range.optional(),
    subJobLogCount: Range.optional(),
  }),
]);
export type RunFixtureRequest = z.infer<typeof RunFixtureRequest>;

export const RunFixtureResponse = z.object({
  success: z.boolean(),
  message: z.string().optional(),
});
export type RunFixtureResponse = z.infer<typeof RunFixtureResponse>;
