name: Prune unused ISE resources (currently only rules)

on:
  workflow_dispatch:
    inputs:
      dry-run:
        description: "Dry run"
        required: false
        default: "true"
  schedule:
    - cron: "0 4 * * *" # every day at 4:00 AM UTC
  push:
    paths:
      - .github/workflows/ise-prune.yml
      - tools/deployment-tools/src/**

jobs:
  prune:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
      pull-requests: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-gcp-auth
      - uses: ./.github/actions/setup-node
      - run: pnpm --filter deployment-tools build
      - run: pnpm install
      - if: github.event_name == 'schedule' || (github.event_name == 'workflow_dispatch' && inputs.dry-run == 'false')
        name: Prune rules (force)
        run: pnpm exec deployment-tools prune --force --rules
      - if: github.event_name == 'workflow_dispatch' && inputs.dry-run == 'true'
        name: Prune rules (dry run)
        run: pnpm exec deployment-tools prune --dry-run --rules
      - if: github.event_name == 'schedule' || github.event_name == 'push'
        name: Prune everything (dry run)
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: pnpm exec deployment-tools prune --dry-run --databases-and-storage-buckets --functions --rules
