import { Vector3, Quaternion } from "three";
import { PhysicsDataBehavior } from "../../ecs/behaviors/physics";
import { EntityOriginTypes } from "../../entity/Entity";
import { PrimitiveBehavior } from "../../ecs/behaviors/primitive";
import { TransformBehavior } from "../behaviors";
import {
  PhysicsUserData,
  PrimitiveType,
  TransformationData,
} from "@/liveblocks.config";
import { Behavior, EntityId, Prefab } from "@nilo/ecs";

/**
 * Helper function to create a primitive entity prefab with common behaviors
 */
export function createPrimitiveEntityPrefab(options: {
  position?: Vector3;
  rotation?: Quaternion;
  scale?: Vector3;
  primitiveType?: PrimitiveType;
  color?: number;
  userId?: EntityId | null;
  originType?: EntityOriginTypes;
  transformations?: TransformationData[];
  physicsUserData?: PhysicsUserData;
  tags?: string[];
}): Prefab {
  const {
    position,
    rotation,
    scale,
    primitiveType = "Cube",
    color = 0xffffff,
    userId = null,
    originType,
    transformations = [],
    physicsUserData = {},
    tags = [],
  } = options;

  const behaviors: Behavior[] = [
    // Prevent accidental reference sharing across frames
    new TransformBehavior(position?.clone(), rotation?.clone(), scale?.clone()),
    new PhysicsDataBehavior(physicsUserData),
    new PrimitiveBehavior(
      primitiveType,
      color,
      userId,
      originType,
      transformations,
      tags
    ),
  ];

  return new Prefab("primitiveEntity", behaviors);
}
