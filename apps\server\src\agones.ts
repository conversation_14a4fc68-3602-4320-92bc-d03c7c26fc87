import AgonesSDK from "@google-cloud/agones-sdk";
import { getLogger } from "@nilo/logger";

const logger = getLogger({ module: "agones" });

const populateDnsUrl = process.env.POPULATE_DNS_URL;

const populatedIps = new Set<string>();
async function populateDnsIfNeeded(ip: string) {
  if (!populateDnsUrl) {
    return;
  }

  if (populatedIps.has(ip)) {
    return;
  }

  logger.info(`Populating DNS for ${ip}`);
  try {
    const response = await fetch(populateDnsUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ ip }),
    });

    if (!response.ok) {
      logger.error("Failed to populate DNS", { response });
    }
  } catch (error) {
    logger.error("Failed to populate DNS", { error });
  }

  // adding to the set anyway to avoid spamming the server
  populatedIps.add(ip);
}

export async function initAgones() {
  logger.debug("Initializing Agones");
  const agones = new AgonesSDK();

  logger.debug("Connecting to Agones");
  await agones.connect();

  agones.watchGameServer(
    (gameServer) => {
      logger.info("Game server updated", { gameServer });
      populateDnsIfNeeded(gameServer?.status?.address);
      // TODO: extract and use the roomId from gameServer.objectMeta.labelsMap
    },
    (error) => {
      logger.error("Error watching game server", { error });
    }
  );

  // marking the game server as ready
  await agones.ready();

  logger.debug("Agones initialized");
  return agones;
}
