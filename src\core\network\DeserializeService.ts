import { ByteStreamReader } from "@evenstar/byteform";
import { logger } from "@sentry/react";
import { GameEntityComponent } from "../components";
import {
  EnvironmentEntity,
  MeshEntity,
  NetworkEntity,
  ParticleEntity,
  PrimitiveEntity,
  PromptEntity,
  UserEntity,
  BrushStrokeEntity,
  SharedEntity,
} from "../entity";
import { Client } from "../client";
import { GameEntityBehavior, TransformBehavior } from "../client/behaviors";
import { Router, MessageSchema, EntityId } from "@nilo/network";
import { Component, Prefab, World, Service } from "@nilo/ecs";
import {
  EntitySerializationState,
  NetworkComponent,
  EntitySerializationComponent,
  FriendlySchema,
} from "@nilo/ecs-networking";

/** An alias for type-erased NetworkEntity types. */
export type ErasedNetworkEntity = {
  new (e: EntityId): NetworkEntity<unknown>;
  readonly SCHEMA: FriendlySchema<unknown>;
  readonly UNIQUE_ID: number;
};

/** Implements type-erased network logic for an individual component. */
export interface ComponentCallbacks {
  /** The component's identifier within the ECS. */
  component: Component<unknown>;

  /**
   * Update (or insert) a component into the ECS.
   * @param e The ID of the component's entity
   * @param data The instantaneous serialized data of this component
   */
  update: (e: EntityId, data: Uint8Array) => void;
}

/** Type-erased network logic for a NetworkEntity in a GameEntityComponent. */
export interface NetworkEntityCallbacks {
  /** Schema for this network entity's data. */
  schema: FriendlySchema<unknown>;

  /**
   * Factory for uninitialized NetworkEntity implementations.
   * @param e The identifier for the new NetworkEntity
   * @returns A type-erased instance of NetworkEntity
   */
  create: (e: EntityId) => NetworkEntity<unknown>;
}

/** STAY DATA-ORIENTED IN THIS CLASS. */
export class DeserializeService extends Service {
  private _components: Map<number, ComponentCallbacks> = new Map();
  private _networkEntities: Map<number, NetworkEntityCallbacks> = new Map();

  public constructor(world: World) {
    // add this service to the world
    super(world);

    // add the component callbacks for GameEntityComponent
    this._components.set(GameEntityComponent.KEY, {
      component: GameEntityComponent,
      update: this.updateGameEntityComponent.bind(this),
    });

    // register each NetworkEntity type
    this.registerNetworkEntity(UserEntity);
    this.registerNetworkEntity(BrushStrokeEntity);
    this.registerNetworkEntity(EnvironmentEntity);
    this.registerNetworkEntity(MeshEntity);
    this.registerNetworkEntity(ParticleEntity);
    this.registerNetworkEntity(PrimitiveEntity);
    this.registerNetworkEntity(PromptEntity);
    this.registerNetworkEntity(SharedEntity);

    // automatically handle incoming components upon registration
    this.world.onRegisterComponent(this.onRegisterComponent.bind(this));
  }

  /** Looks up the component callbacks for a given component. */
  public getComponentCallbacks(
    component: Component<unknown>
  ): ComponentCallbacks | undefined {
    return this._components.get(component.KEY);
  }

  /** Looks up the network entity callbacks for a given network entity ID. */
  public getEntityCallbacks(id: number): NetworkEntityCallbacks | undefined {
    return this._networkEntities.get(id);
  }

  /** The network message router for network messages through this service. */
  public static ROUTER = new Router<DeserializeService>(
    logger.error.bind(logger)
  )
    .addHandler(MessageSchema.SpawnEntity, (ns, e) => {
      if (!ns.world.entities.has(e)) {
        ns.world.spawnEntityWithId(e);
      }
    })
    .addHandler(MessageSchema.KillEntity, (ns, e) => {
      if (ns.world.entities.has(e)) {
        const gameEntity = ns.world.getComponent(e, GameEntityComponent);
        if (gameEntity) {
          Client.removeEntity(gameEntity);
        } else {
          ns.world.removeEntity(e);
        }
      }
    })
    .addHandler(MessageSchema.InsertComponent, (ns, msg) => {
      const cbs = ns._components.get(msg.component);
      if (!cbs) {
        logger.error("unknown component key", { component: msg.component });
        return;
      }
      cbs.update(msg.entity, msg.data);
    })
    .addHandler(MessageSchema.RemoveComponent, (ns, msg) => {
      const cbs = ns._components.get(msg.component);
      if (!cbs) {
        logger.error("unknown component key", { component: msg.component });
        return;
      }
      ns.world.removeComponent(msg.entity, cbs.component);
    });

  private onRegisterComponent(component: Component<unknown>) {
    // handle only networked components
    if (!(component instanceof NetworkComponent)) {
      return;
    }

    // add callbacks for this component type
    this._components.set(component.KEY, {
      component,
      update: (entity, data) =>
        this.updateNetworkComponent(component, entity, data),
    });
  }

  private registerNetworkEntity(entity: ErasedNetworkEntity) {
    this._networkEntities.set(entity.UNIQUE_ID, {
      schema: entity.SCHEMA,
      create: (e) => new entity(e),
    });
  }

  private updateNetworkComponent(
    component: NetworkComponent<unknown, unknown>,
    entity: EntityId,
    data: Uint8Array
  ) {
    try {
      // parse component data using FriendlySchema
      const componentData = component.schema.deserializeBytes(data);

      // convert the component data into its associated component
      const componentValue = component.fromData(componentData);

      // insert the component onto the entity
      this.world.addComponent(entity, component, componentValue);

      // set the serialized component data
      this.setSerializedComponentData(entity, GameEntityComponent, data);
    } catch (error) {
      logger.error("Failed to update network component", {
        componentKey: component.KEY,
        componentName: component.constructor.name,
        entity,
        dataSize: data.length,
        hasSchema: !!component.schema,
        error,
        // Log data preview for debugging buffer corruption
        dataPreview: Array.from(data.slice(0, 16))
          .map((b) => b.toString(16).padStart(2, "0"))
          .join(" "),
      });
      // Don't rethrow - skip this component update but continue processing
    }
  }

  private updateGameEntityComponent(e: EntityId, data: Uint8Array) {
    try {
      // read the unique entity kind ID
      const reader = new ByteStreamReader(data);
      const id = reader.readUint32();

      // look up the entity-specific callbacks by ID
      const callbacks = this._networkEntities.get(id);
      if (!callbacks) {
        logger.error("unknown NetworkEntity ID", { id });
        return;
      }

      // initialize the entity with an error if needed
      if (!this.world.entities.has(e)) {
        logger.error("updating component before entity spawned", { e });
        this.world.spawnEntityWithId(e, []);
      }

      // look up the entity itself or initialize it
      let entity = this.world.getComponent(e, GameEntityComponent);
      if (!entity) {
        entity = callbacks.create(e);

        // TODO: this touches way more state than necessary
        // need to decouple entity prefabs from networking
        new Prefab("game-entity", [
          new TransformBehavior(entity.position, entity.rotation, entity.scale),
          new GameEntityBehavior(entity),
        ])
          .build()
          .addToEntity(e, this.world);

        entity.onRoomJoin();
      }

      // ensure the entity is a NetworkEntity
      // should be unreachable through callback registration
      if (!(entity instanceof NetworkEntity)) {
        const name = entity.constructor.name;
        logger.error(`${name} is not a NetworkEntity`);
        return;
      }

      // parse the data payload from bytes using FriendlySchema
      const remainingData = data.slice(reader.position);
      const payload = callbacks.schema.deserializeBytes(remainingData);

      // update the entity itself
      entity.deserialize(payload);

      // mark the entity as dirty within ECS
      this.world.markDirty(e, GameEntityComponent);

      // set the serialized component data
      this.setSerializedComponentData(e, GameEntityComponent, data);
    } catch (error) {
      logger.error("Failed to update NetworkEntity", {
        entity: e,
        dataSize: data.length,
        error,
        // Log data preview for debugging buffer corruption
        dataPreview: Array.from(data.slice(0, 16))
          .map((b) => b.toString(16).padStart(2, "0"))
          .join(" "),
      });
    }
  }

  private setSerializedComponentData(
    e: EntityId,
    component: Component<unknown>,
    data: Uint8Array
  ) {
    // ensure entity has a serialized state
    let serialization = this.world.getComponent(
      e,
      EntitySerializationComponent
    );

    if (!serialization) {
      serialization = new EntitySerializationState();
      this.world.addComponent(e, EntitySerializationComponent, serialization);
    }

    // set a *clean* serialized data for this component
    const serializedComponent = serialization.components.get(component.KEY);
    if (serializedComponent) {
      serializedComponent.dirty = false;
      serializedComponent.data = data;
    } else {
      serialization.components.set(component.KEY, {
        dirty: false,
        data,
      });
    }
  }
}
