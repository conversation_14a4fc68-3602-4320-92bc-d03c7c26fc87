export type Multicaster = ReturnType<typeof createMulticaster>;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const createMulticaster = <TFunc extends (...args: any[]) => any>() => {
  type MulticasterSubscriber = { callback: TFunc; priority: number };

  // Store subscribers as array of {callback, priority} objects
  const subscribers: MulticasterSubscriber[] = [];

  const multicaster = function __multicasterEmit(
    ...args: Parameters<TFunc>
  ): ReturnType<TFunc> {
    let result: ReturnType<TFunc> | undefined = undefined;
    const callMulticastHandler = ({ callback }: MulticasterSubscriber) =>
      (result = callback(...args));
    subscribers.forEach(callMulticastHandler);
    return result as ReturnType<TFunc>;
  };

  multicaster.sub = (callback: TFunc, priority = 0) => {
    // Find the correct insertion point to maintain sorted order
    const insertIndex = subscribers.findIndex((sub) => sub.priority < priority);
    const newSubscriber = { callback, priority };

    if (insertIndex === -1) {
      // If no lower priority found, append to end
      subscribers.push(newSubscriber);
    } else {
      // Insert at the correct position
      subscribers.splice(insertIndex, 0, newSubscriber);
    }

    // Return unsubscribe function for convenience
    return () => multicaster.unsub(callback);
  };

  multicaster.unsub = (callback: TFunc) => {
    const index = subscribers.findIndex((sub) => sub.callback === callback);
    if (index !== -1) {
      subscribers.splice(index, 1);
    }
  };

  multicaster.clear = () => {
    subscribers.length = 0;
  };

  return multicaster;
};
