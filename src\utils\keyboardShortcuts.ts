import {
  keyboardMappingDict,
  KeyboardMappingID,
} from "@/config/keyboardMapping";

/**
 * TODO: Optimize this to be more efficient.
 *
 * We're looping over all available actions on every getKeyboardAction().
 * That's fine for now, but the list will grow.
 *
 * Eventually we should flip thing the other way around and hash map this baby.
 * To do that, we want to make sure to:
 * 1. Create a key combo string from KeyboardEvent. Like,
 *   {shiftKey:false, ctrlKey: true, code: "KeyQ", ...} -> "ctrl+keyq" (lowercase)
 * 2. Reverse the yaml dict: modifier(s)+key -> action_name
 *   - Should sort modifiers the same way we use them in step 1.
 *     So that both "Shift+Ctrl+KeyQ" and "Ctrl+Shift+KeyQ" map to "ctrl+shift+keyq".
 *   - We might want to support multiple actions for the same key combo.
 *     (app knows which it needs in what context)
 *     So maybe modifier(s)+key -> [action_name, ...] is better.
 */

// Modifier keys that should be treated specially
const MODIFIER_KEYS = new Set(["ctrl", "shift", "alt", "meta"]);

// Create the shortcut parser with caching
const parser = createShortcutParser();

interface ParsedShortcut {
  modifiers: {
    ctrl: boolean;
    shift: boolean;
    alt: boolean;
    meta: boolean;
  };
  key: string;
}

/**
 * Compare a keyboard event to a parsed shortcut
 * @param event - Keyboard event from browser
 * @param shortcut - Parsed shortcut to compare against
 * @returns True if the event matches the shortcut
 */
function compareEventToShortcut(
  event: KeyboardEvent,
  shortcut: ParsedShortcut
): boolean {
  // Check modifiers
  if (shortcut.modifiers.ctrl !== event.ctrlKey) return false;
  if (shortcut.modifiers.shift !== event.shiftKey) return false;
  if (shortcut.modifiers.alt !== event.altKey) return false;
  if (shortcut.modifiers.meta !== event.metaKey) return false;

  // Check key (convert event code to lowercase for comparison)
  return event.code.toLowerCase() === shortcut.key;
}

/**
 * Global function to check a keyboard event against all configured shortcuts
 * @param event - Keyboard event from browser
 * @returns The action key if a match is found, null otherwise
 */
export function getKeyboardAction(
  event: KeyboardEvent
): KeyboardMappingID | null {
  for (const [actionKey, shortcutString] of Object.entries(
    keyboardMappingDict
  )) {
    // Split by comma to handle multiple shortcuts for the same action
    const shortcutStrings = shortcutString
      .split(",")
      .map((s: string) => s.trim())
      .filter((s: string) => s.length > 0); // Filter out empty strings from trailing commas

    for (const singleShortcutString of shortcutStrings) {
      const parsedShortcut = parser.parseShortcut(singleShortcutString);

      if (compareEventToShortcut(event, parsedShortcut)) {
        return actionKey as KeyboardMappingID;
      }
    }
  }

  return null;
}

/**
 * Factory function that creates a shortcut parser with built-in caching
 * @returns Parser object with parseShortcut method
 */
function createShortcutParser() {
  // Cache for parsed shortcuts to avoid re-parsing on every keyboard event
  const parsedShortcutsCache = new Map<string, ParsedShortcut>();

  /**
   * Parse a keyboard shortcut string into modifiers and key
   * @param shortcut - String like "Ctrl+KeyQ" or "Ctrl+Shift+KeyQ"
   * @returns Parsed shortcut with modifiers and key
   */
  function parseShortcut(shortcut: string): ParsedShortcut {
    // Check cache first
    if (parsedShortcutsCache.has(shortcut)) {
      return parsedShortcutsCache.get(shortcut)!;
    }

    const parts = shortcut.split("+").map((part) => part.trim().toLowerCase());

    const modifiers = {
      ctrl: false,
      shift: false,
      alt: false,
      meta: false,
    };

    let key = "";

    for (const part of parts) {
      if (MODIFIER_KEYS.has(part)) {
        modifiers[part as keyof typeof modifiers] = true;
      } else {
        key = part;
      }
    }

    const parsedShortcut = { modifiers, key };

    // Cache the parsed shortcut
    parsedShortcutsCache.set(shortcut, parsedShortcut);

    return parsedShortcut;
  }

  // Pre-populate the cache with all configured shortcuts
  for (const shortcutString of Object.values(keyboardMappingDict)) {
    // Split by comma to handle multiple shortcuts for the same action
    const shortcutStrings = shortcutString
      .split(",")
      .map((s: string) => s.trim())
      .filter((s: string) => s.length > 0); // Filter out empty strings from trailing commas
    for (const singleShortcutString of shortcutStrings) {
      parseShortcut(singleShortcutString);
    }
  }

  return {
    parseShortcut,
  };
}
