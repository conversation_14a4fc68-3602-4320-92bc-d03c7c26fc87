/**
 * Jolt<PERSON>ebworker - Webworker implementation for Jolt Physics
 *
 * This webworker provides a dedicated 60 FPS physics loop using setInterval,
 * ensuring consistent physics simulation regardless of display refresh rate.
 *
 * Key Features:
 * - Dedicated 60 FPS physics loop (16.67ms intervals)
 * - Separation of physics simulation from render loop
 * - Consistent timing for stable physics behavior
 * - Proper cleanup and resource management
 *
 * The physics loop runs independently of the main render loop, providing
 * stable physics simulation even when the display frame rate varies.
 */

import * as Comlink from "comlink";
import {
  createJoltPhysicsService,
  JoltPhysicsService,
} from "../JoltPhysicsService";
import { RagdollSettings } from "../types/Ragdoll";
import { BodyGeometry, PhysicsBody } from "../types/Body";
import { PhysicsShapeType } from "../types/PhysicsOptions";
import { PhysicsStepInfo } from "../types/PhysicsStep";
import {
  BodyControlApi,
  CharacterControlApi,
  JoltPhysicsServiceApi,
  Rag<PERSON>llControlApi,
} from "../types/webworker";
import { JoltBody } from "../types/debug";
import { CharacterPhysicsSettings } from "../types/character";
import { PhysicsStepController } from "./PhysicsStepController";

class JoltWebworker implements JoltPhysicsServiceApi {
  private readonly PHYSICS_FPS = 60;

  private _physicsService: JoltPhysicsService | undefined;
  private readonly _physicsStepController: PhysicsStepController;
  private _initPromise: Promise<boolean>;

  private _isPhysicsLoopRunning = false;

  constructor() {
    this._initPromise = this.initPhysicsService();
    this._physicsStepController = new PhysicsStepController(
      this.physicsStep.bind(this),
      this.PHYSICS_FPS
    );
  }

  public get isInitialized(): boolean {
    return this._physicsService !== undefined;
  }

  public getInitPromise(): Promise<boolean> {
    return this._initPromise;
  }

  private async initPhysicsService(): Promise<boolean> {
    this._physicsService = await createJoltPhysicsService();
    return true;
  }

  public async startPhysicsLoop(): Promise<void> {
    await this._initPromise;

    if (this._isPhysicsLoopRunning) {
      console.warn("⚠️ Physics loop is already running");
      return;
    }

    this._isPhysicsLoopRunning = true;
    this._physicsStepController.startLoop();
    return this._physicsService!.startPhysicsLoop();
  }

  public async stopPhysicsLoop(): Promise<void> {
    await this._initPromise;

    if (!this._isPhysicsLoopRunning) {
      console.warn("⚠️ Physics loop is already stopped");
      return;
    }

    this._isPhysicsLoopRunning = false;

    this._physicsStepController.stopLoop();

    console.debug("🛑 Physics loop stopped");
    return this._physicsService!.stopPhysicsLoop();
  }

  public isPhysicsLoopRunning(): boolean {
    return this._isPhysicsLoopRunning;
  }

  public physicsStep(deltaTime: number, numberOfSteps: number): void {
    this._physicsService!.physicsStep(deltaTime, numberOfSteps);
  }

  public getLastStepInfo(): PhysicsStepInfo {
    return this._physicsService!.getLastStepInfo();
  }

  public getObjectBodyControlPairs(): [string, BodyControlApi][] {
    return this._physicsService!.getObjectBodyControlPairs();
  }

  public getAllBodies(getAllShapes: boolean): JoltBody[] {
    return this._physicsService!.getAllBodies(getAllShapes);
  }

  public registerRagdollForPhysics(
    ragdollSettings: RagdollSettings
  ): Comlink.Remote<RagdollControlApi> | RagdollControlApi {
    const ragdollControl =
      this._physicsService!.registerRagdollForPhysics(ragdollSettings);
    return Comlink.proxy(ragdollControl);
  }

  public async registerObjectForPhysics(
    physicsBody: PhysicsBody,
    geometry: BodyGeometry
  ): Promise<Comlink.Remote<BodyControlApi> | BodyControlApi | undefined> {
    const bodyControl = await this._physicsService!.registerObjectForPhysics(
      physicsBody,
      geometry
    );
    if (bodyControl) {
      return Comlink.proxy(bodyControl);
    }
    return undefined;
  }

  public unregisterObjectFromPhysics(id: string): void {
    this._physicsService!.unregisterObjectFromPhysics(id);
  }

  public unregisterRagdollFromPhysics(
    ragdollControl: RagdollControlApi | Comlink.Remote<RagdollControlApi>
  ): void {
    this._physicsService!.unregisterRagdollFromPhysics(
      ragdollControl as RagdollControlApi
    );
  }

  public unregisterCharacterFromPhysics(
    characterControl: Comlink.Remote<CharacterControlApi> | CharacterControlApi
  ): void {
    this._physicsService!.unregisterCharacterFromPhysics(
      characterControl as CharacterControlApi
    );
  }

  public createCharacterPhysics(
    characterSettings: Partial<CharacterPhysicsSettings>
  ): Comlink.Remote<CharacterControlApi> | CharacterControlApi {
    const characterControl =
      this._physicsService!.createCharacterPhysics(characterSettings);
    return Comlink.proxy(characterControl);
  }

  public destroy(): void {
    // Stop the physics loop if it's running
    if (this._isPhysicsLoopRunning) {
      this.stopPhysicsLoop();
    }

    return this._physicsService!.destroy();
  }

  public getPhysicsFPS(): number {
    return this.PHYSICS_FPS;
  }

  public async findBestShape(
    geometry: BodyGeometry
  ): Promise<PhysicsShapeType> {
    return this._physicsService!.findBestShape(geometry);
  }

  public setGravityEnabled(enabled: boolean): void {
    return this._physicsService!.setGravityEnabled(enabled);
  }

  public getStaticBodiesCount(): number {
    return this._physicsService!.getStaticBodiesCount();
  }

  public getDynamicBodiesCount(): number {
    return this._physicsService!.getDynamicBodiesCount();
  }
}

const joltWebworker = new JoltWebworker();

Comlink.expose(joltWebworker);

//Physics loop
