import { useStorage } from "@liveblocks/react";
import { Trash2 } from "lucide-react";

import { Button } from "../ui/button";
import { Separator } from "../ui/separator";
import { BasePanel } from "./BasePanel";
import {
  useEditorUIActions,
  useEditorUIState,
} from "@/contexts/EditorUIContext";
import { Client } from "@/core/client";
import { runCommands } from "@/core/command";
import SelectEntitiesCommand from "@/core/command/commands/selectEntities";
import { EntitySelectionMode } from "@/core/util/EntitySelectionMode";
import { useMySelectedEntities } from "@/hooks/useMySelectedEntities";
import { cn } from "@/lib/utils";
import { EntityType, GameEntityData } from "@/liveblocks.config";
import { deleteEntities } from "@/core/commands/EntitiesDelete";

type EntityWithId = GameEntityData & { id: string };

const SHOW_EXTRA_INFO = true;
const SHOW_DELETE_BUTTON = false;
const EXCLUDED_ENTITY_TYPES: EntityType[] = [
  EntityType.SharedEntity,
  EntityType.EnvironmentEntity,
];

export function EntitiesPanel() {
  const { toggleEntitiesPanel, toggleLeftPanelPinned } = useEditorUIActions();
  const { isLeftPanelPinned } = useEditorUIState();
  const { isEntitySelected } = useMySelectedEntities();

  // Get all entities from storage
  const entities =
    useStorage((root) => {
      const entitiesMap = root.entities;
      const entitiesArray: EntityWithId[] = [];

      entitiesMap.forEach((entity, id) => {
        // Skip excluded entity types
        if (EXCLUDED_ENTITY_TYPES.includes(entity.type)) {
          return;
        }

        entitiesArray.push({ ...entity, id });
      });

      return entitiesArray;
    }) || [];

  const getEntityTypeColor = (type: EntityType) => {
    const colors = {
      [EntityType.MeshEntity]: "text-pink-400",
      [EntityType.PrimitiveEntity]: "text-rose-400",
      [EntityType.UserEntity]: "text-purple-400",
      [EntityType.PromptEntity]: "text-amber-400",
      [EntityType.EnvironmentEntity]: "text-slate-400",
      [EntityType.BrushStrokeEntity]: "text-yellow-400",
      [EntityType.SharedEntity]: "text-blue-400",
      [EntityType.ParticleEntity]: "text-cyan-400",
      [EntityType.PreviewPrimitive]: "text-indigo-400",
    };
    return colors[type] || "text-slate-400";
  };

  const formatEntityType = (type: EntityType) => {
    return type
      .replace("Entity", "")
      .replace(/([A-Z])/g, " $1")
      .trim();
  };

  const handleEntityDelete = (entityId: string) => {
    deleteEntities([entityId]);
  };

  const handleEntitySelect = (entityId: string) => {
    const entity = Client.getEntity(entityId);
    if (entity && entity.selected()) {
      // If already selected, deselect it
      runCommands(
        new SelectEntitiesCommand([entityId], EntitySelectionMode.REMOVE)
      );
    } else {
      // If not selected, select it
      runCommands(
        new SelectEntitiesCommand([entityId], EntitySelectionMode.REPLACE)
      );
    }
  };

  return (
    <BasePanel
      title="Entities"
      onClose={() => toggleEntitiesPanel(false)}
      onPin={() => toggleLeftPanelPinned()}
      isPinned={isLeftPanelPinned}
    >
      <Separator />

      {/* Entity list */}
      <div className="w-full space-y-0">
        {entities.map((entity) => (
          <div
            key={entity.id}
            onClick={() => handleEntitySelect(entity.id)}
            className={cn(
              "flex items-center justify-between gap-4 rounded-nilo-default px-2 py-1 transition-all cursor-pointer",
              isEntitySelected(entity.id)
                ? "bg-nilo-fill-primary-dark text-nilo-text-quaternary"
                : "hover:bg-nilo-fill-secondary-hover active:bg-nilo-fill-tertiary focus-visible:bg-nilo-fill-secondary-hover "
            )}
          >
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <div className="flex-1 min-w-0">
                <div
                  className={cn(
                    "text-xs font-bold truncate transition-all",
                    isEntitySelected(entity.id)
                      ? ""
                      : getEntityTypeColor(entity.type as EntityType)
                  )}
                >
                  {formatEntityType(entity.type as EntityType)}
                </div>
                {SHOW_EXTRA_INFO && (
                  <div
                    className={
                      isEntitySelected(entity.id)
                        ? "text-nilo-text-quaternary"
                        : "text-muted-foreground"
                    }
                  >
                    <div className="text-[10px]">
                      Position: ({Number(entity.positionX || 0).toFixed(1)},{" "}
                      {Number(entity.positionY || 0).toFixed(1)},{" "}
                      {Number(entity.positionZ || 0).toFixed(1)})
                    </div>
                    {entity.type === EntityType.MeshEntity &&
                      (entity as GameEntityData & { prompt?: string })
                        .prompt && (
                        <div className="text-[10px] truncate">
                          Prompt: &quot;
                          {String(
                            (entity as GameEntityData & { prompt?: string })
                              .prompt
                          )}
                          &quot;
                        </div>
                      )}
                    {entity.type === EntityType.PrimitiveEntity &&
                      (entity as GameEntityData & { primitiveType?: string })
                        .primitiveType && (
                        <div className="text-[10px]">
                          Shape:{" "}
                          {String(
                            (
                              entity as GameEntityData & {
                                primitiveType?: string;
                              }
                            ).primitiveType
                          )}
                        </div>
                      )}
                  </div>
                )}
              </div>
            </div>

            {SHOW_DELETE_BUTTON && (
              <div className="flex items-center gap-1 ml-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEntityDelete(entity.id);
                  }}
                  className="h-6 w-6 p-0"
                  title="Delete entity"
                >
                  <Trash2 className="h-2 w-2" />
                </Button>
              </div>
            )}
          </div>
        ))}
      </div>

      {entities.length === 0 && (
        <div className="text-center text-muted-foreground py-8">
          No entities found
        </div>
      )}
    </BasePanel>
  );
}
