# Scripts Directory

This directory contains utility scripts for development and testing workflows.

## Available Scripts

### `release-workflow.sh`

Tests the GitHub release workflow locally using real git operations. This script validates that the release process will work correctly before running it in production.

#### Usage

```bash
./scripts/release-workflow.sh <release-name> [--cleanup]
```

#### Parameters

- `<release-name>` - **Required**: Name for the release branch (will create `release/<name>`)
- `--cleanup` - **Optional**: Delete the release branch after testing

#### Examples

```bash
# Test workflow and keep the branch for inspection
./scripts/release-workflow.sh v1.2.0

# Test workflow and automatically clean up
./scripts/release-workflow.sh my-feature --cleanup

# Test hotfix workflow with cleanup
./scripts/release-workflow.sh hotfix-123 --cleanup

# Show help
./scripts/release-workflow.sh --help
```

#### What It Does

1. **Validates Environment**: Checks git repository and required branches
2. **Fetches Latest**: Updates `main` and `prod` branches from remote
3. **Finds Divergence**: Identifies where `prod` diverged from `main`
4. **Creates Release Branch**: Creates `release/<name>` from `prod`
5. **Reverts Commits**: Reverts all commits back to the divergence point
6. **Merges Main**: Merges `main` into the release branch (should be conflict-free)
7. **Shows Results**: Displays branch status and commit history
8. **Optional Cleanup**: Deletes the test branch if `--cleanup` is specified

#### Safety Features

- ✅ **Local Only**: No `git push` or PR creation
- ✅ **Non-Destructive**: Original branch is always restored
- ✅ **Error Handling**: Automatic cleanup on failures
- ✅ **Validation**: Extensive checks before each operation

#### Output

The script provides colored, step-by-step output showing:

- Branch creation and switching
- Commit analysis and revert operations
- Merge results and conflict detection
- Final branch status and recommendations

#### Use Cases

- **Pre-Release Validation**: Test the workflow before creating actual releases
- **Debugging**: Investigate merge conflicts or workflow issues
- **Training**: Understand how the release process works
- **CI/CD Development**: Validate workflow changes locally

#### Related Files

- `.github/workflows/create-release.yml` - The actual GitHub workflow this script tests
- `.github/workflows/prod-merge.yml` - Handles merging release branches to prod

### `cleanup-branches.sh`

Safely deletes all local branches matching a specified pattern. This script provides a flexible way to clean up branches by name pattern with built-in safety features.

#### Usage

```bash
./scripts/cleanup-branches.sh <pattern> [--force] [--dry-run]
```

#### Parameters

- `<pattern>` - **Required**: Branch pattern to match (e.g., 'release', 'fabio', 'feature')

#### Options

- `--force` - Skip confirmation prompt and delete branches immediately
- `--dry-run` - Show what would be deleted without actually deleting
- `--help, -h` - Show help message

#### Examples

```bash
# Delete all branches containing 'release'
./scripts/cleanup-branches.sh release

# Preview deletion of branches containing 'fabio'
./scripts/cleanup-branches.sh fabio --dry-run

# Delete all 'feature' branches without confirmation
./scripts/cleanup-branches.sh feature --force

# Interactive deletion of 'hotfix' branches
./scripts/cleanup-branches.sh hotfix
```

#### Pattern Matching

- Matches branches containing the pattern **anywhere** in the name
- `'release'` matches: `release/v1.0`, `my-release-branch`, `hotfix-release`
- `'fabio'` matches: `fabio/feature`, `feature-fabio`, `fabio-hotfix`

#### Safety Features

- ✅ **Pattern-based**: Only deletes branches matching the specified pattern
- ✅ **Current Branch Protection**: Automatically switches away if current branch matches
- ✅ **Confirmation Prompt**: Interactive confirmation unless `--force` is used
- ✅ **Dry Run Mode**: Preview deletions with `--dry-run`
- ✅ **Detailed Output**: Shows exactly what will be deleted
- ✅ **Error Handling**: Graceful handling of deletion failures

#### Use Cases

- **Release Cleanup**: `./scripts/cleanup-branches.sh release --force`
- **Personal Branch Cleanup**: `./scripts/cleanup-branches.sh your-name`
- **Feature Branch Cleanup**: `./scripts/cleanup-branches.sh feature`
- **Safe Preview**: `./scripts/cleanup-branches.sh pattern --dry-run`

## Adding New Scripts

When adding new scripts to this directory:

1. Make them executable: `chmod +x scripts/your-script.sh`
2. Add proper help documentation with `--help` flag
3. Include error handling and cleanup
4. Update this README with usage instructions
5. Follow the existing naming convention
