name: Integration Tests PR Comment Trigger

permissions:
  issues: write
  pull-requests: write
  actions: write
  contents: read

on:
  issue_comment:
    types: [created]

jobs:
  trigger-tests:
    # Only run on pull request comments
    if: github.event.issue.pull_request != null
    runs-on: ubuntu-latest
    steps:
      - name: Check if comment is from collaborator
        id: check-permissions
        uses: actions/github-script@v7
        with:
          result-encoding: string
          script: |
            try {
              const { data: permission } = await github.rest.repos.getCollaboratorPermissionLevel({
                owner: context.repo.owner,
                repo: context.repo.repo,
                username: context.payload.comment.user.login
              });
              
              const hasPermission = ['admin', 'write'].includes(permission.permission);
              
              if (!hasPermission) {
                console.log(`User ${context.payload.comment.user.login} does not have permission to trigger tests`);
              }
              
              return hasPermission.toString();
            } catch (error) {
              console.log(`Error checking permissions: ${error}`);
              return 'false';
            }

      - name: Check comment content and trigger tests
        if: steps.check-permissions.outputs.result == 'true'
        uses: actions/github-script@v7
        with:
          script: |
            const comment = context.payload.comment.body.trim();

            // Strip out all quoted lines (lines starting with >) and check for trigger emoji
            const sanitizedComment = comment
              .split('\n')
              .filter(line => !line.trim().startsWith('>'))
              .join('\n');

            if (sanitizedComment.includes('🧪') || sanitizedComment.includes(':test_tube:')) {
              // Get PR details
              const { data: pr } = await github.rest.pulls.get({
                owner: context.repo.owner,
                repo: context.repo.repo,
                pull_number: context.issue.number
              });
              
              // Extract labels as comma-separated string
              const labelNames = pr.labels.map(label => label.name).join(',');
              
              console.log(`PR Title: ${pr.title}`);
              console.log(`PR Labels: ${labelNames}`);
              console.log(`PR Branch: ${pr.head.ref}`);
              
              // React to the comment to show it was received
              await github.rest.reactions.createForIssueComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: context.payload.comment.id,
                content: 'rocket'
              });
              
              // Trigger the integration tests workflow
              await github.rest.actions.createWorkflowDispatch({
                owner: context.repo.owner,
                repo: context.repo.repo,
                workflow_id: 'integration-tests.yml',
                ref: pr.head.ref,
                inputs: {
                  pr_number: context.issue.number.toString()
                }
              });
              
              // Wait and retry to find the triggered integration test run
              console.log('Looking for the triggered integration test run...');
              let integrationTestRunId = null;
              
              // Retry logic to find the integration test run (similar to gate workflow)
              const maxRetries = 6;  // 6 retries over ~30 seconds
              const retryDelay = 5000;  // 5 seconds between retries
              
              for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                  console.log(`Attempt ${attempt}/${maxRetries} to find integration test run...`);
                  
                  // Get ALL Integration Tests runs (don't filter by head_sha in API call)
                  const { data: runsData } = await github.rest.actions.listWorkflowRuns({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    workflow_id: 'integration-tests.yml',
                    per_page: 50  // Increased to get more runs
                  });
                  
                  // Filter and find the most recent run for this commit
                  const relevantRuns = runsData.workflow_runs
                    .filter(run => run.head_sha === pr.head.sha)
                    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                  
                  if (relevantRuns.length > 0) {
                    const relevantRun = relevantRuns[0];
                    integrationTestRunId = relevantRun.id;
                    console.log(`✅ Found integration test run ID: ${integrationTestRunId}, status: ${relevantRun.status}`);
                    break;  // Found it, exit retry loop
                  } else {
                    console.log(`❌ No integration test run found for commit ${pr.head.sha} (attempt ${attempt}/${maxRetries})`);
                    
                    // Don't wait after the last attempt
                    if (attempt < maxRetries) {
                      console.log(`⏳ Waiting ${retryDelay/1000} seconds before next attempt...`);
                      await new Promise(resolve => setTimeout(resolve, retryDelay));
                    }
                  }
                } catch (error) {
                  console.log(`Error finding integration test run (attempt ${attempt}/${maxRetries}): ${error.message}`);
                  
                  // Don't wait after the last attempt
                  if (attempt < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                  }
                }
              }
              
              if (!integrationTestRunId) {
                console.log('⚠️ Could not find integration test run after all retries. This may be normal if the workflow takes longer to appear in the API.');
              }
              
              // Find and re-run the original gate workflow run for this PR
              console.log('Looking for original gate workflow run to re-run...');
              
              try {
                // Get all gate workflow runs for this commit
                const { data: gateRuns } = await github.rest.actions.listWorkflowRuns({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  workflow_id: 'integration-test-gate.yml',
                  head_sha: pr.head.sha,
                  per_page: 50
                });
                
                // Find the most recent gate run for this PR
                const relevantRun = gateRuns.workflow_runs
                  .filter(run => run.head_sha === pr.head.sha)
                  .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0];
                
                if (relevantRun) {
                  console.log(`Found gate run ID: ${relevantRun.id}, status: ${relevantRun.status}, conclusion: ${relevantRun.conclusion}`);
                  
                  // Re-run the original gate workflow
                  await github.rest.actions.reRunWorkflow({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    run_id: relevantRun.id
                  });
                  
                  console.log(`Successfully re-ran gate workflow run ${relevantRun.id}`);
                } else {
                  console.log('No gate workflow run found for this commit. This might be expected if no gate has run yet.');
                  
                  // Fallback: trigger a new gate workflow if no existing run found
                  await github.rest.actions.createWorkflowDispatch({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    workflow_id: 'integration-test-gate.yml',
                    ref: pr.head.ref,
                    inputs: {
                      pr_number: context.issue.number.toString(),
                      pr_title: pr.title,
                      pr_labels: labelNames
                    }
                  });
                  
                  console.log('Triggered new gate workflow as fallback');
                }
              } catch (error) {
                console.log(`Error handling gate workflow: ${error.message}`);
                
                // Fallback: trigger a new gate workflow on error
                await github.rest.actions.createWorkflowDispatch({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  workflow_id: 'integration-test-gate.yml',
                  ref: pr.head.ref,
                  inputs: {
                    pr_number: context.issue.number.toString(),
                    pr_title: pr.title,
                    pr_labels: labelNames
                  }
                });
                
                console.log('Triggered new gate workflow as error fallback');
              }
              
              // Comment back with confirmation
              let progressUrl;
              if (integrationTestRunId) {
                progressUrl = `https://github.com/${context.repo.owner}/${context.repo.repo}/actions/runs/${integrationTestRunId}`;
              } else {
                progressUrl = `https://github.com/${context.repo.owner}/${context.repo.repo}/actions/workflows/integration-tests.yml`;
              }
              
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: `🧪 Comment detected.\n🚀 Integration tests triggered for commit \`${pr.head.sha.substring(0, 7)}\` on branch \`${pr.head.ref}\`\n\nView progress: ${progressUrl}`
              });
            }

      - name: Comment permission denied
        if: steps.check-permissions.outputs.result == 'false' && contains(github.event.comment.body, '🧪')
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: `❌ @${context.payload.comment.user.login} You don't have permission to trigger integration tests. Only repository collaborators with write access can use this command.`
            });
