import { expect, test } from "@jest/globals";
import { World } from "../src/world";
import { Query } from "../src/query";
import { Position, Velocity, Color } from "./common";

test("getComponent fallback to pending during locked section", () => {
  const world = new World();
  const entity = world.addEntity([[Position, { x: 1, y: 2 }]]);
  const entityData = world.entityData(entity)!;

  const query = new Query({ pos: Position });
  query.forEach(world, (_entityId, _data) => {
    // Test World API
    world.addComponent(entity, Velocity, { x: 5, y: 10 });
    expect(world.getComponent(entity, Velocity)).toEqual({ x: 5, y: 10 });
    expect(world.hasComponent(entity, Velocity)).toBe(true);

    // Test EntityData API
    expect(entityData.getComponent(Velocity)).toEqual({ x: 5, y: 10 });
    expect(entityData.hasComponent(Velocity)).toBe(true);

    world.setComponent(entity, Velocity, { x: 5, y: 20 });
    expect(world.getComponent(entity, Velocity)).toEqual({ x: 5, y: 20 });
  });

  expect(world.getComponent(entity, Velocity)).toEqual({ x: 5, y: 20 });
  expect(entityData.getComponent(Velocity)).toEqual({ x: 5, y: 20 });
});

test("hasComponent checks both archetype and pending", () => {
  const world = new World();
  const entity = world.addEntity([[Position, { x: 1, y: 2 }]]);
  const entityData = world.entityData(entity)!;

  const query = new Query({ pos: Position });
  query.forEach(world, (_entityId, _data) => {
    expect(entityData.hasComponent(Position)).toBe(true);
    expect(world.hasComponent(entity, Position)).toBe(true);

    entityData.addComponent(Velocity, { x: 5, y: 10 });

    expect(entityData.hasComponent(Velocity)).toBe(true);
    expect(world.hasComponent(entity, Velocity)).toBe(true);
    expect(entityData.hasComponent(Color)).toBe(false);
    expect(world.hasComponent(entity, Color)).toBe(false);
  });
});

test("allComponents includes both archetype and pending", () => {
  const world = new World();
  const entity = world.addEntity([[Position, { x: 1, y: 2 }]]);
  const entityData = world.entityData(entity)!;

  const query = new Query({ pos: Position });
  query.forEach(world, (_entityId, _data) => {
    entityData.addComponent(Velocity, { x: 5, y: 10 });
    entityData.addComponent(Color, { x: 255, y: 0, z: 0 });

    // Test individual component access
    expect(entityData.getComponent(Velocity)).toEqual({ x: 5, y: 10 });
    expect(world.getComponent(entity, Velocity)).toEqual({ x: 5, y: 10 });

    // Test allComponents enumeration
    const components = entityData.allComponents();
    expect(components).toHaveLength(3);
    const componentMap = new Map(components);
    expect(componentMap.get(Position)).toEqual({ x: 1, y: 2 });
    expect(componentMap.get(Velocity)).toEqual({ x: 5, y: 10 });
    expect(componentMap.get(Color)).toEqual({ x: 255, y: 0, z: 0 });
  });
});

test("setComponent during locked sections", () => {
  const world = new World();
  const entity = world.addEntity([[Position, { x: 1, y: 2 }]]);
  const entityData = world.entityData(entity)!;

  const query = new Query({ pos: Position });
  query.forEach(world, (_entityId, _data) => {
    // Test setComponent on existing archetype component
    entityData.setComponent(Position, { x: 100, y: 200 });
    expect(entityData.getComponent(Position)).toEqual({ x: 100, y: 200 });

    // Test setComponent on pending component
    entityData.addComponent(Velocity, { x: 5, y: 10 });
    entityData.setComponent(Velocity, { x: 15, y: 25 });
    expect(entityData.getComponent(Velocity)).toEqual({ x: 15, y: 25 });
  });

  expect(entityData.getComponent(Position)).toEqual({ x: 100, y: 200 });
  expect(entityData.getComponent(Velocity)).toEqual({ x: 15, y: 25 });
});

test("removeComponent during locked sections", () => {
  const world = new World();
  const entity1 = world.addEntity([[Position, { x: 1, y: 2 }]]);
  const entity2 = world.addEntity([[Position, { x: 3, y: 4 }]]);
  const entityData1 = world.entityData(entity1)!;
  const entityData2 = world.entityData(entity2)!;

  const query = new Query({ pos: Position });
  query.forEach(world, (_entityId, _data) => {
    // Test removing pending component
    entityData1.addComponent(Velocity, { x: 5, y: 10 });
    expect(entityData1.hasComponent(Velocity)).toBe(true);
    entityData1.removeComponent(Velocity);
    expect(entityData1.hasComponent(Velocity)).toBe(false);
    expect(entityData1.getComponent(Velocity)).toBeNull();

    // Test removing archetype component
    entityData2.removeComponent(Position);
  });

  expect(entityData1.hasComponent(Velocity)).toBe(false);
  expect(entityData2.hasComponent(Position)).toBe(false);
});

test("add-remove-add sequence during locked section", () => {
  const world = new World();
  const entity = world.addEntity();
  const entityData = world.entityData(entity)!;

  world.addComponent(entity, Position, { x: 0, y: 0 });

  const query = new Query({ pos: Position });
  query.forEach(world, (_entityId, _data) => {
    entityData.addComponent(Velocity, { x: 1, y: 1 });
    expect(entityData.hasComponent(Velocity)).toBe(true);
    expect(entityData.getComponent(Velocity)).toEqual({ x: 1, y: 1 });

    entityData.removeComponent(Velocity);
    expect(entityData.hasComponent(Velocity)).toBe(false);

    entityData.addComponent(Velocity, { x: 2, y: 2 });
    expect(entityData.hasComponent(Velocity)).toBe(true);
    expect(entityData.getComponent(Velocity)).toEqual({ x: 2, y: 2 });
  });

  expect(entityData.hasComponent(Velocity)).toBe(true);
  expect(entityData.getComponent(Velocity)).toEqual({ x: 2, y: 2 });
});

test("entity lifecycle and pending component management", () => {
  const world = new World();
  const entity1 = world.addEntity([[Position, { x: 1, y: 2 }]]);
  const entity2 = world.addEntity([[Position, { x: 3, y: 4 }]]);

  const query = new Query({ pos: Position });
  query.forEach(world, (_entityId, _data) => {
    world.addComponent(entity1, Velocity, { x: 5, y: 10 });
    world.addComponent(entity2, Velocity, { x: 15, y: 20 });

    expect(world.hasComponent(entity1, Velocity)).toBe(true);
    expect(world.hasComponent(entity2, Velocity)).toBe(true);

    // Test entity removal deferral
    world.removeEntity(entity1);
    expect(world.isAlive(entity1)).toBe(true); // Still alive during locked section
  });

  // Test entity removal after flush
  expect(world.isAlive(entity1)).toBe(false);
  expect(world.hasComponent(entity2, Velocity)).toBe(true);

  // Test manual pending component cleanup
  world.acquireArchetypeLock();
  world.addComponent(entity2, Color, { x: 255, y: 0, z: 0 });
  expect(world.hasPendingComponent(entity2, Color)).toBe(true);
  world.releaseArchetypeLock();

  expect(world.hasPendingComponent(entity2, Color)).toBe(false);
  expect(world.hasComponent(entity2, Color)).toBe(true);
});

test("multiple entities with pending components", () => {
  const world = new World();
  const entity1 = world.addEntity([[Position, { x: 1, y: 2 }]]);
  const entity2 = world.addEntity([[Position, { x: 3, y: 4 }]]);
  const entity3 = world.addEntity([[Position, { x: 5, y: 6 }]]);

  const query = new Query({ pos: Position });
  query.forEach(world, (_entityId, _data) => {
    world.addComponent(entity1, Velocity, { x: 10, y: 20 });
    world.addComponent(entity2, Color, { x: 255, y: 0, z: 0 });
    world.addComponent(entity3, Velocity, { x: 30, y: 40 });
    world.addComponent(entity3, Color, { x: 0, y: 255, z: 0 });

    expect(world.hasComponent(entity1, Velocity)).toBe(true);
    expect(world.hasComponent(entity1, Color)).toBe(false);
    expect(world.hasComponent(entity2, Velocity)).toBe(false);
    expect(world.hasComponent(entity2, Color)).toBe(true);
    expect(world.hasComponent(entity3, Velocity)).toBe(true);
    expect(world.hasComponent(entity3, Color)).toBe(true);
  });

  expect(world.hasComponent(entity1, Velocity)).toBe(true);
  expect(world.hasComponent(entity2, Color)).toBe(true);
  expect(world.hasComponent(entity3, Velocity)).toBe(true);
  expect(world.hasComponent(entity3, Color)).toBe(true);
});

test("EntityData utility methods with pending components", () => {
  const world = new World();
  const entity = world.addEntity([[Position, { x: 1, y: 2 }]]);
  const entityData = world.entityData(entity)!;

  const query = new Query({ pos: Position });
  query.forEach(world, (_entityId, _data) => {
    entityData.addComponent(Velocity, { x: 5, y: 10 });

    // Test toBuilder includes pending components
    const builder = entityData.toBuilder();
    const newEntity = builder.spawn(world);
    expect(newEntity.hasComponent(Position)).toBe(true);
    expect(newEntity.hasComponent(Velocity)).toBe(true);
    expect(newEntity.getComponent(Position)).toEqual({ x: 1, y: 2 });
    expect(newEntity.getComponent(Velocity)).toEqual({ x: 5, y: 10 });

    // Test display includes pending components
    const display = entityData.display();
    expect(display).toContain("position");
    expect(display).toContain("velocity");
  });
});

test("spawning entity and adding components during locked section", () => {
  const world = new World();
  const entity = world.addEntity([[Position, { x: 1, y: 2 }]]);

  const query = new Query({ pos: Position });
  query.forEach(world, (_entityId, _data) => {
    // Spawn new entity during locked section
    const newEntity = world.addEntity([[Position, { x: 10, y: 20 }]]);
    const newEntityData = world.entityData(newEntity)!;

    // Add component to the newly spawned entity
    newEntityData.addComponent(Velocity, { x: 5, y: 15 });

    // Test that spawned entity is deferred but components are accessible via fallback
    expect(world.isAlive(newEntity)).toBe(true);
    expect(newEntityData.isDeferred()).toBe(true);
    expect(newEntityData.hasComponent(Position)).toBe(true); // Accessible via pending
    expect(newEntityData.hasComponent(Velocity)).toBe(true); // Accessible via pending
    expect(world.hasComponent(newEntity, Velocity)).toBe(true);
    expect(newEntityData.getComponent(Velocity)).toEqual({ x: 5, y: 15 });
  });

  // After lock release, everything should be properly applied
  const allEntities = Array.from(world.entities);
  expect(allEntities).toHaveLength(2);

  const newEntity = allEntities.find((e) => e !== entity)!;
  const newEntityData = world.entityData(newEntity)!;
  expect(newEntityData.isDeferred()).toBe(false);
  expect(world.hasComponent(newEntity, Position)).toBe(true);
  expect(world.hasComponent(newEntity, Velocity)).toBe(true);
  expect(world.getComponent(newEntity, Velocity)).toEqual({ x: 5, y: 15 });
});

test("overwriting existing component during locked section", () => {
  const world = new World();
  const entity = world.addEntity([[Position, { x: 1, y: 2 }]]);

  // Add component outside query (normal operation)
  world.addComponent(entity, Velocity, { x: 5, y: 10 });
  expect(world.getComponent(entity, Velocity)).toEqual({ x: 5, y: 10 });

  const query = new Query({ pos: Position });
  query.forEach(world, (_entityId, _data) => {
    expect(world.hasComponent(entity, Velocity)).toBe(true);
    // Try to add the same component again during locked section
    world.addComponent(entity, Velocity, { x: 15, y: 25 });

    expect(world.hasComponent(entity, Velocity)).toBe(true);
    expect(world.getComponent(entity, Velocity)).toEqual({ x: 15, y: 25 });
  });

  // Outside query: modified value should be applied
  expect(world.hasComponent(entity, Velocity)).toBe(true);
  expect(world.getComponent(entity, Velocity)).toEqual({ x: 15, y: 25 });
});

test("nested queries with deferred operations", () => {
  const world = new World();
  const entity1 = world.addEntity([[Position, { x: 1, y: 2 }]]);
  const entity2 = world.addEntity([[Position, { x: 3, y: 4 }]]);

  const outerQuery = new Query({ pos: Position });
  const innerQuery = new Query({ pos: Position });

  outerQuery.forEach(world, (_entityId, _data) => {
    world.addComponent(entity1, Velocity, { x: 10, y: 20 });

    innerQuery.forEach(world, (_innerEntityId, _innerData) => {
      world.addComponent(entity2, Color, { x: 255, y: 0, z: 0 });

      expect(world.hasComponent(entity1, Velocity)).toBe(true);
      expect(world.hasComponent(entity2, Color)).toBe(true);
    });
  });

  expect(world.hasComponent(entity1, Velocity)).toBe(true);
  expect(world.hasComponent(entity2, Color)).toBe(true);
});
