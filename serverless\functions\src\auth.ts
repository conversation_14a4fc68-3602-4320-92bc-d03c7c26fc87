import { DbCollections } from "@nilo/firebase-schema";
import { HttpsError } from "firebase-functions/v2/https";
import { getAuth } from "firebase-admin/auth";
import { Logger } from "./logger";
import {
  beforeUserCreated,
  AuthBlockingEvent,
  isInternalEmail,
  beforeUserSignedIn,
} from "./functions/utils";
import { checkUserInWhitelisted } from "./functions/whitelist";
import { isEmulator } from "./utils";

/**
 * Triggered before a user is created in Firebase Auth.
 * This is used to check if the user is whitelisted.
 */
export const beforeUserAccountCreated = beforeUserCreated(
  {
    secrets: ["AIRTABLE_API_KEY", "AIRTABLE_BASE_ID", "AIRTABLE_TABLE_ID"],
  },
  async (event: AuthBlockingEvent) => {
    const user = event.data;

    if (!user) {
      throw new HttpsError(
        "invalid-argument",
        "User data is not available in the event"
      );
    }

    const logger = Logger.create({ userId: user.uid });

    logger.info("User account creation initiated", {
      userId: user.uid,
      email: user.email,
      emailVerified: user.emailVerified,
      providerData: user.providerData?.map((p) => p.providerId) || [],
    });

    // if emulator, skip whitelist check
    if (isEmulator) {
      logger.info("Emulator detected, skipping whitelist check", {
        userId: user.uid,
        email: user.email,
      });
      return;
    }

    if (isInternalEmail(user.email)) {
      logger.info("User is internal", {
        userId: user.uid,
        email: user.email,
      });
      return;
    }

    if (!user.email) {
      logger.warn("No email provided for whitelist check", {
        userId: user.uid,
      });
      throw new HttpsError(
        "invalid-argument",
        "Email is required for account creation"
      );
    }

    let isWhitelisted = false;

    isWhitelisted = await checkUserInWhitelisted(user.email, logger, {
      userId: user.uid,
    });

    if (!isWhitelisted) {
      logger.warn(
        `User not whitelisted in AirTable or ${DbCollections.whitelistedEmails} collection, blocking account creation`,
        {
          userId: user.uid,
          email: user.email,
        }
      );
      throw new HttpsError(
        "permission-denied",
        "Your email is not whitelisted for account creation.",
        { isWhitelisted }
      );
    }

    logger.info("User account creation approved", {
      userId: user.uid,
    });
  }
);

/**
 * Triggered before a user is signed in to set admin claim.
 */
export const beforeSignedIn = beforeUserSignedIn(
  {},
  async (event: AuthBlockingEvent) => {
    const user = event.data;
    if (!user) {
      throw new HttpsError(
        "invalid-argument",
        "User data is not available in the event"
      );
    }
    const logger = Logger.create({ userId: user.uid });
    const auth = getAuth();

    if (isEmulator) {
      // if on emulator then if email or displayName contains "admin" -> mark as admin
      const admin =
        [user.email, user.displayName].some((data) =>
          data?.toLowerCase().includes("admin")
        ) || isInternalEmail(user.email);
      logger.info("Sign in on emulator detected", {
        userId: user.uid,
        email: user.email,
        admin,
      });
      if (admin) {
        try {
          await auth.setCustomUserClaims(user.uid, { admin });
        } catch (error) {
          logger.error("Error setting admin claim on emulator", {
            userId: user.uid,
            email: user.email,
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }
      return;
    }

    if (isInternalEmail(user.email)) {
      logger.info("Internal user signed in", {
        userId: user.uid,
        email: user.email,
      });
      try {
        await auth.setCustomUserClaims(user.uid, { admin: true });
      } catch (error) {
        logger.error("Error setting admin claim for internal user", {
          userId: user.uid,
          email: user.email,
          error: error instanceof Error ? error.message : String(error),
        });
      }
      return;
    }

    logger.info("Regular user signed in", {
      userId: user.uid,
      email: user.email,
      emailVerified: user.emailVerified,
      providerData: user.providerData?.map((p) => p.providerId) || [],
    });
  }
);
