import {
  DbCollections,
  DbInternalDataJob,
  InternalListDatabasesRequest,
  InternalListDatabasesResponse,
} from "@nilo/firebase-schema";
import { FieldValue } from "firebase-admin/firestore";
import { EventHandlerOptions } from "firebase-functions";
import { onCall, onDocumentCreated, onDocumentUpdated } from "../utils";
import { collection, DocumentReferenceT } from "../../typedFirebase";
import { Logger } from "../../logger";
import { createDataJob } from "./jobFactories";
import { listFirestoreDatabases } from "./dbs";

export const internalListDatabases = onCall(
  InternalListDatabasesRequest,
  InternalListDatabasesResponse,
  { auth: "admin" },
  async () => {
    return { databases: await listFirestoreDatabases() };
  }
);

const jobHandlerOptions: EventHandlerOptions = {
  minInstances: 0,
  concurrency: 10,
  memory: "2GiB",
};

export const onDataJobCreated = onDocumentCreated(
  `${DbCollections.internalDataJobs}/{jobId}`,
  jobHandlerOptions,
  async (event) => {
    const logger = Logger.create({ jobId: event.params.jobId });
    if (!event.data) {
      logger.warn("❌ No data found");
      return;
    }
    const jobRef = DocumentReferenceT.wrap<DbInternalDataJob>(event.data.ref);
    return await onJobCreated({
      logger,
      jobRef,
    });
  }
);

export const onDataSubJobCreated = onDocumentCreated(
  `${DbCollections.internalDataJobs}/{parentJobId}/${DbCollections.internalSubJobs}/{jobId}`,
  jobHandlerOptions,
  async (event) => {
    const logger = Logger.create({
      jobId: event.params.jobId,
      parentJobId: event.params.parentJobId,
    });
    if (!event.data) {
      logger.warn("❌ No data found");
      return;
    }
    const parentJobRef = collection<DbInternalDataJob>(
      DbCollections.internalDataJobs
    ).doc(event.params.parentJobId);
    return await onJobCreated({
      logger,
      jobRef: DocumentReferenceT.wrap<DbInternalDataJob>(event.data.ref),
      parentJobRef,
    });
  }
);

async function onJobCreated({
  logger,
  jobRef,
  parentJobRef,
}: {
  logger: Logger;
  jobRef: DocumentReferenceT<DbInternalDataJob>;
  parentJobRef?: DocumentReferenceT<DbInternalDataJob>;
}) {
  const jobData = await jobRef.data();
  if (!jobData?.config) {
    logger.warn("❌ No config found");
    return;
  }

  let job: ReturnType<typeof createDataJob> | undefined;
  try {
    job = createDataJob(logger, jobRef, jobData.config);
    await jobRef.update({
      updatedAt: FieldValue.serverTimestamp(),
      status: "processing",
    });
    await job.run();
    await job.emitLog("info", "Job completed successfully");
    if (!job.hasSubJobs) {
      await jobRef.update({
        updatedAt: FieldValue.serverTimestamp(),
        status: "completed",
      });
    }
    await parentJobRef?.update({
      updatedAt: FieldValue.serverTimestamp(),
      completedSubJobs: FieldValue.increment(1),
    });
  } catch (error) {
    logger.error("❌ Error running job", error);
    await Promise.all([
      job?.emitLog("error", `Error running job: ${error}`),
      jobRef.update({
        updatedAt: FieldValue.serverTimestamp(),
        status: "failed",
      }),
      parentJobRef?.update({
        updatedAt: FieldValue.serverTimestamp(),
        failedSubJobs: FieldValue.increment(1),
      }),
    ]);
  }
}

export const onDataJobUpdated = onDocumentUpdated(
  `${DbCollections.internalDataJobs}/{jobId}`,
  async (event) => {
    const logger = Logger.create({ jobId: event.params.jobId });
    if (!event.data) {
      logger.warn("❌ No data found");
      return;
    }
    const jobRef = DocumentReferenceT.wrap<DbInternalDataJob>(
      event.data.after.ref
    );
    const jobData = await jobRef.data();
    if (!jobData) {
      logger.warn("❌ No data found");
      return;
    }

    if (jobData.status === "completed" || jobData.status === "failed") {
      // nothing to do
      return;
    }
    if ((jobData.subJobs ?? 0) === 0) {
      // no sub jobs to account for
      return;
    }

    const finishedSubJobs =
      (jobData.failedSubJobs ?? 0) + (jobData.completedSubJobs ?? 0);
    if (jobData.subJobs === finishedSubJobs) {
      // all sub jobs finished
      await jobRef.update({
        updatedAt: FieldValue.serverTimestamp(),
        // if any sub job failed, the job failed
        status: (jobData.failedSubJobs ?? 0) > 0 ? "failed" : "completed",
      });
    }
  }
);
