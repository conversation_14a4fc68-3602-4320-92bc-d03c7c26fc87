import { useEffect, useState } from "react";
import { useAuthState } from "react-firebase-hooks/auth";
import { IdTokenResult, signOut } from "firebase/auth";
import {
  User,
  Mail,
  Shield,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  Info,
  RefreshCcwIcon,
} from "lucide-react";
import { auth } from "@/config/firebase";
import { SignOutIcon } from "@/icons/SignOut";
import { Typography } from "@/components/ui-nilo/Typography";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

export default function UserInfo() {
  const [user] = useAuthState(auth);
  const [token, setToken] = useState<IdTokenResult | undefined>(undefined);

  useEffect(() => {
    if (user) {
      user.getIdTokenResult().then(setToken);
    } else {
      setToken(undefined);
    }
  }, [user]);

  const handleLogout = async () => {
    try {
      await signOut(auth);
      console.debug("🚪 User logged out successfully");
    } catch (error) {
      console.error("❌ Error logging out:", error);
    }
  };

  const handleRefreshToken = async () => {
    try {
      const token = await user?.getIdTokenResult(true);
      setToken(token);
    } catch (error) {
      console.error("❌ Error refreshing token:", error);
    }
  };

  const formatDate = (timestamp: string | { seconds: number } | undefined) => {
    if (!timestamp) return "N/A";
    if (typeof timestamp === "string") {
      return new Date(timestamp).toLocaleString();
    }
    return new Date(timestamp.seconds * 1000).toLocaleString();
  };

  const getStatusBadge = (verified: boolean) => {
    return verified ? (
      <Badge variant="default" className="bg-green-600 hover:bg-green-700">
        <CheckCircle className="w-3 h-3 mr-1" />
        Verified
      </Badge>
    ) : (
      <Badge variant="destructive">
        <XCircle className="w-3 h-3 mr-1" />
        Unverified
      </Badge>
    );
  };

  return (
    <div className="h-screen bg-black text-icon-primary overflow-y-auto scrollbar-thin-nilo">
      <div className="mx-auto space-y-12 pt-8 pb-24 max-w-6xl">
        {/* Header */}
        <header className="text-center">
          <Typography.Title level={1} color="light" className="mb-4">
            User Information
          </Typography.Title>
          <Typography.Body
            level={1}
            color="light"
            weight="medium"
            className="mb-6"
          >
            User details and authentication status for debugging
          </Typography.Body>
          {user && (
            <div className="flex flex-row gap-2 justify-center">
              <Button
                onClick={handleLogout}
                variant="destructive"
                size="default"
              >
                <SignOutIcon />
                Logout
              </Button>
              <Button
                onClick={handleRefreshToken}
                variant="secondary"
                size="default"
              >
                <RefreshCcwIcon />
                Refresh Token
              </Button>
            </div>
          )}
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* User Profile Card */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                User Profile
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                {user?.photoURL ? (
                  <img
                    src={user.photoURL}
                    alt="Profile"
                    className="w-16 h-16 rounded-full border-2 border-border"
                  />
                ) : (
                  <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center">
                    <User className="w-8 h-8 text-muted-foreground" />
                  </div>
                )}
                <div>
                  <Typography.Heading level={2} color="light" className="mb-1">
                    {user?.displayName || "No display name"}
                  </Typography.Heading>
                  <Typography.Body level={2} color="light">
                    {user?.email || "No email"}
                  </Typography.Body>
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <Typography.Label level={2} color="light" weight="medium">
                    User ID
                  </Typography.Label>
                  <Typography.Body
                    level={3}
                    color="light"
                    className="font-mono text-xs"
                  >
                    {user?.uid || "N/A"}
                  </Typography.Body>
                </div>

                <div className="flex justify-between items-center">
                  <Typography.Label level={2} color="light" weight="medium">
                    Email Status
                  </Typography.Label>
                  {getStatusBadge(user?.emailVerified || false)}
                </div>

                <div className="flex justify-between items-center">
                  <Typography.Label level={2} color="light" weight="medium">
                    Account Type
                  </Typography.Label>
                  <Badge variant={user?.isAnonymous ? "secondary" : "default"}>
                    {user?.isAnonymous ? "Anonymous" : "Authenticated"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Authentication Details Card */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Authentication Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <Typography.Label level={2} color="light" weight="medium">
                    <Mail className="w-4 h-4 inline mr-2" />
                    Email Verified
                  </Typography.Label>
                  {getStatusBadge(user?.emailVerified || false)}
                </div>

                <div className="flex justify-between items-center">
                  <Typography.Label level={2} color="light" weight="medium">
                    <User className="w-4 h-4 inline mr-2" />
                    Anonymous User
                  </Typography.Label>
                  <Badge
                    variant={user?.isAnonymous ? "destructive" : "default"}
                  >
                    {user?.isAnonymous ? "Yes" : "No"}
                  </Badge>
                </div>

                {user?.metadata && (
                  <>
                    <Separator />
                    <div className="space-y-2">
                      <Typography.Label level={2} color="light" weight="medium">
                        <Calendar className="w-4 h-4 inline mr-2" />
                        Account Created
                      </Typography.Label>
                      <Typography.Body
                        level={3}
                        color="light"
                        className="font-mono text-sm"
                      >
                        {formatDate(user.metadata.creationTime)}
                      </Typography.Body>
                    </div>

                    <div className="space-y-2">
                      <Typography.Label level={2} color="light" weight="medium">
                        <Clock className="w-4 h-4 inline mr-2" />
                        Last Sign In
                      </Typography.Label>
                      <Typography.Body
                        level={3}
                        color="light"
                        className="font-mono text-sm"
                      >
                        {formatDate(user.metadata.lastSignInTime)}
                      </Typography.Body>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Raw Data Card */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="w-5 h-5" />
              Raw User Data
            </CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted text-muted-foreground p-4 rounded-lg overflow-x-auto text-sm font-mono leading-relaxed border">
              {JSON.stringify(
                {
                  user: {
                    id: user?.uid,
                    email: user?.email,
                    displayName: user?.displayName,
                    photoURL: user?.photoURL,
                    emailVerified: user?.emailVerified,
                    isAnonymous: user?.isAnonymous,
                    metadata: user?.metadata,
                  },
                },
                null,
                2
              )}
            </pre>
          </CardContent>
        </Card>

        {/* Raw Token Data Card */}
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="w-5 h-5" />
              Raw Token Data
            </CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted text-muted-foreground p-4 rounded-lg overflow-x-auto text-sm font-mono leading-relaxed border">
              {token ? JSON.stringify(token, null, 2) : "Loading..."}
            </pre>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
