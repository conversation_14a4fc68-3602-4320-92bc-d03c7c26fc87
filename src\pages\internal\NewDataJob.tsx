import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  addDoc,
  collection,
  CollectionReference,
  serverTimestamp,
} from "firebase/firestore";
import { useAuthState } from "react-firebase-hooks/auth";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Typography } from "@/components/ui-nilo/Typography";
import {
  DbInternalDataJob,
  DbInternalDataJobMigrateScript,
  InternalListDatabasesRequest,
  InternalListDatabasesResponse,
  DbCollections,
} from "@nilo/firebase-schema";
import { bindFunction, useEmulator, db, auth } from "@/config/firebase";
import { isolatedEnvironment } from "@/config/isolatedEnv";

const internalListDatabases = bindFunction<
  InternalListDatabasesRequest,
  InternalListDatabasesResponse
>("internalListDatabases");

export default function NewDataJob() {
  const navigate = useNavigate();
  const [user] = useAuthState(auth);
  const userId = user?.uid;

  const [databases, setDatabases] = useState<string[]>(["(default)", "prod"]);

  useEffect(() => {
    if (useEmulator) {
      setDatabases(["(default)"]);
      return;
    }
    (async () => {
      const result = await internalListDatabases({});
      if (result.data) {
        // organize databases into: "main" (default), "prod", releases and others
        const releaseDbs = [];
        const otherDbs = [];
        for (const db of result.data.databases) {
          if (
            db === null ||
            db === undefined ||
            db === "(default)" ||
            db === "prod"
          ) {
            continue;
          } else if (db?.startsWith("release-")) {
            releaseDbs.push(db);
          } else {
            otherDbs.push(db);
          }
        }
        releaseDbs.sort();
        otherDbs.sort();
        setDatabases(["(default)", "prod", ...releaseDbs, ...otherDbs]);
      }
    })();
  }, []);

  const [cloneWorldForm, setCloneWorldForm] = useState({
    worldId: "",
    fromDatabase: "(default)",
    overwrite: false,
  });

  const [cloneAllWorldsForm, setCloneAllWorldsForm] = useState({
    fromDatabase: "(default)",
    overwrite: false,
    skipOwnedByAnonymousUsers: true,
  });

  const [cloneAllUsersForm, setCloneAllUsersForm] = useState({
    fromDatabase: "(default)",
    skipAnonymousUsers: true,
  });

  const [migrationForm, setMigrationForm] = useState<{
    script: DbInternalDataJobMigrateScript;
  }>({
    script: "nuke_worlds_tasks_assets",
  });

  const handleNewJobSubmit = async (config: DbInternalDataJob["config"]) => {
    if (!userId) {
      console.error("User not authenticated");
      return;
    }

    const jobsCollection = collection(
      db,
      DbCollections.internalDataJobs
    ) as CollectionReference<DbInternalDataJob, DbInternalDataJob>;

    const docRef = await addDoc(jobsCollection, {
      config,
      status: "pending",
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      userId,
    });

    // Navigate to the created job page
    navigate(`/internal/dataJobs/${docRef.id}`);
  };

  const handleCloneWorldSubmit = () => {
    handleNewJobSubmit({
      type: "clone_worlds",
      worldIds: [cloneWorldForm.worldId.trim()],
      fromDatabase: cloneWorldForm.fromDatabase,
      overwrite: cloneWorldForm.overwrite,
    });

    // Reset form
    setCloneWorldForm({
      worldId: "",
      fromDatabase: "(default)",
      overwrite: false,
    });
  };

  const handleCloneAllWorldsSubmit = () => {
    handleNewJobSubmit({
      type: "clone_all_worlds",
      fromDatabase: cloneAllWorldsForm.fromDatabase,
      overwrite: cloneAllWorldsForm.overwrite,
      skipOwnedByAnonymousUsers: cloneAllWorldsForm.skipOwnedByAnonymousUsers,
    });

    // Reset form
    setCloneAllWorldsForm({
      fromDatabase: "(default)",
      overwrite: false,
      skipOwnedByAnonymousUsers: true,
    });
  };

  const handleCloneAllUsersSubmit = () => {
    handleNewJobSubmit({
      type: "clone_all_users",
      fromDatabase: cloneAllUsersForm.fromDatabase,
      skipAnonymousUsers: cloneAllUsersForm.skipAnonymousUsers,
    });

    // Reset form
    setCloneAllUsersForm({
      fromDatabase: "(default)",
      skipAnonymousUsers: true,
    });
  };

  const handleMigrationSubmit = () => {
    // Random phrase to prevent accidental migration - especially for prod and main
    let phrase;
    let env;
    if (useEmulator) {
      env = "emulator";
      phrase = "ok";
    } else if (
      isolatedEnvironment.name === "main" ||
      isolatedEnvironment.name === "prod"
    ) {
      env = isolatedEnvironment.name;
      phrase = `${env}-${Math.random().toString(36).substring(2, 8)}`;
    } else if (isolatedEnvironment.name.startsWith("release-")) {
      env = "release";
      phrase = `${env}-${Math.random().toString(36).substring(2, 8)}`;
    } else {
      env = isolatedEnvironment.name;
      phrase = Math.random().toString(36).substring(2, 6);
    }
    if (
      prompt(
        `Enter the secure phrase to confirm migration in ${env}: ${phrase}`
      ) !== phrase
    ) {
      return;
    }

    handleNewJobSubmit({
      type: "migrate",
      script: migrationForm.script,
    });

    // Reset form
    setMigrationForm({
      script: "nuke_worlds_tasks_assets",
    });
  };

  const handleCancel = () => {
    // Navigate back to jobs list
    navigate("/internal/dataJobs");
  };

  return (
    <div className="h-screen bg-black text-icon-primary overflow-y-auto">
      <div className="mx-auto space-y-6 pt-8 pb-24 max-w-7xl px-4">
        {/* Header */}
        <header className="flex justify-between items-center">
          <div>
            <Typography.Title level={1} color="light" className="mb-2">
              Schedule New Job
            </Typography.Title>
            <Typography.Body level={1} color="light" weight="medium">
              Create a new data job in {isolatedEnvironment.name}
            </Typography.Body>
          </div>
          <Button variant="outline" onClick={handleCancel}>
            Back to Jobs
          </Button>
        </header>

        <Tabs defaultValue="clone_all_worlds" className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-surface-secondary">
            <TabsTrigger value="clone_all_worlds">Clone All Worlds</TabsTrigger>
            <TabsTrigger value="clone_world">Clone World</TabsTrigger>
            <TabsTrigger value="clone_all_users">Clone All Users</TabsTrigger>
            <TabsTrigger value="migrate">Migration</TabsTrigger>
          </TabsList>

          <TabsContent value="clone_world" className="space-y-4">
            <Card className="bg-surface-secondary border-accessories-divider">
              <CardHeader>
                <CardTitle>
                  <Typography.Heading level={1} color="light" className="mb-2">
                    Configuration
                  </Typography.Heading>
                  <Typography.Body level={3} color="light">
                    This will clone the selected world from the selected
                    database into this ISE ({isolatedEnvironment.name})
                  </Typography.Body>
                  {cloneWorldForm.fromDatabase !== "(default)" &&
                    cloneWorldForm.fromDatabase !== "prod" && (
                      <Typography.Body
                        level={3}
                        color="light"
                        className="text-nilo-danger-500"
                      >
                        Worlds cloned from an ISE database might stop working
                        once ISE is deleted.
                      </Typography.Body>
                    )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="worldId">World ID</Label>
                  <Input
                    id="worldId"
                    value={cloneWorldForm.worldId}
                    onChange={(e) =>
                      setCloneWorldForm((prev) => ({
                        ...prev,
                        worldId: e.target.value,
                      }))
                    }
                    placeholder="Enter world ID to clone"
                    className="bg-surface-primary border-accessories-divider"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fromDatabase">From Database</Label>
                  <Select
                    value={cloneWorldForm.fromDatabase}
                    onValueChange={(value: string) =>
                      setCloneWorldForm((prev) => ({
                        ...prev,
                        fromDatabase: value,
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Database" />
                    </SelectTrigger>
                    <SelectContent>
                      {databases.map((db) => (
                        <SelectItem key={db} value={db}>
                          {db}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="cloneWorldOverwrite"
                    checked={cloneWorldForm.overwrite}
                    onCheckedChange={(checked: boolean) =>
                      setCloneWorldForm((prev) => ({
                        ...prev,
                        overwrite: checked,
                      }))
                    }
                  />
                  <Label htmlFor="cloneWorldOverwrite">
                    Overwrite existing worlds
                  </Label>
                </div>

                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={handleCloneWorldSubmit}
                    disabled={!cloneWorldForm.worldId.trim()}
                    className="flex-1"
                  >
                    Schedule Clone World Job
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="clone_all_worlds" className="space-y-4">
            <Card className="bg-surface-secondary border-accessories-divider">
              <CardHeader>
                <CardTitle>
                  <Typography.Heading level={1} color="light" className="mb-2">
                    Configuration
                  </Typography.Heading>
                  <Typography.Body level={3} color="light">
                    This will clone all worlds from the selected database into
                    this ISE ({isolatedEnvironment.name})
                  </Typography.Body>
                  {cloneAllWorldsForm.fromDatabase !== "(default)" &&
                    cloneAllWorldsForm.fromDatabase !== "prod" && (
                      <Typography.Body
                        level={3}
                        color="light"
                        className="text-nilo-danger-500"
                      >
                        Worlds cloned from an ISE database might stop working
                        once ISE is deleted.
                      </Typography.Body>
                    )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="allWorldsFromDatabase">From Database</Label>
                  <Select
                    value={cloneAllWorldsForm.fromDatabase}
                    onValueChange={(value: string) =>
                      setCloneAllWorldsForm((prev) => ({
                        ...prev,
                        fromDatabase: value,
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Database" />
                    </SelectTrigger>
                    <SelectContent>
                      {databases.map((db) => (
                        <SelectItem key={db} value={db}>
                          {db}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="cloneAllWorldsOverwrite"
                    checked={cloneAllWorldsForm.overwrite}
                    onCheckedChange={(checked: boolean) =>
                      setCloneAllWorldsForm((prev) => ({
                        ...prev,
                        overwrite: checked,
                      }))
                    }
                  />
                  <Label htmlFor="cloneAllWorldsOverwrite">
                    Overwrite existing worlds
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="cloneAllWorldsSkipAnonymous"
                    checked={cloneAllWorldsForm.skipOwnedByAnonymousUsers}
                    onCheckedChange={(checked: boolean) =>
                      setCloneAllWorldsForm((prev) => ({
                        ...prev,
                        skipOwnedByAnonymousUsers: checked,
                      }))
                    }
                  />
                  <Label htmlFor="cloneAllWorldsSkipAnonymous">
                    Skip worlds owned by anonymous users
                  </Label>
                </div>

                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={handleCloneAllWorldsSubmit}
                    className="flex-1"
                  >
                    Schedule Clone All Worlds Job
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="clone_all_users" className="space-y-4">
            <Card className="bg-surface-secondary border-accessories-divider">
              <CardHeader>
                <CardTitle>
                  <Typography.Heading level={1} color="light" className="mb-2">
                    Configuration
                  </Typography.Heading>
                  <Typography.Body level={3} color="light">
                    This will clone all users from the selected database into
                    this ISE ({isolatedEnvironment.name})
                  </Typography.Body>
                  {cloneAllUsersForm.fromDatabase !== "(default)" &&
                    cloneAllUsersForm.fromDatabase !== "prod" && (
                      <Typography.Body
                        level={3}
                        color="light"
                        className="text-nilo-danger-500"
                      >
                        Users cloned from an ISE database might stop working
                        once ISE is deleted.
                      </Typography.Body>
                    )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="allUsersFromDatabase">From Database</Label>
                  <Select
                    value={cloneAllUsersForm.fromDatabase}
                    onValueChange={(value: string) =>
                      setCloneAllUsersForm((prev) => ({
                        ...prev,
                        fromDatabase: value,
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Database" />
                    </SelectTrigger>
                    <SelectContent>
                      {databases.map((db) => (
                        <SelectItem key={db} value={db}>
                          {db}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="cloneAllUsersSkipAnonymous"
                    checked={cloneAllUsersForm.skipAnonymousUsers}
                    onCheckedChange={(checked: boolean) =>
                      setCloneAllUsersForm((prev) => ({
                        ...prev,
                        skipAnonymousUsers: checked,
                      }))
                    }
                  />
                  <Label htmlFor="cloneAllUsersSkipAnonymous">
                    Skip anonymous users
                  </Label>
                </div>

                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={handleCloneAllUsersSubmit}
                    className="flex-1"
                  >
                    Schedule Clone All Users Job
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="migrate" className="space-y-4">
            <Card className="bg-surface-secondary border-accessories-divider">
              <CardHeader>
                <CardTitle>
                  <Typography.Heading level={1} color="light" className="mb-2">
                    Configuration
                  </Typography.Heading>
                  <Typography.Body level={3} color="light">
                    This will run the selected migration script in this ISE (
                    {isolatedEnvironment.name})
                  </Typography.Body>
                  <Typography.Body
                    level={3}
                    color="light"
                    className="text-nilo-danger-500"
                  >
                    ⚠️ Migration scripts can permanently delete data. Use with
                    caution.
                  </Typography.Body>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="migrationScript">Migration Script</Label>
                  <Select
                    value={migrationForm.script}
                    onValueChange={(value: DbInternalDataJobMigrateScript) =>
                      setMigrationForm((prev) => ({
                        ...prev,
                        script: value,
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Migration Script" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="nuke_worlds_tasks_assets">
                        Nuke Worlds, Tasks & Assets
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex gap-3 pt-4">
                  <Button onClick={handleMigrationSubmit} className="flex-1">
                    Schedule Migration Job
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
