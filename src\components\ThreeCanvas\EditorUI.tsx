import { AnimatePresence, motion, MotionProps } from "framer-motion";
import { ChevronLeftIcon } from "lucide-react";
import { useEffect, useState, type ReactNode } from "react";

import ThreejsSceneHierarchyView from "@/components/debugging/ThreejsSceneHierarchyView";
import { FeedbackModal } from "@/components/editor/help/FeedbackModal";
import { KeyboardShortcutsModal } from "@/components/editor/help/KeyboardShortcutsModal";
import { DevSettingsPanel } from "@/components/modals/DevSettingsPanel";
import { EntitiesPanel } from "@/components/modals/EntitiesPanel";
import { EnvironmentSettingsPanel } from "@/components/modals/EnvironmentSettingsPanel";
import { InspectorPanel } from "@/components/modals/InspectorPanel";
import { EditorSidebar } from "@/components/ThreeCanvas/EditorSidebar";
import { WorldSettings as LegacyWorldSettings } from "@/components/WorldSettings";
import {
  useEditorUIActions,
  useEditorUIState,
} from "@/contexts/EditorUIContext";
import { Client } from "@/core/client";
import { useMySelectedEntities } from "@/hooks/useMySelectedEntities";
import { useFeatureFlag } from "@/hooks/useFeatureFlags";

export function EditorUI() {
  const appIsReady = useIsAppReady();
  const { selectedEntities } = useMySelectedEntities();
  const isMouseInLeftArea = useSpecialMouseEnterLeaveArea();
  const builderUiSidebarEnabled = useFeatureFlag("builderUiSidebar");
  const builderUiInspectorEnabled = useFeatureFlag("builderUiInspector");

  const {
    isEnvironmentSettingsOpen,
    isDevSettingsOpen,
    isLegacyWorldSettingsOpen,
    isSceneHierarchyOpen,
    isEntitiesOpen,
    isInspectorOpen,
    isInspectorHidden,
    currentLeftPanel,
    isLeftPanelPinned,
  } = useEditorUIState();

  const {
    toggleLeftPanel,
    toggleInspectorPanel,
    toggleInspectorHidden,
    toggleLeftPanelPinned,
    setCurrentLeftPanel,
  } = useEditorUIActions();

  // Auto-show inspector when entities are selected, auto-hide when none selected
  useEffect(() => {
    // Only show inspector if feature flag is enabled
    if (!builderUiInspectorEnabled) {
      return;
    }

    if (selectedEntities.length > 0 && !isInspectorOpen) {
      toggleInspectorPanel(true);
    } else if (selectedEntities.length === 0 && isInspectorOpen) {
      toggleInspectorPanel(false);
    }
  }, [
    selectedEntities.length,
    isInspectorOpen,
    toggleInspectorPanel,
    builderUiInspectorEnabled,
  ]);

  // Auto-close left panel when mouse leaves left area (if not pinned)
  useEffect(() => {
    if (!isMouseInLeftArea && currentLeftPanel && !isLeftPanelPinned) {
      setCurrentLeftPanel(null);
    }
  }, [
    isMouseInLeftArea,
    currentLeftPanel,
    isLeftPanelPinned,
    setCurrentLeftPanel,
  ]);

  if (!appIsReady) {
    return null;
  }

  const leftPanelSlideDistance = 0;
  const rightPanelSlideDistance = 4;

  return (
    <>
      {builderUiSidebarEnabled && (
        <EditorSidebar
          currentPanel={currentLeftPanel}
          isCurrentPanelPinned={isLeftPanelPinned}
          toggleLeftPanel={toggleLeftPanel}
          setCurrentPanelPinned={toggleLeftPanelPinned}
        />
      )}

      <div
        id="editor-ui"
        className="fixed inset-[1rem] left-[4rem] top-[4.5rem] select-none pointer-events-none"
      >
        <ScreenDockedPanel
          alignmentHorizontal="left"
          alignmentVertical="top"
          isOpen={isEnvironmentSettingsOpen}
          slideDistance={leftPanelSlideDistance}
        >
          <EnvironmentSettingsPanel />
        </ScreenDockedPanel>

        <ScreenDockedPanel
          alignmentHorizontal="left"
          alignmentVertical="top"
          isOpen={isDevSettingsOpen}
          slideDistance={leftPanelSlideDistance}
        >
          <DevSettingsPanel />
        </ScreenDockedPanel>

        {/* Legacy stuff // Only useful for debugging */}

        <ScreenDockedPanel
          alignmentHorizontal="left"
          alignmentVertical="top"
          isOpen={isLegacyWorldSettingsOpen}
          slideDistance={leftPanelSlideDistance}
        >
          <LegacyWorldSettings />
        </ScreenDockedPanel>

        <ScreenDockedPanel
          alignmentHorizontal="left"
          alignmentVertical="top"
          isOpen={isEntitiesOpen}
          slideDistance={leftPanelSlideDistance}
        >
          <EntitiesPanel />
        </ScreenDockedPanel>

        <ScreenDockedPanel
          alignmentHorizontal="left"
          alignmentVertical="top"
          isOpen={isSceneHierarchyOpen}
          slideDistance={leftPanelSlideDistance}
        >
          <ThreejsSceneHierarchyView />
        </ScreenDockedPanel>

        {/* Legacy stuff [end] */}

        {/* Right side panels */}
        <ScreenDockedPanel
          alignmentHorizontal="right"
          alignmentVertical="top"
          isOpen={
            !isInspectorHidden && isInspectorOpen && builderUiInspectorEnabled
          }
          slideDistance={rightPanelSlideDistance}
        >
          <InspectorPanel selectedEntities={selectedEntities} />
        </ScreenDockedPanel>

        <ScreenDockedPanel
          alignmentHorizontal="right"
          alignmentVertical="center"
          isOpen={
            isInspectorHidden && isInspectorOpen && builderUiInspectorEnabled
          }
          slideDistance={4}
        >
          <button
            onClick={() => toggleInspectorHidden(false)}
            className="flex items-center justify-center w-12 h-12 transition-all duration-200 pointer-events-auto"
            title="Re-enable Inspector"
          >
            <ChevronLeftIcon className="h-8 w-8 text-nilo-text-secondary/60 hover:text-nilo-text-secondary stroke-2" />
          </button>
        </ScreenDockedPanel>

        <KeyboardShortcutsModal />
        <FeedbackModal />
      </div>
    </>
  );
}

function ScreenDockedPanel({
  children,
  alignmentHorizontal,
  alignmentVertical = "center",
  isOpen,
  slideDistance,
}: {
  children: ReactNode;
  alignmentHorizontal: "left" | "right";
  alignmentVertical: "top" | "bottom" | "center";
  isOpen: boolean;
  slideDistance: number;
}) {
  const getHorizontalAnimation = (): MotionProps => {
    if (alignmentHorizontal === "left") {
      return {
        initial: { x: -slideDistance, opacity: 0 },
        exit: { x: -slideDistance, opacity: 0 },
      };
    } else {
      return {
        initial: { x: slideDistance, opacity: 0 },
        exit: { x: slideDistance, opacity: 0 },
      };
    }
  };

  const getVerticalAlignment = () => {
    switch (alignmentVertical) {
      case "top":
        return "justify-start";
      case "bottom":
        return "justify-end";
      case "center":
      default:
        return "justify-center";
    }
  };

  const getHorizontalAlignment = () => {
    return alignmentHorizontal === "left" ? "items-start" : "items-end";
  };

  const animationProps = getHorizontalAnimation();

  return (
    <AnimatePresence mode="sync">
      {isOpen && (
        <motion.div
          {...animationProps}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.2, ease: "easeInOut" }}
          className={`absolute inset-0 flex flex-col ${getVerticalAlignment()} ${getHorizontalAlignment()}`}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

/**
 * Hook that tracks mouse position relative to an invisible HTML element
 * that spans the width of the left panels for accurate mouse tracking.
 *
 * @returns boolean indicating if mouse is over the left panel area
 *
 * //// TODO: Port to tailwind and react, e.g. <SpecialMouseEnterLeaveArea onLeave={...} />
 */
function useSpecialMouseEnterLeaveArea(): boolean {
  const [isInLeftArea, setIsInLeftArea] = useState(false);

  useEffect(() => {
    // Create invisible element for mouse tracking
    const trackingElement = document.createElement("div");
    trackingElement.id = "mouse-tracking-area";
    trackingElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 520px;
      height: 100vh;
      pointer-events: none;
      z-index: 99999;
      opacity: 0.0;
    `;

    document.body.appendChild(trackingElement);

    const handleMouseMove = (event: MouseEvent) => {
      const rect = trackingElement.getBoundingClientRect();
      const isInLeft =
        event.clientX >= rect.left &&
        event.clientX <= rect.right &&
        event.clientY >= rect.top &&
        event.clientY <= rect.bottom;

      setIsInLeftArea(isInLeft);
    };

    // Add event listener
    document.addEventListener("mousemove", handleMouseMove);

    // Cleanup
    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      if (trackingElement.parentNode) {
        trackingElement.parentNode.removeChild(trackingElement);
      }
    };
  }, []);

  return isInLeftArea;
}

function useIsAppReady() {
  const [appIsReady, setAppIsReady] = useState(false);

  useEffect(() => {
    const onInit = () => {
      setAppIsReady(true);
    };

    Client.events.on("initialized", onInit);

    return () => {
      Client.events.off("initialized", onInit);
    };
  }, []);

  return appIsReady;
}
