# This is run for all branches that have their environment set up (determined by the existence of the firestore database)
# It deploys the functions if they have changed

name: Deploy Firebase Functions
on:
  workflow_dispatch: {}
  push:
    paths:
      - .github/workflows/firebase-deploy-functions.yml
      - serverless/functions/**
      - packages/firebase-schema/**

concurrency:
  group: firebase-functions-${{ github.head_ref || github.ref_name }}
  cancel-in-progress: true

jobs:
  deploy_functions:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-gcp-auth
      - uses: ./.github/actions/setup-node
      - uses: ./.github/actions/get-isolated-environment
        id: isolated-env
        with:
          branch: ${{ github.head_ref || github.ref_name }}
          emitEnvCodeTo: serverless/functions/src/isolatedEnv.ts
      - if: steps.isolated-env.outputs.database != '(default)'
        id: check-database
        run: |
          if pnpm exec firebase firestore:databases:get ${{ steps.isolated-env.outputs.database }} &> /dev/null; then
            echo "Database exists"
            echo "exists=true" >> $GITHUB_OUTPUT
          else
            echo "Database does not exist"
            echo "exists=false" >> $GITHUB_OUTPUT
          fi
      - if: steps.isolated-env.outputs.database == '(default)' || steps.check-database.outputs.exists == 'true'
        name: Deploy functions
        uses: ./.github/actions/run-single-command-with-retry
        with:
          command: pnpm exec firebase deploy --non-interactive --force --debug --only functions:${{ steps.isolated-env.outputs.functions }}
