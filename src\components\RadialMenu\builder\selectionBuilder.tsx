/* eslint-disable react/prop-types */
import { proxy, useSnapshot } from "valtio";
import {
  CheckIcon,
  PlayIcon as LucidePlayIcon,
  PauseIcon as LucidePauseIcon,
  BoxesIcon,
  BoxIcon,
  LocateOffIcon,
  LocateIcon,
  LucideBan,
} from "lucide-react";
import { ReactNode } from "react";
import { MathUtils } from "three";
import {
  AnimationsIcon,
  CharacterRunningIcon,
  CodeIcon,
  ControllerIcon,
  DuplicateIcon,
  ExportIcon,
  FlameIcon,
  LockIcon,
  MaterialGlassIcon,
  MaterialIcon,
  MaterialWireframeIcon,
  MoreIcon,
  PaletteIcon,
  PauseIcon,
  PhysicsIcon,
  PivotBottomIcon,
  PivotCenterIcon,
  PivotIcon,
  PivotImportedIcon,
  PlayIcon,
  PrimitiveIcon,
  RagdollIcon,
  ResetIcon,
  SetupIcon,
  StarsIcon,
  TrashCanIcon,
  UnlockIcon,
  VehicleIcon,
} from "../icons";
import { RadialMenu, RadialSection } from "../RadialMenu";
import { radial } from "../modules";
import { ButtonsItem, RadialButtonsItem } from "../modules/ButtonsModule";
import { beginTransaction } from "@/core/command/transaction";
import { runCommands } from "@/core/command";
import {
  EntityOriginTypes,
  GameEntity,
  MeshEntity,
  ParticleEntity,
  PrimitiveEntity,
} from "@/core/entity";

import SetEntityColorCommand from "@/core/command/commands/setEntityColor";
import { cn } from "@/lib/utils";
import {
  MATERIAL_PRESET_NAMES,
  MaterialPresetName,
} from "@/core/util/MaterialUtils";
import {
  materialPresetCommit,
  materialPresetPreview,
  materialPresetRevert,
} from "@/core/commands/SelectedEntitiesMaterialPreset";
import { Client } from "@/core/client";
import { CurrentAction } from "@/core/util/CurrentAction";
import { selectedEntitiesDelete } from "@/core/commands/SelectedEntitiesDelete";
import { entityTransformReset } from "@/core/util/TransformUtils";
import { isEntityWithPhysics } from "@/core/util/setPhysicsStaticFlag";
import SetEntityPhysicsFlagsCommand from "@/core/command/commands/setEntityPhysicsFlags";
import { checkEntityForRig } from "@/core/util/modelGeneration/checkEntityForRig";
import SetEntityOriginTypeCommand from "@/core/command/commands/setEntityOriginType";
import { getFeatureFlag } from "@/utils/feature-flags";
import { addPlayAnimationPanel } from "@/core/util/animations/addPlayAnimationPanel";
import {
  AnimationComponent,
  AnimatorComponent,
  HealthComponent,
  RequestRespawnComponent,
  RespawnPointTag,
  TriggerAreaDamageComponent,
  TriggerComponent,
} from "@/core/components";
import { MeshRagdoll, RagdollState } from "@/core/animator/ragdoll/MeshRagdoll";
import { selectedEntitiesDetailCommit } from "@/core/commands/SelectedEntitiesDetail";
import { TriggerAreaDamageConfig } from "@/types/DealDamageData";
import { DuplicateControlsInstant } from "@/core/util/controls/duplicateControlsInstant/DuplicateControlsInstant";
import { ExportFormat } from "@/core/util/export";
import { SetEntityPhysicsShapeCommand } from "@/core/command/commands";
import UIManager from "@/core/util/UIManager";
import { PanelPositioning } from "@/core/util/UIManager/Panels/options";
import { ParticleEditorPanel } from "@/core/entity/panels/ParticleEditorPanel";
import { canShapeTypeBeDynamic } from "@/physics/helpers/canBeDynamic";
import {
  trackCoreBuilderAction,
  MenuScope,
  ActionKey,
} from "@/utils/tracking/radialMenuUtils";

/**
 * Determine the menu scope based on selected entities
 */
const getMenuScope = (selected: GameEntity[]): MenuScope => {
  const hasRiggedEntity = selected.some(
    (entity) => entity instanceof MeshEntity && checkEntityForRig(entity)
  );

  if (hasRiggedEntity) {
    return "rigged_entity";
  }

  const hasPrimitiveOnly = selected.every(
    (entity) => entity instanceof PrimitiveEntity
  );
  if (hasPrimitiveOnly) {
    return "primitive";
  }

  return "entity";
};

/**
 * Robust mapping from color names to ActionKey with comprehensive validation
 */
function mapColorNameToActionKey(colorName: string): ActionKey | null {
  const colorKeyMap: Record<string, ActionKey> = {
    red: "red",
    orange: "orange",
    yellow: "yellow",
    green: "green",
    cyan: "cyan",
    blue: "blue",
    purple: "purple",
    magenta: "magenta",
    pink: "pink",
    brown: "brown",
    white: "white",
    gray: "gray",
    black: "black",
  };

  const actionKey = colorKeyMap[colorName];

  if (!actionKey) {
    console.error(
      `📊 Tracking: Missing mapping for color "${colorName}". ` +
        `Please add it to colorKeyMap in selectionBuilder.tsx and to ActionKey type in radialMenuUtils.ts`
    );
    return null;
  }

  return actionKey;
}

/**
 * Robust mapping from MaterialPresetName to ActionKey with validation
 */
function mapMaterialPresetToActionKey(
  presetName: MaterialPresetName
): ActionKey | null {
  const materialKeyMap: Record<MaterialPresetName, ActionKey> = {
    Default: "default_material",
    Wireframe: "wireframe",
    Glass: "glass",
  };

  const actionKey = materialKeyMap[presetName];

  if (!actionKey) {
    console.error(
      `📊 Tracking: Missing mapping for material preset "${presetName}". ` +
        `Please add it to materialKeyMap in selectionBuilder.tsx and to ActionKey type in radialMenuUtils.ts`
    );
    return null;
  }

  return actionKey;
}

const ColorPickerProps: Omit<
  RadialButtonsItem,
  "type" | "Component" | "layers"
> = {
  offset: 20,

  layersGap: 8,
  buttonsGap: 8,

  buttonSize: 40,
};

const buildColorPicker = (selected: GameEntity[]): RadialSection | null => {
  const colorOptions = [
    { name: "Red", value: "#ff3333" },
    { name: "Orange", value: "#ffa500" },
    { name: "Yellow", value: "#ffff00" },
    { name: "Green", value: "#33cc33" },
    { name: "Cyan", value: "#00ffff" },
    { name: "Blue", value: "#3399ff" },
    { name: "Purple", value: "#9932cc" },
    { name: "Magenta", value: "#ff00ff" },
    { name: "Pink", value: "#ff69b4" },
    { name: "Brown", value: "#a52a2a" },
    { name: "White", value: "#ffffff" },
    { name: "Gray", value: "#a9a9a9" },
    { name: "Black", value: "#000000" },
  ];

  const primitives = selected.filter(
    (entity) => entity instanceof PrimitiveEntity
  );

  if (!primitives.length) {
    return null;
  }

  const state = proxy({
    selectedColor: "#" + primitives[0].getColor().toString(16),
  });

  const onCleanup = () => {
    primitives.forEach((entity) => {
      if (entity) {
        entity.revertColorPreview();
      }
    });
  };

  const checkmarkAngle = 45;
  const checkmarkShift = 0.45;
  const checkmarkPos = {
    x: checkmarkShift * Math.sin(checkmarkAngle * MathUtils.DEG2RAD),
    y: checkmarkShift * Math.cos(checkmarkAngle * MathUtils.DEG2RAD),
  };

  return {
    icon: <PaletteIcon />,
    title: "Color",

    reduceMenuOpacity: true,
    menuOpacityOutside: 0.25,

    item: radial.buttons({
      layers: [6, 7],
      onUnmount: onCleanup,
      onMobileInteractionFinish: onCleanup,
      Component: ({ itemIndex }) => {
        const { selectedColor } = useSnapshot(state);
        const isSelected = selectedColor === colorOptions[itemIndex].value;
        const size = 40;

        return (
          <div
            className={cn(
              "relative w-full h-full flex justify-center items-center border-solid border-2 rounded-full",
              isSelected
                ? "border-nilo-fill-primary"
                : "border-nilo-fill-secondary hover:border-nilo-neutral-white active:border-nilo-text-secondary"
            )}
            style={{
              backgroundColor: colorOptions[itemIndex].value,
            }}
            onPointerEnter={() => {
              primitives.forEach((entity) => {
                if (entity) {
                  // entity.previewColor(colorOptions[itemIndex].value);
                  entity.previewColor(
                    Number("0x" + colorOptions[itemIndex].value.slice(1))
                  );
                }
              });
            }}
            onPointerUp={() => {
              // Track color selection with robust mapping
              const colorName = colorOptions[itemIndex].name.toLowerCase();
              const actionKey = mapColorNameToActionKey(colorName);
              if (actionKey) {
                trackCoreBuilderAction(
                  getMenuScope(selected),
                  "edit",
                  actionKey,
                  "color"
                );
              }

              state.selectedColor = colorOptions[itemIndex].value;
              const transaction =
                primitives.length > 1
                  ? beginTransaction("selected-entities-change-color")
                  : null;
              primitives.forEach((entity) => {
                if (entity) {
                  runCommands(
                    new SetEntityColorCommand(
                      entity.id,
                      Number("0x" + colorOptions[itemIndex].value.slice(1))
                    )
                  );
                }
              });
              transaction?.end();
            }}
          >
            <span
              className="bg-nilo-fill-primary"
              style={{
                display: isSelected ? "block" : "none",
                width: 14,
                height: 14,
                borderRadius: "50%",
                color: "#212121",
                // fontSize: markSize,

                position: "absolute",
                left: size / 2 + size * checkmarkPos.x,
                top: size / 2 - size * checkmarkPos.y,
                transform: "translate(-50%, -50%)",
              }}
            >
              <CheckIcon width="95%" height="95%" strokeWidth={3} />
            </span>
          </div>
        );
      },

      ...ColorPickerProps,
    }),
  };
};

const buildMaterialPicker = (selected: GameEntity[]): RadialSection | null => {
  const hasPrimitiveOrMesh = selected.some(
    (entity) =>
      entity instanceof PrimitiveEntity || entity instanceof MeshEntity
  );

  if (!hasPrimitiveOrMesh) {
    return null;
  }

  const materialIcons: Record<MaterialPresetName, ReactNode> = {
    Default: <MaterialIcon />,
    Glass: <MaterialGlassIcon />,
    Wireframe: <MaterialWireframeIcon />,
  };

  const items = MATERIAL_PRESET_NAMES.map((presetName) => {
    const item: ButtonsItem = {
      icon: materialIcons[presetName],
      tooltip: presetName,

      onPointerEnter: () => materialPresetPreview(selected, presetName),
      onPointerUp: () => {
        // Track material selection with robust mapping
        const actionKey = mapMaterialPresetToActionKey(presetName);
        if (actionKey) {
          trackCoreBuilderAction(
            getMenuScope(selected),
            "edit",
            actionKey,
            "material"
          );
        }
        materialPresetCommit(selected, presetName);
      },
    };

    return item;
  });

  return {
    icon: <MaterialIcon />,
    title: "Material",

    reduceMenuOpacity: true,
    menuOpacityOutside: 0.25,

    item: radial.buttonsSet({
      layers: [items],
      closeOnClick: true,
      onUnmount: () => materialPresetRevert(selected),
      onMobileInteractionFinish: () => materialPresetRevert(selected),
    }),
  };
};

const buildEffect = (selected: GameEntity[]): RadialSection | null => {
  const allParticles = selected.filter(
    (entity) => entity instanceof ParticleEntity
  );

  if (allParticles.length === 0) {
    return null;
  }

  if (allParticles.length !== selected.length) {
    return null; // TODO: Do we really want to show particle editor when mixed stuff is selected?
  }

  return {
    icon: <FlameIcon />,
    title: "Effect editor",
    onClick: () => {
      trackCoreBuilderAction(getMenuScope(selected), "edit", "effect_editor");
      const panelId = UIManager.instance.addPanel({
        floating: false,
        visible: true,
        entityId: allParticles[0].id,
        screenOffset: {
          x: 0,
          y: 10,
        },
        positioning: PanelPositioning.BOTTOM_LEFT,
        content: ParticleEditorPanel(allParticles),
      });
      //Hide panel when user clicks on the scene
      Client.userEntity.OnSceneClick.once((_) => {
        UIManager.instance.removePanel(panelId);
      });
    },
  };
};

const buildPhysics = (selected: GameEntity[]): RadialSection | null => {
  const sampleEntity = selected.find(isEntityWithPhysics);
  if (!sampleEntity) {
    //// None found? We don't add the [Physics] menu item then
    return null;
  }

  const sampleEntityBodyCtrl = selected
    .map((e) => e.getPhysicsBodyControl())
    .find(Boolean);

  if (!sampleEntityBodyCtrl) return null;

  //// Add option to toggle shapeType asis/convexHull
  const toggleShapeType = sampleEntityBodyCtrl.getPhysicsOptions().shapeType;

  const isStatic = sampleEntityBodyCtrl.getPhysicsOptions().isStatic;
  const isConvexHull = toggleShapeType === "convexHull";
  const canBeDynamic = canShapeTypeBeDynamic(toggleShapeType);

  const sampleEntityData = Client.world.entityData(sampleEntity.id);
  const hasTrigger = sampleEntityData?.hasComponent(TriggerComponent) ?? false;

  const items = [
    {
      icon: hasTrigger ? <LocateOffIcon /> : <LocateIcon />,
      tooltip: hasTrigger ? "Remove Trigger" : "Make Trigger",
      onClick: () => {
        trackCoreBuilderAction(
          getMenuScope(selected),
          "edit",
          hasTrigger ? "remove_trigger" : "make_trigger"
        );
        Client.room.batch(() => {
          selected.forEach((entity) => {
            if (
              !(
                entity instanceof MeshEntity ||
                entity instanceof PrimitiveEntity
              )
            ) {
              return;
            }
            entity.triggerSetup(!hasTrigger);
          });
        });
      },
    },
    {
      icon: isConvexHull ? <BoxesIcon /> : <BoxIcon />,
      tooltip: isConvexHull ? "Set decomposition" : "Set ConvexHull",
      onClick: () => {
        trackCoreBuilderAction(
          getMenuScope(selected),
          "edit",
          isConvexHull ? "set_decomposition" : "set_convex_hull"
        );
        Client.room.batch(() => {
          selected.forEach((entity) =>
            runCommands(
              new SetEntityPhysicsShapeCommand(
                entity.id,
                isConvexHull ? "decomp" : "convexHull"
              )
            )
          );
        });
      },
    },
  ];

  if (canBeDynamic) {
    items.push({
      icon: isStatic ? <LucidePlayIcon /> : <LucidePauseIcon />,
      tooltip: `${isStatic ? "Enable" : "Disable"} Physics`,
      onClick: () => {
        trackCoreBuilderAction(
          getMenuScope(selected),
          "edit",
          isStatic ? "enable_physics" : "disable_physics"
        );
        Client.room.batch(() => {
          selected.forEach((entity) =>
            runCommands(new SetEntityPhysicsFlagsCommand(entity.id, !isStatic))
          );
        });
      },
    });
  } else {
    items.push({
      icon: <LucideBan />,
      tooltip: "Asis shape type cannot be dynamic",
      onClick: () => {
        // Do nothing
      },
    });
  }

  return {
    icon: <PhysicsIcon />,
    title: "Physics",
    item: radial.buttonsSet({
      layers: [items],
      closeOnClick: true,
    }),
  };
};

const buildLock = (selected: GameEntity[]): RadialSection | null => {
  // const sampleValue = selected[0].getLocked();

  return {
    icon: <LockIcon />,
    title: "Lock",
    onClick: () => {
      trackCoreBuilderAction(getMenuScope(selected), "edit", "lock");
      selected.forEach((entity) => {
        entity.setLocked(true);
      });
    },
  };
};

const buildUnlock = (selected: GameEntity[]): RadialSection => {
  return {
    icon: <UnlockIcon />,
    title: "Unlock",
    onClick: () => {
      trackCoreBuilderAction(getMenuScope(selected), "edit", "unlock");
      selected.forEach((entity) => {
        entity.setLocked(false);
      });
    },
  };
};

const buildDuplicate = (selected: GameEntity[]): RadialSection => {
  return {
    icon: <DuplicateIcon />,
    title: "Duplicate",
    onClick: () => {
      trackCoreBuilderAction(getMenuScope(selected), "edit", "duplicate");
      const camera = Client.userEntity.getCamera();
      if (camera) {
        DuplicateControlsInstant.commit(selected, camera);
      }
    },
  };
};

const buildCodeEditor = (entity: GameEntity): RadialSection | null => {
  return {
    icon: <CodeIcon />,
    title: "Code",
    onClick: () => {
      trackCoreBuilderAction(getMenuScope([entity]), "edit", "code");
      Client.openCollaborativeEditor(entity.id);
    },
  };
};

const buildMore = (selected: GameEntity[]): RadialSection => {
  const items = [
    {
      icon: <ExportIcon />,
      tooltip: "Export",
      onClick: () => {
        trackCoreBuilderAction(getMenuScope(selected), "edit", "export");
        Client.userEntity.export(ExportFormat.GLTF, selected);
      },
    },
    {
      icon: <DuplicateIcon />,
      tooltip: "Duplicate",
      onClick: () => {
        trackCoreBuilderAction(
          getMenuScope(selected),
          "edit",
          "duplicate_controls"
        );
        Client.userEntity.setCurrentAction(CurrentAction.DUPLICATE_CONTROLS);
      },
    },
    {
      icon: <TrashCanIcon />,
      tooltip: "Delete",
      onClick: () => {
        trackCoreBuilderAction(getMenuScope(selected), "edit", "delete");
        selectedEntitiesDelete();
      },
    },
    {
      icon: <ResetIcon />,
      tooltip: "Reset Transform",
      onClick: () => {
        trackCoreBuilderAction(
          getMenuScope(selected),
          "edit",
          "reset_transform"
        );
        Client.room.batch(() => {
          selected.forEach(entityTransformReset);
        });
      },
    },
  ];

  return {
    icon: <MoreIcon />,
    title: "More",
    item: radial.buttonsSet({
      layers: [items],
      closeOnClick: true,
    }),
  };
};

const buildOrigin = (selected: GameEntity[]): RadialSection | null => {
  const validEntities = selected.filter((entity) => !checkEntityForRig(entity));
  if (validEntities.length === 0) {
    return null;
  }

  const items = [
    {
      icon: <PivotBottomIcon />,
      tooltip: "Bottom Center",
      onClick: () => {
        trackCoreBuilderAction(
          getMenuScope(selected),
          "edit",
          "set_origin_bottom_center"
        );
        const transaction = beginTransaction("set-origin-type");
        validEntities.forEach((entity) => {
          runCommands([
            new SetEntityOriginTypeCommand(
              entity.id,
              EntityOriginTypes.GeometryBottomCenter
            ),
          ]);
        });
        transaction.end();
      },
    },
    {
      icon: <PivotCenterIcon />,
      tooltip: "Geometry Center",
      onClick: () => {
        trackCoreBuilderAction(
          getMenuScope(selected),
          "edit",
          "set_origin_geometry_center"
        );
        const transaction = beginTransaction("set-origin-type");
        validEntities.forEach((entity) => {
          runCommands([
            new SetEntityOriginTypeCommand(
              entity.id,
              EntityOriginTypes.GeometryCenter
            ),
          ]);
        });
        transaction.end();
      },
    },
    {
      icon: <PivotImportedIcon />,
      tooltip: "Native Origin",
      onClick: () => {
        trackCoreBuilderAction(
          getMenuScope(selected),
          "edit",
          "set_origin_native"
        );
        const transaction = beginTransaction("set-origin-type");
        validEntities.forEach((entity) => {
          runCommands([
            new SetEntityOriginTypeCommand(
              entity.id,
              EntityOriginTypes.NativeOrigin
            ),
          ]);
        });
        transaction.end();
      },
    },
  ];

  return {
    icon: <PivotIcon />,
    title: "Origin",
    item: radial.buttonsSet({
      layers: [items],
      closeOnClick: true,
    }),
  };
};

const buildLOD = (selected: GameEntity[]): RadialSection | null => {
  const nonRigged = selected
    .filter((entity) => entity instanceof MeshEntity)
    .filter((entity) => !checkEntityForRig(entity));

  if (nonRigged.length === 0) {
    return null;
  }

  const PRESETS = [0.02, 0.05, 0.1, 0.25, 0.5, 1];

  return {
    icon: <PrimitiveIcon />,
    title: nonRigged.length > 1 ? `LOD (${nonRigged.length} selected)` : "LOD",

    reduceMenuOpacity: true,
    menuOpacityOutside: 0.25,

    item: radial.slider({
      angle: 70,

      min: 0,
      max: PRESETS.length - 1,
      value: PRESETS.length - 1,
      step: 1,
      stepsVisible: true,

      onChange: (value) => {
        trackCoreBuilderAction(getMenuScope(selected), "edit", "set_lod");
        selectedEntitiesDetailCommit(PRESETS[value]);
      },
    }),
  };
};

const buildControls = (entity: GameEntity): RadialSection | null => {
  if (Client.playerControls.hasActiveControls()) {
    if (Client.playerControls.getCurrentControlledEntity() === entity) {
      return {
        icon: <ControllerIcon />,
        title: "Remove",
        onClick: () => {
          trackCoreBuilderAction(getMenuScope([entity]), "play", "controls");
          Client.playerControls.destroyActivePlayerControls();
          Client.userEntity.deselectAllEntities();
        },
      };
    }
  }

  if (checkEntityForRig(entity)) {
    return {
      icon: <CharacterRunningIcon />,
      title: "Controls",
      onClick: () => {
        trackCoreBuilderAction(getMenuScope([entity]), "play", "controls");
        Client.playerControls.setActivePlayerControlsTypeTo(
          "character",
          entity
        );
        Client.userEntity.deselectAllEntities();
      },
    };
  }

  const isMesh = entity instanceof MeshEntity;
  if (!isMesh) return null;

  return {
    icon: <VehicleIcon />,
    title: "Controls",
    onClick: () => {
      trackCoreBuilderAction(getMenuScope([entity]), "play", "controls");
      Client.playerControls.setActivePlayerControlsTypeTo("hovercar", entity);
      Client.userEntity.deselectAllEntities();
    },
  };
};

const _buildRigCharacter = (
  unrigged: MeshEntity[],
  selectedCount: number
): RadialSection | null => {
  const riggableEntities = unrigged.filter((entity) => entity.isRiggable());

  const count = riggableEntities.length;
  if (count < 1) {
    return null;
  }

  const nonStarted = riggableEntities.filter((entity) => !entity.isRigging());
  if (nonStarted.length === 0) {
    return null;
  }

  return {
    icon: <SetupIcon />,
    title: selectedCount > 1 ? `Rig (${nonStarted.length} selected)` : `Rig`,
    onClick: async () => {
      trackCoreBuilderAction(getMenuScope(unrigged), "edit", "setup_character");
      const promises = nonStarted.map((e) => e.rig());
      await Promise.all(promises).catch((error) => {
        console.error("Error rigging character", error);
      });
    },
  };
};

const _buildRiggedCharacter = (rigged: MeshEntity[]): RadialSection | null => {
  if (rigged.length < 1) {
    return null;
  }

  const items = [
    {
      icon: <PlayIcon />,
      tooltip: "Play animation",
      onClick: () => {
        trackCoreBuilderAction(
          "rigged_entity",
          "play",
          "play_animation",
          "character"
        );
        addPlayAnimationPanel(rigged);
      },
    },
  ];

  if (
    rigged.some((entity) =>
      entity.data().getComponent(AnimatorComponent)?.hasAnimationPlaying()
    )
  ) {
    items.push({
      icon: <PauseIcon />,
      tooltip: "Stop animation",
      onClick: () => {
        trackCoreBuilderAction(
          "rigged_entity",
          "play",
          "stop_animation",
          "character"
        );
        rigged.forEach((entity) => {
          entity.data().removeComponent(AnimationComponent);
          Client.markEntityAsDirty(entity);
        });
      },
    });
  }

  const ragdolls = rigged
    .map((e) => e.data().getComponent(AnimatorComponent)?.getRagdoll())
    .filter(Boolean) as MeshRagdoll[];

  if (ragdolls.length) {
    const sampleRagdoll = ragdolls[0];
    const isEnabled = sampleRagdoll.ragdollState === RagdollState.Ragdoll;

    if (isEnabled) {
      items.push({
        icon: <RagdollIcon />,
        tooltip: "Deactivate Ragdoll",
        onClick: () => {
          trackCoreBuilderAction(
            "rigged_entity",
            "play",
            "active_ragdoll",
            "character"
          );
          ragdolls.forEach((r) => {
            r.setRagdollState(RagdollState.Kinematic);
          });
        },
      });
    } else {
      items.push({
        icon: <RagdollIcon />,
        tooltip: "Activate Ragdoll",
        onClick: () => {
          trackCoreBuilderAction(
            "rigged_entity",
            "play",
            "active_ragdoll",
            "character"
          );
          ragdolls.forEach((r) => {
            r.setRagdollState(RagdollState.Ragdoll);
          });
        },
      });
    }
  }

  return {
    icon: <AnimationsIcon />,
    title: "Character",
    item: radial.buttonsSet({
      layers: [items],
      closeOnClick: true,
    }),
  };
};

const buildCharacter = (selected: GameEntity[]): RadialSection | null => {
  const riggableEntities = selected.filter(
    (entity) => entity instanceof MeshEntity
  );
  const unriggedEntities = riggableEntities.filter(
    (entity) => !checkEntityForRig(entity)
  );
  const riggedEntities = riggableEntities.filter((entity) =>
    checkEntityForRig(entity)
  );

  if (
    getFeatureFlag("rigCharacterModel") &&
    unriggedEntities.length > 0 &&
    riggedEntities.length === 0
  ) {
    return _buildRigCharacter(unriggedEntities, selected.length);
  } else if (riggedEntities.length > 0) {
    return _buildRiggedCharacter(riggedEntities);
  }

  return null;
};

const buildGameplay = (selected: GameEntity[]): RadialSection => {
  const sample = selected[0];
  const data = Client.world.entityData(sample.id);

  const hasHealth = data?.hasComponent(HealthComponent) ?? false;
  const hasDealDamage = data?.hasComponent(TriggerAreaDamageComponent) ?? false;
  const hasRespawn = data?.hasComponent(RequestRespawnComponent) ?? false;
  const hasRespawnPointTag = data?.hasComponent(RespawnPointTag) ?? false;

  const items = [
    {
      icon: <StarsIcon />,
      tooltip: hasRespawnPointTag
        ? "Remove Respawn Point"
        : "Add Respawn Point",
      onClick: () => {
        trackCoreBuilderAction(
          getMenuScope(selected),
          "edit",
          hasRespawnPointTag ? "remove_respawn_point" : "add_respawn_point"
        );
        Client.room.batch(() => {
          selected.forEach((entity) => {
            const entityData = Client.world.entityData(entity.id);
            if (!entityData) return;

            if (hasRespawnPointTag) {
              // Remove respawn point tag
              entityData.removeComponent(RespawnPointTag);
            } else {
              // Add respawn point tag
              entityData.addComponent(RespawnPointTag, "");
            }
            Client.markEntityAsDirty(entity);
          });
        });
      },
    },
    {
      icon: <StarsIcon />,
      tooltip: hasRespawn ? "Remove Respawn" : "Add Respawn",
      onClick: () => {
        trackCoreBuilderAction(
          getMenuScope(selected),
          "edit",
          hasRespawn ? "remove_respawn" : "add_respawn"
        );
        Client.room.batch(() => {
          selected.forEach((entity) => {
            const entityData = Client.world.entityData(entity.id);
            if (!entityData) return;

            if (hasRespawn) {
              // Remove respawn component
              entityData.removeComponent(RequestRespawnComponent);
            } else {
              // Add respawn component with default value of true
              entityData.addComponent(RequestRespawnComponent, true);
            }
            Client.markEntityAsDirty(entity);
          });
        });
      },
    },
    {
      icon: <StarsIcon />,
      tooltip: hasDealDamage ? "Remove Deal Damage" : "Add Deal Damage",
      onClick: () => {
        trackCoreBuilderAction(
          getMenuScope(selected),
          "edit",
          hasDealDamage ? "remove_deal_damage" : "add_deal_damage"
        );
        Client.room.batch(() => {
          selected.forEach((entity) => {
            const entityData = Client.world.entityData(entity.id);
            if (!entityData) return;

            if (hasDealDamage) {
              // Remove deal damage component
              entityData.removeComponent(TriggerAreaDamageComponent);
            } else {
              // Add deal damage component with default config
              const defaultDamageConfig: TriggerAreaDamageConfig = {
                damagePerSecond: 0,
                damageOnEnter: 100,
                ticksPerSecond: 2,
              };
              entityData.addComponent(
                TriggerAreaDamageComponent,
                defaultDamageConfig
              );
            }
            Client.markEntityAsDirty(entity);
          });
        });
      },
    },
    {
      icon: <StarsIcon />,
      tooltip: hasHealth ? "Remove Health" : "Add Health",
      onClick: () => {
        trackCoreBuilderAction(
          getMenuScope(selected),
          "edit",
          hasHealth ? "remove_health" : "add_health"
        );
        Client.room.batch(() => {
          selected.forEach((entity) => {
            const entityData = Client.world.entityData(entity.id);
            if (!entityData) return;

            if (hasHealth) {
              // Remove health component
              entityData.removeComponent(HealthComponent);
            } else {
              // Add health component with default value of 100
              entityData.addComponent(HealthComponent, {
                health: 100,
                maxHealth: 100,
              });
            }
            Client.markEntityAsDirty(entity);
          });
        });
      },
    },
  ];

  return {
    icon: <StarsIcon />,
    title: "Gameplay",
    item: radial.buttonsSet({
      closeOnClick: true,
      layers: [items],
    }),
  };
};

export const buildSelectionRadialMenu = (selected: GameEntity[]) => {
  const hasMesh = selected.find((e) => e instanceof MeshEntity);
  const hasPrimitive = selected.find((e) => e instanceof PrimitiveEntity);
  const hasLocked = selected.find((e) => e.getLocked());

  const isOnlyPrimitives = hasPrimitive && !hasMesh;
  const isSingle = selected.length === 1;

  let state: RadialSection[];

  if (hasLocked) {
    state = proxy<RadialSection[]>([
      buildUnlock(selected),
      buildDuplicate(selected),
    ] as RadialSection[]);
  } else {
    state = proxy<RadialSection[]>(
      [
        buildEffect(selected),
        isOnlyPrimitives && buildColorPicker(selected),
        buildMaterialPicker(selected),
        buildLock(selected),
        buildPhysics(selected),
        buildLOD(selected),
        buildOrigin(selected),
        buildGameplay(selected),
        buildCharacter(selected),
        isSingle && buildControls(selected[0]),
        isSingle && buildCodeEditor(selected[0]),
        buildMore(selected),
      ].filter(Boolean) as RadialSection[]
    );
  }

  return <RadialMenu sections={state} />;
};
