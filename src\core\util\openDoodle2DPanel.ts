import UIManager from "@/core/util/UIManager";
import {
  Doodle2DPanel,
  LiveCanvasPanel,
} from "@/core/entity/panels/UserEntity";

export const DOODLE_2D_PANEL_ID = "doodle-2d-panel";
export const LIVE_CANVAS_PANEL_ID = "live-canvas-panel";

export function openDoodle2DPanel(): void {
  const hasOpened = UIManager.instance.hasPanel(DOODLE_2D_PANEL_ID);
  if (hasOpened) {
    return;
  }

  if (UIManager.instance.hasPanel(LIVE_CANVAS_PANEL_ID)) {
    UIManager.instance.removePanel(LIVE_CANVAS_PANEL_ID);
  }

  UIManager.instance.addPanel({
    id: DOODLE_2D_PANEL_ID,
    floating: false,
    visible: true,
    style: {
      left: 0,
      bottom: 0,
      zIndex: 10000,
      padding: 16,
      maxWidth: "100%",
      maxHeight: "100%",
    },
    content: Doodle2DPanel(),
  });
}

export function openLiveCanvasPanel(): void {
  const hasOpened = UIManager.instance.hasPanel(LIVE_CANVAS_PANEL_ID);
  if (hasOpened) {
    return;
  }

  if (UIManager.instance.hasPanel(DOODLE_2D_PANEL_ID)) {
    UIManager.instance.removePanel(DOODLE_2D_PANEL_ID);
  }

  UIManager.instance.addPanel({
    id: LIVE_CANVAS_PANEL_ID,
    floating: false,
    visible: true,
    style: {
      left: 0,
      bottom: 0,
      zIndex: 10000,
      padding: 16,
      maxWidth: "100%",
      maxHeight: "100%",
    },
    content: LiveCanvasPanel(),
  });
}

export function closeLiveCanvasPanel(): void {
  UIManager.instance.removePanel(LIVE_CANVAS_PANEL_ID);
}
