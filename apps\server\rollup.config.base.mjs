import { createRequire } from "module";
import { nodeResolve } from "@rollup/plugin-node-resolve";
import commonjs from "@rollup/plugin-commonjs";
import typescript from "@rollup/plugin-typescript";
import json from "@rollup/plugin-json";

const require = createRequire(import.meta.url);
const pkg = require("./package.json");

export default {
  input: "src/index.ts",
  output: {
    file: "dist/index.mjs",
    format: "es",
    sourcemap: true,
  },
  external: Object.keys(pkg.dependencies).filter(
    (dep) => !dep.startsWith("@nilo") // Exclude all dependencies except those starting with @nilo, so TS can compile them
  ),
  plugins: [
    nodeResolve({
      preferBuiltins: true,
      browser: false,
      extensions: [".mjs", ".js", ".json", ".node", ".mts", ".ts"],
      exportConditions: ["default", "module", "import", "node"],
    }),
    json(),
    commonjs(),
    typescript({
      include: [
        "src/**/*.ts",
        "../../packages/logger/**/*.ts",
        "../../packages/network/**/*.ts",
      ],
    }),
  ],
  onLog(level, log, handler) {
    if (log.code === "CIRCULAR_DEPENDENCY") {
      // TODO: Fix circular dependencies or ignore only for specific packages
      return; // Ignore circular dependency warnings
    }

    handler(level, log);
  },
};
